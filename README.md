# 开发准备

使用bigfish 详见 https://bigfish.antfin-inc.com/doc/run-in-3-steps

umijs 详见 https://umijs.org/zh/config/#uglifyjsoptions


# 1、NodeJS

* 请确保你的 NodeJS 版本是 8.5.0 以上。



# 2、 依赖安装

## tnpm

*  请先确保你使用的 tnpm 为最新的版本，如果不是则可能 http://web.npm.alibaba-inc.com/package/tnpm

*  npm install -g tnpm --registry=http://registry.npm.alibaba-inc.com


## bigfish



# 3、 本地开发

## 安装依赖

*   tnpm install

##  本地运行

*  tnpm run dev

##  打包

* npm run build
* 发布 build 下的文件，包括 index.html 和 js


* tnpm run build -- --debug 不对代码进行压缩，并输出构建相关信息，用于调试。

* tnpm run build -- --analyze 构建完成后生成并打开代码分析结果，用于代码大小性能等优化。

* tnpm run build -- --no-sourcemap 不生成 source map 文件，2.0 默认关闭 sourcemap。

* tnpm run build -- --sourcemap 生成 source map 文件，1.0 默认打开 sourcemap。

* tnpm run build -- --assets 只生成 js 和 css，2.0 后废弃，推荐使用标准前端应用。

* tnpm run build -- --no-hash 构建产物不做 hash 处理。


##  发布

*   ./publish.sh 0.0.1


# 4、本地文件

├── dist/                          // 默认的 build 输出目录

├── mock/                          // mock 文件所在目录，基于 express

├── config/

    ├── config.js                  // umi 配置，同 .umirc.js，二选一

└── src/                           // 源码目录，可选

    ├── layouts/index.js           // 全局布局

    ├── pages/                     // 页面目录，里面的文件即路由

        ├── .umi/                  // dev 临时目录，需添加到 .gitignore

        ├── .umi-production/       // build 临时目录，会自动删除

        ├── document.ejs           // HTML 模板

        ├── 404.js                 // 404 页面

        ├── page1.js               // 页面 1，任意命名，导出 react 组件

        ├── page1.test.js          // 用例文件，umi test 会匹配所有 .test.js 和 .e2e.js 结尾的文件

        └── page2.js               // 页面 2，任意命名

    ├── global.css                 // 约定的全局样式文件，自动引入，也可以用 global.less

    ├── global.js                  // 可以在这里加入 polyfill

    ├── app.js                     // 运行时配置文件

├── .umirc.js                      // umi 配置，同 config/config.js，二选一

├── .env                           // 环境变量

└── package.json


# 5、废弃
* npm set registry http://registry.npm.alibaba-inc.com（换成阿里内部源，加快安装速度&使用内部npm库）

* npm run build:dll（开发过程中，将第三方库打包后重复使用）
* npm start（实时编译 + 前端服务器）


