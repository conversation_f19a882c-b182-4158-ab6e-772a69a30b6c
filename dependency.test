/Users/<USER>/git/orange/orangeview
├─┬ UNMET DEPENDENCY @ali/emasd-pro@1.0.14
│ ├─┬ UNMET DEPENDENCY antd@3.13.6
│ │ ├── UNMET DEPENDENCY @ant-design/icons@1.1.16
│ │ ├─┬ UNMET DEPENDENCY @ant-design/icons-react@1.1.2
│ │ │ ├─┬ UNMET DEPENDENCY ant-design-palettes@1.1.3
│ │ │ │ └── tinycolor2@1.4.1
│ │ │ └── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├── UNMET DEPENDENCY array-tree-filter@2.1.0
│ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ ├── UNMET DEPENDENCY create-react-class@15.6.3
│ │ ├─┬ UNMET DEPENDENCY create-react-context@0.2.2
│ │ │ ├── UNMET DEPENDENCY fbjs@0.8.16
│ │ │ └── UNMET DEPENDENCY gud@1.0.0
│ │ ├─┬ UNMET DEPENDENCY css-animation@1.6.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ └── UNMET DEPENDENCY component-classes@1.2.6
│ │ ├─┬ UNMET DEPENDENCY dom-closest@0.2.0
│ │ │ └── UNMET DEPENDENCY dom-matches@2.0.0
│ │ ├── UNMET DEPENDENCY enquire.js@2.1.6
│ │ ├── UNMET DEPENDENCY lodash@4.17.11
│ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ ├── UNMET DEPENDENCY omit.js@1.0.0
│ │ ├─┬ UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├─┬ UNMET DEPENDENCY loose-envify@1.4.0
│ │ │ │ └── js-tokens@3.0.2
│ │ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ │ └── UNMET DEPENDENCY react-is@16.8.3
│ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-calendar@9.10.10
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY moment@2.20.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-cascader@0.17.1
│ │ │ ├── UNMET DEPENDENCY array-tree-filter@2.1.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallow-equal@1.1.0
│ │ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │ │   └── loose-envify@1.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-checkbox@2.1.6
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └── UNMET DEPENDENCY rc-util@4.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-collapse@1.10.3
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├─┬ UNMET DEPENDENCY css-animation@1.6.1
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ └── component-classes@1.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ │ ├── UNMET DEPENDENCY react-is@16.8.3
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├─┬ UNMET DEPENDENCY rc-dialog@7.3.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ │ └── UNMET DEPENDENCY rc-util@4.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-drawer@1.7.7
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └── UNMET DEPENDENCY rc-util@4.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-dropdown@2.4.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-editor-mention@1.1.12
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├─┬ UNMET DEPENDENCY draft-js@0.10.5
│ │ │ │ ├── fbjs@0.8.16
│ │ │ │ ├── immutable@3.7.6
│ │ │ │ └── object-assign@4.1.1
│ │ │ ├── UNMET DEPENDENCY immutable@3.8.2
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ │ └─┬ UNMET DEPENDENCY rc-editor-core@0.8.9
│ │ │   ├── babel-runtime@6.26.0
│ │ │   ├── classnames@2.2.5
│ │ │   ├── draft-js@0.10.5
│ │ │   ├── immutable@3.8.2
│ │ │   ├── lodash@4.17.4
│ │ │   ├── prop-types@15.6.0
│ │ │   └── setimmediate@1.0.5
│ │ ├─┬ UNMET DEPENDENCY rc-form@2.4.2
│ │ │ ├─┬ UNMET DEPENDENCY async-validator@1.8.5
│ │ │ │ └── babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY create-react-class@15.6.3
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├─┬ UNMET DEPENDENCY hoist-non-react-statics@3.3.0
│ │ │ │ └── react-is@16.8.3
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.4
│ │ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │ │   └── loose-envify@1.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-input-number@4.3.9
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY is-negative-zero@2.0.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ └─┬ UNMET DEPENDENCY rmc-feedback@2.0.0
│ │ │   ├── babel-runtime@6.26.0
│ │ │   └── classnames@2.2.5
│ │ ├─┬ UNMET DEPENDENCY rc-menu@7.4.21
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY ismobilejs@0.5.1
│ │ │ ├─┬ UNMET DEPENDENCY mini-store@2.0.0
│ │ │ │ ├── hoist-non-react-statics@2.5.5
│ │ │ │ ├── prop-types@15.6.0
│ │ │ │ ├── react-lifecycles-compat@3.0.4
│ │ │ │ └── shallowequal@1.1.0
│ │ │ ├── UNMET DEPENDENCY mutationobserver-shim@0.3.3
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ └── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ ├─┬ UNMET DEPENDENCY rc-notification@3.3.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ │ └── UNMET DEPENDENCY rc-util@4.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-pagination@1.17.8
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-progress@2.3.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-rate@2.5.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-select@8.8.4
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY component-classes@1.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ │ ├── UNMET DEPENDENCY rc-menu@7.4.21
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │ │   └── loose-envify@1.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-slider@8.6.6
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-tooltip@3.7.3
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │ │   └── loose-envify@1.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-steps@3.3.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.11
│ │ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-switch@1.9.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-table@6.4.3
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY component-classes@1.2.6
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.11
│ │ │ ├── UNMET DEPENDENCY mini-store@2.0.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ │ └── UNMET DEPENDENCY warning@3.0.0
│ │ ├─┬ UNMET DEPENDENCY rc-tabs@9.6.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY create-react-context@0.2.2
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.11
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├─┬ UNMET DEPENDENCY rc-hammerjs@0.6.9
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── hammerjs@2.0.8
│ │ │ │ └── prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ └── UNMET DEPENDENCY warning@3.0.0
│ │ ├─┬ UNMET DEPENDENCY rc-time-picker@3.6.2
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY moment@2.20.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ ├─┬ UNMET DEPENDENCY rc-tooltip@3.7.3
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ ├─┬ UNMET DEPENDENCY rc-tree@1.14.10
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├─┬ UNMET DEPENDENCY rc-animate@3.0.0-rc.6
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── classnames@2.2.5
│ │ │ │ ├── component-classes@1.2.6
│ │ │ │ ├── fbjs@0.8.16
│ │ │ │ ├── prop-types@15.6.0
│ │ │ │ ├── raf@3.4.1
│ │ │ │ ├── rc-util@4.6.0
│ │ │ │ └── react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY warning@3.0.0
│ │ ├─┬ UNMET DEPENDENCY rc-tree-select@2.5.4
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├─┬ UNMET DEPENDENCY rc-animate@3.0.0-rc.6
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── classnames@2.2.5
│ │ │ │ ├── component-classes@1.2.6
│ │ │ │ ├── fbjs@0.8.16
│ │ │ │ ├── prop-types@15.6.0
│ │ │ │ ├── raf@3.4.1
│ │ │ │ ├── rc-util@4.6.0
│ │ │ │ └── react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY rc-tree@1.14.10
│ │ │ ├─┬ UNMET DEPENDENCY rc-trigger@3.0.0-rc.3
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── classnames@2.2.6
│ │ │ │ ├── prop-types@15.6.0
│ │ │ │ ├── raf@3.4.1
│ │ │ │ ├── rc-align@2.4.5
│ │ │ │ ├── rc-animate@3.0.0-rc.6
│ │ │ │ └── rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │ │   └── loose-envify@1.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├─┬ UNMET DEPENDENCY rc-align@2.4.5
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── dom-align@1.8.0
│ │ │ │ ├── prop-types@15.6.0
│ │ │ │ └── rc-util@4.6.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.6.0
│ │ │ └── UNMET DEPENDENCY rc-util@4.6.0
│ │ ├─┬ UNMET DEPENDENCY rc-upload@2.6.3
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │ │   └── loose-envify@1.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-util@4.6.0
│ │ │ ├─┬ UNMET DEPENDENCY add-dom-event-listener@1.1.0
│ │ │ │ └── object-assign@4.1.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └─┬ UNMET DEPENDENCY shallowequal@0.2.2
│ │ │   └── lodash.keys@3.1.2
│ │ ├─┬ UNMET DEPENDENCY react-lazy-load@3.0.13
│ │ │ ├── UNMET DEPENDENCY eventlistener@0.0.1
│ │ │ ├── UNMET DEPENDENCY lodash.debounce@4.0.8
│ │ │ ├── UNMET DEPENDENCY lodash.throttle@4.1.1
│ │ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY react-slick@0.23.2
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ │ ├── UNMET DEPENDENCY enquire.js@2.1.6
│ │ │ ├─┬ UNMET DEPENDENCY json2mq@0.2.0
│ │ │ │ └── string-convert@0.2.1
│ │ │ ├── UNMET DEPENDENCY lodash.debounce@4.0.8
│ │ │ ├── UNMET DEPENDENCY prettier@1.16.4
│ │ │ └── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ ├── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │   └── UNMET DEPENDENCY loose-envify@1.4.0
│ ├─┬ UNMET DEPENDENCY axios@0.16.2
│ │ ├─┬ UNMET DEPENDENCY follow-redirects@1.2.6
│ │ │ └── UNMET DEPENDENCY debug@3.1.0
│ │ └── UNMET DEPENDENCY is-buffer@1.1.6
│ ├─┬ UNMET DEPENDENCY bizcharts@3.1.0
│ │ ├─┬ UNMET DEPENDENCY @antv/g2@3.0.4-beta.2
│ │ │ ├─┬ UNMET DEPENDENCY @antv/g@2.0.6
│ │ │ │ ├── d3-ease@1.0.5
│ │ │ │ ├── d3-interpolate@1.1.6
│ │ │ │ ├── d3-timer@1.0.9
│ │ │ │ ├── gl-matrix@2.3.2
│ │ │ │ ├── lodash@4.17.4
│ │ │ │ └── wolfy87-eventemitter@5.1.0
│ │ │ ├── UNMET DEPENDENCY fecha@2.3.3
│ │ │ ├── UNMET DEPENDENCY gl-matrix@2.4.1
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.4
│ │ │ └── UNMET DEPENDENCY wolfy87-eventemitter@5.2.6
│ │ ├─┬ UNMET DEPENDENCY invariant@2.2.2
│ │ │ └── UNMET DEPENDENCY loose-envify@1.3.1
│ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ └─┬ UNMET DEPENDENCY warning@3.0.0
│ │   └── UNMET DEPENDENCY loose-envify@1.3.1
│ ├── UNMET DEPENDENCY classnames@2.2.5
│ ├── UNMET DEPENDENCY codemirror@5.49.2
│ ├── UNMET DEPENDENCY g2@2.3.13
│ ├── UNMET DEPENDENCY g2-plugin-slider@1.2.1
│ ├── UNMET DEPENDENCY moment@2.20.1
│ ├─┬ UNMET DEPENDENCY prop-types@15.6.0
│ │ ├── UNMET DEPENDENCY fbjs@0.8.16
│ │ ├── UNMET DEPENDENCY loose-envify@1.3.1
│ │ └── UNMET DEPENDENCY object-assign@4.1.1
│ ├─┬ UNMET DEPENDENCY react@15.6.2
│ │ ├─┬ UNMET DEPENDENCY create-react-class@15.6.3
│ │ │ ├── UNMET DEPENDENCY fbjs@0.8.16
│ │ │ ├── UNMET DEPENDENCY loose-envify@1.3.1
│ │ │ └── UNMET DEPENDENCY object-assign@4.1.1
│ │ ├─┬ UNMET DEPENDENCY fbjs@0.8.16
│ │ │ ├── UNMET DEPENDENCY core-js@1.2.7
│ │ │ ├─┬ UNMET DEPENDENCY isomorphic-fetch@2.2.1
│ │ │ │ ├── node-fetch@1.7.3
│ │ │ │ └── whatwg-fetch@2.0.3
│ │ │ ├── UNMET DEPENDENCY loose-envify@1.3.1
│ │ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ │ ├─┬ UNMET DEPENDENCY promise@7.3.1
│ │ │ │ └── asap@2.0.6
│ │ │ ├── UNMET DEPENDENCY setimmediate@1.0.5
│ │ │ └── UNMET DEPENDENCY ua-parser-js@0.7.17
│ │ ├─┬ UNMET DEPENDENCY loose-envify@1.3.1
│ │ │ └── UNMET DEPENDENCY js-tokens@3.0.2
│ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ ├─┬ UNMET DEPENDENCY react-dom@15.6.2
│ │ ├── UNMET DEPENDENCY fbjs@0.8.16
│ │ ├── UNMET DEPENDENCY loose-envify@1.3.1
│ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ ├─┬ UNMET DEPENDENCY react-fullpage@0.1.19
│ │ ├─┬ UNMET DEPENDENCY babel-preset-stage-2@6.24.1
│ │ │ ├── UNMET DEPENDENCY babel-plugin-syntax-dynamic-import@6.18.0
│ │ │ ├─┬ UNMET DEPENDENCY babel-plugin-transform-class-properties@6.24.1
│ │ │ │ ├── babel-helper-function-name@6.24.1
│ │ │ │ ├── babel-plugin-syntax-class-properties@6.13.0
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ └── babel-template@6.26.0
│ │ │ ├─┬ UNMET DEPENDENCY babel-plugin-transform-decorators@6.24.1
│ │ │ │ ├── babel-helper-explode-class@6.24.1
│ │ │ │ ├── babel-plugin-syntax-decorators@6.13.0
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── babel-template@6.26.0
│ │ │ │ └── babel-types@6.26.0
│ │ │ └─┬ UNMET DEPENDENCY babel-preset-stage-3@6.24.1
│ │ │   ├── babel-plugin-syntax-trailing-function-commas@6.22.0
│ │ │   ├── babel-plugin-transform-async-generator-functions@6.24.1
│ │ │   ├── babel-plugin-transform-async-to-generator@6.24.1
│ │ │   ├── babel-plugin-transform-exponentiation-operator@6.24.1
│ │ │   └── babel-plugin-transform-object-rest-spread@6.26.0
│ │ ├─┬ UNMET DEPENDENCY react@16.4.2
│ │ │ ├── UNMET DEPENDENCY fbjs@0.8.16
│ │ │ ├── UNMET DEPENDENCY loose-envify@1.3.1
│ │ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ │ └─┬ UNMET DEPENDENCY react-dom@16.4.2
│ │   ├── UNMET DEPENDENCY fbjs@0.8.16
│ │   ├── UNMET DEPENDENCY loose-envify@1.3.1
│ │   ├── UNMET DEPENDENCY object-assign@4.1.1
│ │   └── UNMET DEPENDENCY prop-types@15.6.0
│ └── UNMET DEPENDENCY uuid@3.2.1
├─┬ UNMET DEPENDENCY @alipay/bigfish@2.25.0
│ ├─┬ UNMET DEPENDENCY @alipay/bigfish-ui@1.1.0
│ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ ├── is-git-url@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-git-url@1.0.0@is-git-url
│ │ ├─┬ UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ └── UNMET DEPENDENCY minimist@0.0.8
│ │ ├── nprogress@0.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_nprogress@0.2.0@nprogress
│ │ ├─┬ UNMET DEPENDENCY react-beforeunload@2.1.0
│ │ │ └─┬ UNMET DEPENDENCY prop-types@15.7.2
│ │ │   ├── loose-envify@1.4.0
│ │ │   ├── object-assign@4.1.1
│ │ │   └── react-is@16.8.3
│ │ ├─┬ UNMET DEPENDENCY runscript@1.4.0
│ │ │ ├─┬ UNMET DEPENDENCY debug@2.6.9
│ │ │ │ └── ms@2.0.0
│ │ │ └── is-type-of@1.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-type-of@1.2.1@is-type-of
│ │ ├── slash2@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_slash2@2.0.0@slash2
│ │ └── valid-url@1.0.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_valid-url@1.0.9@valid-url
│ ├─┬ UNMET DEPENDENCY @alipay/swift-reportor@1.2.9
│ │ ├─┬ @alipay/basement@2.19.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_basement@2.19.0@@alipay/basement
│ │ │ ├── @ali/secure_identity_login_module@1.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ali_secure_identity_login_module@1.1.1@@ali/secure_identity_login_module
│ │ │ ├── co@4.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_co@4.6.0@co deduped
│ │ │ ├─┬ UNMET DEPENDENCY compressing@1.5.0
│ │ │ │ ├── flushwritable@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_flushwritable@1.0.0@flushwritable
│ │ │ │ ├── get-ready@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_get-ready@1.0.0@get-ready
│ │ │ │ ├── iconv-lite@0.5.0
│ │ │ │ ├── mkdirp@0.5.1
│ │ │ │ ├── pump@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_pump@3.0.0@pump deduped
│ │ │ │ ├── streamifier@0.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_streamifier@0.1.1@streamifier
│ │ │ │ ├── tar-stream@1.6.2
│ │ │ │ ├── yauzl@2.10.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_yauzl@2.10.0@yauzl
│ │ │ │ └── yazl@2.5.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_yazl@2.5.1@yazl deduped
│ │ │ ├── UNMET DEPENDENCY debug@3.1.0
│ │ │ ├── UNMET DEPENDENCY depd@1.1.2
│ │ │ ├── humanize-ms@1.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_humanize-ms@1.2.1@humanize-ms deduped
│ │ │ ├── is@3.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is@3.3.0@is
│ │ │ ├─┬ UNMET DEPENDENCY md5@2.2.1
│ │ │ │ ├── charenc@0.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_charenc@0.0.2@charenc
│ │ │ │ ├── crypt@0.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_crypt@0.0.2@crypt
│ │ │ │ └── is-buffer@1.1.6
│ │ │ ├── mz@2.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz@2.7.0@mz
│ │ │ ├── mz-modules@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz-modules@2.1.0@mz-modules
│ │ │ ├─┬ UNMET DEPENDENCY tar-stream@1.6.2
│ │ │ │ ├── bl@1.2.2
│ │ │ │ ├── buffer-alloc@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_buffer-alloc@1.2.0@buffer-alloc
│ │ │ │ ├── end-of-stream@1.4.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_end-of-stream@1.4.4@end-of-stream deduped
│ │ │ │ ├── fs-constants@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_fs-constants@1.0.0@fs-constants
│ │ │ │ ├── readable-stream@2.3.6
│ │ │ │ ├── to-buffer@1.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_to-buffer@1.1.1@to-buffer
│ │ │ │ └── xtend@4.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_xtend@4.0.2@xtend
│ │ │ ├── UNMET DEPENDENCY urllib@2.34.1
│ │ │ ├── UNMET DEPENDENCY utility@1.16.3
│ │ │ └── UNMET DEPENDENCY uuid@3.3.3
│ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ ├─┬ UNMET DEPENDENCY ansi-styles@3.2.1
│ │ │ │ └── color-convert@1.9.3
│ │ │ ├── UNMET DEPENDENCY escape-string-regexp@1.0.5
│ │ │ └─┬ UNMET DEPENDENCY supports-color@5.5.0
│ │ │   └── has-flag@3.0.0
│ │ ├── co@4.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_co@4.6.0@co
│ │ ├── UNMET DEPENDENCY commander@2.20.3
│ │ ├── UNMET DEPENDENCY istanbul-lib-coverage@2.0.5
│ │ ├── lodash.pick@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.pick@<EMAIL>
│ │ ├─┬ UNMET DEPENDENCY shelljs@0.8.3
│ │ │ ├── UNMET DEPENDENCY glob@7.1.6
│ │ │ ├── UNMET DEPENDENCY interpret@1.2.0
│ │ │ └── rechoir@0.6.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_rechoir@0.6.2@rechoir
│ │ └── UNMET DEPENDENCY urllib@2.34.1
│ ├─┬ UNMET DEPENDENCY @alipay/umi-plugin-bigfish@2.25.0
│ │ ├── @ali/anima-hd@5.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ali_anima-hd@5.0.1@@ali/anima-hd
│ │ ├─┬ UNMET DEPENDENCY @ali/parrot-tool-must@1.8.10
│ │ │ ├─┬ UNMET DEPENDENCY @ali/intl-universal@0.4.11
│ │ │ │ ├── intl-format-cache@2.2.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_intl-format-cache@2.2.9@intl-format-cache
│ │ │ │ ├── intl-messageformat@2.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_intl-messageformat@2.2.0@intl-messageformat
│ │ │ │ ├── intl-relativeformat@2.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_intl-relativeformat@2.2.0@intl-relativeformat
│ │ │ │ ├── invariant@2.2.2
│ │ │ │ ├── prop-types@15.6.0
│ │ │ │ └── universal-env@0.4.20 -> /Users/<USER>/git/orange/orangeview/node_modules/_universal-env@0.4.20@universal-env
│ │ │ ├── @ali/parrot-medusa@0.0.13 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ali_parrot-medusa@0.0.13@@ali/parrot-medusa
│ │ │ ├── @ali/parrot-sdk-logger@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ali_parrot-sdk-logger@1.0.0@@ali/parrot-sdk-logger
│ │ │ ├─┬ UNMET DEPENDENCY @ali/translate@1.3.7
│ │ │ │ ├── @alicloud/pop-core@1.7.9
│ │ │ │ ├── axios@0.18.1
│ │ │ │ ├── conf@2.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_conf@2.2.0@conf deduped
│ │ │ │ ├── md5@2.2.1
│ │ │ │ ├── query-string@6.9.0
│ │ │ │ └── queue@4.5.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_queue@4.5.1@queue deduped
│ │ │ ├─┬ UNMET DEPENDENCY @babel/core@7.7.7
│ │ │ │ ├── @babel/code-frame@7.5.5
│ │ │ │ ├── @babel/generator@7.7.7
│ │ │ │ ├── @babel/helpers@7.7.4
│ │ │ │ ├── @babel/parser@7.7.7
│ │ │ │ ├── @babel/template@7.7.4
│ │ │ │ ├── @babel/traverse@7.7.4
│ │ │ │ ├── @babel/types@7.7.4
│ │ │ │ ├── convert-source-map@1.7.0
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── json5@2.1.1
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── resolve@1.14.1
│ │ │ │ ├── semver@5.7.1
│ │ │ │ └── source-map@0.5.7
│ │ │ ├─┬ UNMET DEPENDENCY @babel/generator@7.7.7
│ │ │ │ ├── @babel/types@7.7.4
│ │ │ │ ├── jsesc@2.5.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_jsesc@2.5.2@jsesc
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ └── source-map@0.5.7
│ │ │ ├── UNMET DEPENDENCY @babel/parser@7.7.7
│ │ │ ├─┬ UNMET DEPENDENCY @babel/traverse@7.7.4
│ │ │ │ ├── @babel/code-frame@7.5.5
│ │ │ │ ├── @babel/generator@7.7.7
│ │ │ │ ├── @babel/helper-function-name@7.7.4
│ │ │ │ ├── @babel/helper-split-export-declaration@7.7.4
│ │ │ │ ├── @babel/parser@7.7.7
│ │ │ │ ├── @babel/types@7.7.4
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── globals@11.12.0
│ │ │ │ └── lodash@4.17.15
│ │ │ ├─┬ UNMET DEPENDENCY @babel/types@7.7.4
│ │ │ │ ├── esutils@2.0.2
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ └── to-fast-properties@2.0.0
│ │ │ ├── @vue/component-compiler-utils@2.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@vue_component-compiler-utils@2.6.0@@vue/component-compiler-utils
│ │ │ ├─┬ UNMET DEPENDENCY axios@0.18.1
│ │ │ │ ├── follow-redirects@1.5.10
│ │ │ │ └── is-buffer@2.0.4
│ │ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ │ ├── ansi-styles@3.2.1
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ └── supports-color@5.5.0
│ │ │ ├── UNMET DEPENDENCY commander@2.20.3
│ │ │ ├─┬ UNMET DEPENDENCY cross-spawn@6.0.5
│ │ │ │ ├── nice-try@1.0.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_nice-try@1.0.5@nice-try
│ │ │ │ ├── path-key@2.0.1
│ │ │ │ ├── semver@5.7.1
│ │ │ │ ├── shebang-command@1.2.0
│ │ │ │ └── which@1.3.1
│ │ │ ├── find@0.2.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_find@0.2.9@find
│ │ │ ├── UNMET DEPENDENCY form-data@2.5.1
│ │ │ ├── UNMET DEPENDENCY fs-extra@7.0.1
│ │ │ ├─┬ UNMET DEPENDENCY inquirer@6.2.0
│ │ │ │ ├── ansi-escapes@3.2.0
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── cli-cursor@2.1.0
│ │ │ │ ├── cli-width@2.2.0
│ │ │ │ ├── external-editor@3.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_external-editor@3.1.0@external-editor
│ │ │ │ ├── figures@2.0.0
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── mute-stream@0.0.7
│ │ │ │ ├── run-async@2.3.0
│ │ │ │ ├── rxjs@6.5.3
│ │ │ │ ├── string-width@2.1.1
│ │ │ │ ├── strip-ansi@4.0.0
│ │ │ │ └── through@2.3.8 -> /Users/<USER>/git/orange/orangeview/node_modules/_through@2.3.8@through
│ │ │ ├── js-string-escape@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_js-string-escape@1.0.1@js-string-escape
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ │ ├─┬ UNMET DEPENDENCY os-locale@3.1.0
│ │ │ │ ├── execa@1.0.0
│ │ │ │ ├── lcid@2.0.0
│ │ │ │ └── mem@4.3.0
│ │ │ ├── UNMET DEPENDENCY prettier@1.16.4
│ │ │ ├── prettier-eslint@8.8.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_prettier-eslint@8.8.2@prettier-eslint
│ │ │ ├─┬ UNMET DEPENDENCY prop-types@15.7.2
│ │ │ │ ├── loose-envify@1.4.0
│ │ │ │ ├── object-assign@4.1.1
│ │ │ │ └── react-is@16.8.3
│ │ │ ├── qrcode-terminal@0.12.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_qrcode-terminal@0.12.0@qrcode-terminal
│ │ │ ├── UNMET DEPENDENCY query-string@6.9.0
│ │ │ ├── UNMET DEPENDENCY semver@6.3.0
│ │ │ ├─┬ UNMET DEPENDENCY table@5.4.6
│ │ │ │ ├── ajv@6.10.2
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── slice-ansi@2.1.0
│ │ │ │ └── string-width@3.1.0
│ │ │ ├─┬ UNMET DEPENDENCY velocity@0.7.2
│ │ │ │ ├── colorful@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_colorful@2.1.0@colorful
│ │ │ │ ├── commander@2.3.0
│ │ │ │ ├── tracer@0.7.4
│ │ │ │ └── utilx@0.0.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_utilx@0.0.5@utilx
│ │ │ └─┬ UNMET DEPENDENCY vue-template-compiler@2.6.11
│ │ │   ├── de-indent@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_de-indent@1.0.2@de-indent
│ │ │   └── he@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_he@1.2.0@he
│ │ ├── UNMET DEPENDENCY @ali/tracker@4.3.1
│ │ ├─┬ @alipay/eslint-config-bigfish@4.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_eslint-config-bigfish@4.1.1@@alipay/eslint-config-bigfish
│ │ │ ├─┬ UNMET DEPENDENCY @alipay/update-reminder@1.0.7
│ │ │ │ ├── ini@1.3.5
│ │ │ │ ├── semver@5.7.1
│ │ │ │ └── urllib@2.34.1
│ │ │ ├─┬ UNMET DEPENDENCY @umijs/fabric@1.2.12
│ │ │ │ ├── @typescript-eslint/eslint-plugin@1.13.0
│ │ │ │ ├── @typescript-eslint/parser@2.12.0
│ │ │ │ ├── eslint@5.16.0
│ │ │ │ ├── eslint-config-airbnb@17.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-config-airbnb@17.1.1@eslint-config-airbnb deduped
│ │ │ │ ├── eslint-config-airbnb-base@13.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-config-airbnb-base@13.2.0@eslint-config-airbnb-base deduped
│ │ │ │ ├── eslint-config-airbnb-typescript@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-config-airbnb-typescript@4.0.1@eslint-config-airbnb-typescript deduped
│ │ │ │ ├── eslint-config-prettier@4.3.0
│ │ │ │ ├── eslint-formatter-pretty@2.1.1
│ │ │ │ ├── eslint-plugin-babel@5.3.0
│ │ │ │ ├── eslint-plugin-compat@3.3.0
│ │ │ │ ├── eslint-plugin-eslint-comments@3.1.2
│ │ │ │ ├── eslint-plugin-import@2.19.1
│ │ │ │ ├── eslint-plugin-jest@22.21.0
│ │ │ │ ├── eslint-plugin-jsx-a11y@6.2.3
│ │ │ │ ├── eslint-plugin-markdown@1.0.1
│ │ │ │ ├── eslint-plugin-promise@4.2.1
│ │ │ │ ├── eslint-plugin-react@7.17.0
│ │ │ │ ├── eslint-plugin-react-hooks@1.7.0
│ │ │ │ ├── eslint-plugin-unicorn@8.0.2
│ │ │ │ ├── stylelint@10.1.0
│ │ │ │ ├── stylelint-config-css-modules@1.5.0
│ │ │ │ ├── stylelint-config-prettier@5.3.0
│ │ │ │ ├── stylelint-config-rational-order@0.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_stylelint-config-rational-order@0.1.2@stylelint-config-rational-order
│ │ │ │ ├── stylelint-config-standard@18.3.0
│ │ │ │ ├── stylelint-declaration-block-no-ignored-properties@2.2.0
│ │ │ │ └── stylelint-order@3.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_stylelint-order@3.1.1@stylelint-order
│ │ │ ├── UNMET DEPENDENCY eslint@5.16.0
│ │ │ ├─┬ UNMET DEPENDENCY stylelint@9.10.1
│ │ │ │ ├── autoprefixer@9.7.3
│ │ │ │ ├── balanced-match@1.0.0
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── cosmiconfig@5.2.1
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── execall@1.0.0
│ │ │ │ ├── file-entry-cache@4.0.0
│ │ │ │ ├── get-stdin@6.0.0
│ │ │ │ ├── global-modules@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global-modules@2.0.0@global-modules
│ │ │ │ ├── globby@9.2.0
│ │ │ │ ├── globjoin@0.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_globjoin@0.1.4@globjoin
│ │ │ │ ├── html-tags@2.0.0
│ │ │ │ ├── ignore@5.1.4
│ │ │ │ ├── import-lazy@3.1.0
│ │ │ │ ├── imurmurhash@0.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_imurmurhash@0.1.4@imurmurhash deduped
│ │ │ │ ├── known-css-properties@0.11.0
│ │ │ │ ├── leven@2.1.0
│ │ │ │ ├── lodash@4.17.4
│ │ │ │ ├── log-symbols@2.2.0
│ │ │ │ ├── mathml-tag-names@2.1.1
│ │ │ │ ├── meow@5.0.0
│ │ │ │ ├── micromatch@3.1.10
│ │ │ │ ├── normalize-selector@0.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_normalize-selector@0.2.0@normalize-selector
│ │ │ │ ├── pify@4.0.1
│ │ │ │ ├── postcss@7.0.25
│ │ │ │ ├── postcss-html@0.36.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-html@0.36.0@postcss-html
│ │ │ │ ├── postcss-jsx@0.36.3
│ │ │ │ ├── postcss-less@3.1.4
│ │ │ │ ├── postcss-markdown@0.36.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-markdown@0.36.0@postcss-markdown
│ │ │ │ ├── postcss-media-query-parser@0.2.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-media-query-parser@0.2.3@postcss-media-query-parser
│ │ │ │ ├── postcss-reporter@6.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-reporter@6.0.1@postcss-reporter
│ │ │ │ ├── postcss-resolve-nested-selector@0.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-resolve-nested-selector@0.1.1@postcss-resolve-nested-selector
│ │ │ │ ├── postcss-safe-parser@4.0.1
│ │ │ │ ├── postcss-sass@0.3.5
│ │ │ │ ├── postcss-scss@2.0.0
│ │ │ │ ├── postcss-selector-parser@3.1.1
│ │ │ │ ├── postcss-syntax@0.36.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-syntax@0.36.2@postcss-syntax
│ │ │ │ ├── postcss-value-parser@3.3.1
│ │ │ │ ├── resolve-from@4.0.0
│ │ │ │ ├── signal-exit@3.0.2
│ │ │ │ ├── slash@2.0.0
│ │ │ │ ├── specificity@0.4.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_specificity@0.4.1@specificity
│ │ │ │ ├── string-width@3.1.0
│ │ │ │ ├── style-search@0.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_style-search@0.1.0@style-search
│ │ │ │ ├── sugarss@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_sugarss@2.0.0@sugarss
│ │ │ │ ├── svg-tags@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_svg-tags@1.0.0@svg-tags
│ │ │ │ └── table@5.4.6
│ │ │ ├── UNMET DEPENDENCY stylelint-config-prettier@4.0.0
│ │ │ └─┬ UNMET DEPENDENCY stylelint-config-standard@18.3.0
│ │ │   └── stylelint-config-recommended@2.2.0
│ │ ├─┬ @alipay/h5data@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_h5data@1.3.0@@alipay/h5data
│ │ │ ├── UNMET DEPENDENCY @alipay/render-util@1.4.3
│ │ │ ├── UNMET DEPENDENCY isomorphic-fetch@2.2.1
│ │ │ └─┬ UNMET DEPENDENCY react@16.12.0
│ │ │   ├── loose-envify@1.3.1
│ │ │   ├── object-assign@4.1.1
│ │ │   └── prop-types@15.7.2
│ │ ├─┬ @alipay/multiple-hybrid-build@1.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_multiple-hybrid-build@1.1.4@@alipay/multiple-hybrid-build
│ │ │ ├── @alipay/hybrid-zip@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_hybrid-zip@1.1.2@@alipay/hybrid-zip
│ │ │ ├── @alipay/portal-hybrid-builder@1.2.10 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_portal-hybrid-builder@1.2.10@@alipay/portal-hybrid-builder
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY commander@2.20.3
│ │ │ ├── fs-extra@0.30.0
│ │ │ ├── UNMET DEPENDENCY jsonfile@2.4.0
│ │ │ └── spm-log@0.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_spm-log@0.1.3@spm-log
│ │ ├─┬ UNMET DEPENDENCY @alipay/oneapi-sdk@1.1.2
│ │ │ ├── @alipay/stage-bag@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_stage-bag@1.0.1@@alipay/stage-bag
│ │ │ ├── is-type-of@1.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-type-of@1.2.1@is-type-of deduped
│ │ │ ├── mz@2.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz@2.7.0@mz deduped
│ │ │ ├── mz-modules@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz-modules@2.1.0@mz-modules deduped
│ │ │ ├─┬ UNMET DEPENDENCY ts-morph@5.0.0
│ │ │ │ ├── @dsherret/to-absolute-glob@2.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_@dsherret_to-absolute-glob@2.0.2@@dsherret/to-absolute-glob
│ │ │ │ ├── @ts-morph/common@0.1.1
│ │ │ │ └── code-block-writer@10.1.0
│ │ │ ├── UNMET DEPENDENCY urllib@2.34.1
│ │ │ └── zlogger@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_zlogger@2.1.0@zlogger
│ │ ├─┬ UNMET DEPENDENCY @alipay/oneapi-ui@1.1.9
│ │ │ ├─┬ UNMET DEPENDENCY @ant-design/icons@4.0.0-alpha.11
│ │ │ │ ├── @ant-design/colors@3.2.2
│ │ │ │ ├── @ant-design/icons-svg@4.0.0-alpha.0
│ │ │ │ ├── classnames@2.2.6
│ │ │ │ ├── insert-css@2.0.0
│ │ │ │ └── rc-util@4.16.3
│ │ │ ├─┬ UNMET DEPENDENCY @umijs/hooks@1.5.1
│ │ │ │ ├── intersection-observer@0.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_intersection-observer@0.7.0@intersection-observer deduped
│ │ │ │ ├── lodash.isequal@4.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.isequal@<EMAIL>
│ │ │ │ ├── resize-observer-polyfill@1.5.1
│ │ │ │ └── screenfull@5.0.0
│ │ │ ├─┬ UNMET DEPENDENCY antd@4.0.0-alpha.10
│ │ │ │ ├── @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context deduped
│ │ │ │ ├── @ant-design/icons@4.0.0-alpha.11
│ │ │ │ ├── @ant-design/icons-svg@4.0.0-alpha.0
│ │ │ │ ├── @types/react-slick@0.23.4
│ │ │ │ ├── array-tree-filter@2.1.0
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── classnames@2.2.6
│ │ │ │ ├── copy-to-clipboard@3.2.0
│ │ │ │ ├── css-animation@1.6.1
│ │ │ │ ├── dom-closest@0.2.0
│ │ │ │ ├── dom-scroll-into-view@1.2.1
│ │ │ │ ├── enquire.js@2.1.6
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── moment@2.24.0
│ │ │ │ ├── omit.js@1.0.2
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── raf@3.4.1
│ │ │ │ ├── rc-animate@2.10.2
│ │ │ │ ├── rc-calendar@9.15.8
│ │ │ │ ├── rc-cascader@1.0.0-alpha.0
│ │ │ │ ├── rc-checkbox@2.1.6
│ │ │ │ ├── rc-collapse@1.11.7
│ │ │ │ ├── rc-dialog@7.5.14
│ │ │ │ ├── rc-drawer@3.0.2
│ │ │ │ ├── rc-dropdown@3.0.0-alpha.0
│ │ │ │ ├── rc-field-form@0.0.0-alpha.25
│ │ │ │ ├── rc-input-number@4.5.3
│ │ │ │ ├── rc-mentions@0.4.1
│ │ │ │ ├── rc-menu@8.0.0-alpha.7
│ │ │ │ ├── rc-notification@3.3.1
│ │ │ │ ├── rc-pagination@1.20.11
│ │ │ │ ├── rc-progress@2.5.2
│ │ │ │ ├── rc-rate@2.5.0
│ │ │ │ ├── rc-resize-observer@0.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_rc-resize-observer@0.1.3@rc-resize-observer deduped
│ │ │ │ ├── rc-select@10.0.0-alpha.34
│ │ │ │ ├── rc-slider@8.7.1
│ │ │ │ ├── rc-steps@3.5.0
│ │ │ │ ├── rc-switch@1.9.0
│ │ │ │ ├── rc-table@7.0.0-alpha.21
│ │ │ │ ├── rc-tabs@9.7.0
│ │ │ │ ├── rc-time-picker@4.0.0-alpha.2
│ │ │ │ ├── rc-tooltip@3.7.3
│ │ │ │ ├── rc-tree@3.0.0-alpha.37
│ │ │ │ ├── rc-tree-select@3.0.0-alpha.5
│ │ │ │ ├── rc-trigger@2.6.2
│ │ │ │ ├── rc-upload@2.9.4
│ │ │ │ ├── rc-util@4.16.3
│ │ │ │ ├── rc-virtual-list@0.0.0-alpha.28
│ │ │ │ ├── react-lazy-load@3.0.13
│ │ │ │ ├── react-lifecycles-compat@3.0.4
│ │ │ │ ├── react-slick@0.25.2
│ │ │ │ ├── resize-observer-polyfill@1.5.1
│ │ │ │ ├── shallowequal@1.1.0
│ │ │ │ └── warning@4.0.3
│ │ │ ├── UNMET DEPENDENCY brace@0.11.1
│ │ │ ├── UNMET DEPENDENCY dayjs@1.8.18
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.4
│ │ │ ├── UNMET DEPENDENCY openapi-types@1.3.5
│ │ │ ├─┬ UNMET DEPENDENCY react-ace@7.0.5
│ │ │ │ ├── brace@0.11.1
│ │ │ │ ├── diff-match-patch@1.0.4
│ │ │ │ ├── lodash.get@4.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.get@<EMAIL>
│ │ │ │ ├── lodash.isequal@4.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.isequal@<EMAIL> deduped
│ │ │ │ └── prop-types@15.7.2
│ │ │ ├─┬ UNMET DEPENDENCY react-dnd@9.5.1
│ │ │ │ ├── @types/hoist-non-react-statics@3.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@types_hoist-non-react-statics@3.3.1@@types/hoist-non-react-statics
│ │ │ │ ├── @types/shallowequal@1.1.1
│ │ │ │ ├── dnd-core@9.5.1
│ │ │ │ ├── hoist-non-react-statics@3.3.0
│ │ │ │ └── shallowequal@1.1.0
│ │ │ ├─┬ UNMET DEPENDENCY react-dnd-html5-backend@9.5.1
│ │ │ │ └── dnd-core@9.5.1
│ │ │ ├─┬ UNMET DEPENDENCY react-json-view@1.19.1
│ │ │ │ ├── flux@3.1.3
│ │ │ │ ├── react-base16-styling@0.6.0
│ │ │ │ ├── react-lifecycles-compat@3.0.4
│ │ │ │ └── react-textarea-autosize@6.1.0
│ │ │ ├─┬ UNMET DEPENDENCY react-use@13.12.2
│ │ │ │ ├── @xobotyi/scrollbar-width@1.5.0
│ │ │ │ ├── copy-to-clipboard@3.2.0
│ │ │ │ ├── nano-css@5.2.1
│ │ │ │ ├── react-fast-compare@2.0.4
│ │ │ │ ├── resize-observer-polyfill@1.5.1
│ │ │ │ ├── screenfull@5.0.0
│ │ │ │ ├── set-harmonic-interval@1.0.1
│ │ │ │ ├── throttle-debounce@2.1.0
│ │ │ │ ├── ts-easing@0.2.0
│ │ │ │ └── tslib@1.10.0
│ │ │ └── styled-components@4.4.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_styled-components@4.4.1@styled-components
│ │ ├── UNMET DEPENDENCY @alipay/oneconsole-integration-helper@1.1.7
│ │ ├─┬ UNMET DEPENDENCY @alipay/onex-sdk@1.4.6
│ │ │ ├── UNMET DEPENDENCY @alipay/onex-types@1.5.5
│ │ │ ├── js-cookie@2.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_js-cookie@2.2.1@js-cookie deduped
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.4
│ │ │ └── UNMET DEPENDENCY umi-request@1.2.15
│ │ ├─┬ @alipay/rc-tracert@4.0.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_rc-tracert@4.0.7@@alipay/rc-tracert
│ │ │ ├── hoist-non-react-statics@1.2.0
│ │ │ └── UNMET DEPENDENCY react-router-config@1.0.0-beta.4
│ │ ├─┬ @alipay/sff-codegen@2.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_sff-codegen@2.2.0@@alipay/sff-codegen
│ │ │ ├─┬ UNMET DEPENDENCY @alipay/sff-utils@2.12.0
│ │ │ │ ├── @ali/jar2proxy@4.1.3
│ │ │ │ ├── @alipay/oneapi-sdk@1.1.2
│ │ │ │ ├── @alipay/sff-oneapi-generator@1.1.3
│ │ │ │ ├── @alipay/sff-spec@1.6.1
│ │ │ │ ├── @alipay/stage-bag@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_stage-bag@1.0.1@@alipay/stage-bag deduped
│ │ │ │ ├── cpy@7.3.0
│ │ │ │ ├── crequire@1.8.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_crequire@1.8.1@crequire deduped
│ │ │ │ ├── deep-object-diff@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_deep-object-diff@1.1.0@deep-object-diff
│ │ │ │ ├── extend2@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_extend2@1.0.0@extend2 deduped
│ │ │ │ ├── fast-deep-equal@2.0.1
│ │ │ │ ├── globby@10.0.1
│ │ │ │ ├── js-yaml@3.13.1
│ │ │ │ ├── mz@2.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz@2.7.0@mz deduped
│ │ │ │ ├── mz-modules@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz-modules@2.1.0@mz-modules deduped
│ │ │ │ ├── resolve@1.14.1
│ │ │ │ ├── runscript@1.4.0
│ │ │ │ ├── searequire@1.5.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_searequire@1.5.3@searequire
│ │ │ │ ├── tar@4.4.13
│ │ │ │ ├── ts-morph@3.1.3
│ │ │ │ ├── typescript@3.7.4
│ │ │ │ ├── urllib@2.34.1
│ │ │ │ ├── urlsafe-base64@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_urlsafe-base64@1.0.0@urlsafe-base64
│ │ │ │ ├── uuid@3.3.3
│ │ │ │ └── zlogger@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_zlogger@2.1.0@zlogger deduped
│ │ │ ├─┬ UNMET DEPENDENCY chokidar@3.3.1
│ │ │ │ ├── anymatch@3.1.1
│ │ │ │ ├── braces@3.0.2
│ │ │ │ ├── fsevents@2.1.2
│ │ │ │ ├── glob-parent@5.1.0
│ │ │ │ ├── is-binary-path@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-binary-path@2.1.0@is-binary-path
│ │ │ │ ├── is-glob@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-glob@4.0.1@is-glob deduped
│ │ │ │ ├── normalize-path@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_normalize-path@3.0.0@normalize-path
│ │ │ │ └── readdirp@3.3.0
│ │ │ └── common-bin-plus@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_common-bin-plus@1.0.2@common-bin-plus
│ │ ├─┬ UNMET DEPENDENCY @alipay/site2render@1.49.3
│ │ │ ├── @alipay/basement@2.19.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_basement@2.19.0@@alipay/basement deduped
│ │ │ ├── egg-bin@1.11.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_egg-bin@1.11.1@egg-bin
│ │ │ ├─┬ UNMET DEPENDENCY fs-extra@0.30.0
│ │ │ │ ├── graceful-fs@4.2.3
│ │ │ │ ├── jsonfile@2.4.0
│ │ │ │ ├── klaw@1.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_klaw@1.3.1@klaw
│ │ │ │ ├── path-is-absolute@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-is-absolute@1.0.1@path-is-absolute deduped
│ │ │ │ └── rimraf@2.7.1
│ │ │ ├── UNMET DEPENDENCY minimist@1.2.0
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ ├─┬ UNMET DEPENDENCY path-to-regexp@1.8.0
│ │ │ │ └── isarray@0.0.1
│ │ │ ├── ready@0.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_ready@0.1.1@ready
│ │ │ ├── UNMET DEPENDENCY rimraf@2.7.1
│ │ │ ├── UNMET DEPENDENCY urllib@2.34.1
│ │ │ └── UNMET DEPENDENCY utility@1.16.3
│ │ ├─┬ UNMET DEPENDENCY @alipay/source-map-webpack-plugin@2.0.9
│ │ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ │ └── ms@2.1.2
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ ├── UNMET DEPENDENCY rimraf@2.7.1
│ │ │ └── worker-farm@1.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_worker-farm@1.7.0@worker-farm
│ │ ├─┬ @alipay/tracert@1.1.16 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_tracert@1.1.16@@alipay/tracert
│ │ │ ├── @ali/anima-contain-status@1.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ali_anima-contain-status@1.0.4@@ali/anima-contain-status
│ │ │ ├── UNMET DEPENDENCY @ali/anima-detect@1.0.1
│ │ │ ├── @ali/anima-router@1.0.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ali_anima-router@1.0.5@@ali/anima-router
│ │ │ └── name-storage@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_name-storage@1.3.0@name-storage
│ │ ├── @alipay/umi-plugin-antdcloud-nav@2.21.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_umi-plugin-antdcloud-nav@2.21.1@@alipay/umi-plugin-antdcloud-nav
│ │ ├─┬ @alipay/umi-request-middlewares@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_@alipay_umi-request-middlewares@1.0.3@@alipay/umi-request-middlewares
│ │ │ └── UNMET DEPENDENCY umi-request@1.2.15
│ │ ├─┬ UNMET DEPENDENCY @alipay/umi-ui-basement@1.1.0
│ │ │ └── valid-url@1.0.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_valid-url@1.0.9@valid-url deduped
│ │ ├─┬ UNMET DEPENDENCY @alipay/xmas-fastclick@1.0.7
│ │ │ ├── @ali/tap-scroll@0.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ali_tap-scroll@0.5.0@@ali/tap-scroll
│ │ │ └── fastclick@1.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_fastclick@1.0.6@fastclick
│ │ ├─┬ UNMET DEPENDENCY @alipay/yunfengdie-api-sdk@1.0.10
│ │ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ │ └── ms@2.1.2
│ │ │ ├── url@0.11.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_url@0.11.0@url
│ │ │ └── UNMET DEPENDENCY urllib@2.34.1
│ │ ├─┬ UNMET DEPENDENCY @umijs/plugin-prerender@1.5.8
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ └─┬ UNMET DEPENDENCY umi-server@1.1.8
│ │ │   ├── cheerio@1.0.0-rc.3
│ │ │   ├── debug@4.1.1
│ │ │   └── ssr-polyfill@1.0.0
│ │ ├─┬ UNMET DEPENDENCY @umijs/plugin-qiankun@1.3.4
│ │ │ ├── UNMET DEPENDENCY @babel/runtime@7.7.7
│ │ │ └─┬ UNMET DEPENDENCY qiankun@1.3.9
│ │ │   ├── @babel/runtime@7.7.7
│ │ │   ├── import-html-entry@1.3.8
│ │ │   ├── lodash@4.17.15
│ │ │   ├── single-spa@4.4.2
│ │ │   └── tslib@1.10.0
│ │ ├── babel-plugin-add-module-require@1.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-add-module-require@1.0.4@babel-plugin-add-module-require
│ │ ├─┬ UNMET DEPENDENCY babel-plugin-import@1.13.0
│ │ │ ├─┬ UNMET DEPENDENCY @babel/helper-module-imports@7.7.4
│ │ │ │ └── @babel/types@7.7.4
│ │ │ └── UNMET DEPENDENCY @babel/runtime@7.7.7
│ │ ├─┬ UNMET DEPENDENCY bigbrother-webpack-plugin@1.7.4
│ │ │ ├── bfj@6.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_bfj@6.1.2@bfj
│ │ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ │ └── ms@2.1.2
│ │ │ ├── UNMET DEPENDENCY ini@1.3.5
│ │ │ ├── is-ali-env@0.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-ali-env@0.1.4@is-ali-env
│ │ │ ├─┬ UNMET DEPENDENCY shameimaru@1.0.2
│ │ │ │ ├── algorithmjs@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_algorithmjs@1.0.0@algorithmjs
│ │ │ │ ├── mz@2.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz@2.7.0@mz deduped
│ │ │ │ ├── npm-package-arg@6.1.1
│ │ │ │ └── uuid@3.3.3
│ │ │ ├── UNMET DEPENDENCY tslib@1.10.0
│ │ │ └── UNMET DEPENDENCY uuid@3.3.3
│ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ ├─┬ UNMET DEPENDENCY ansi-styles@3.2.1
│ │ │ │ └── color-convert@1.9.3
│ │ │ ├── UNMET DEPENDENCY escape-string-regexp@1.0.5
│ │ │ └─┬ UNMET DEPENDENCY supports-color@5.5.0
│ │ │   └── has-flag@3.0.0
│ │ ├─┬ UNMET DEPENDENCY cheerio@1.0.0-rc.3
│ │ │ ├─┬ UNMET DEPENDENCY css-select@1.2.0
│ │ │ │ ├── boolbase@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_boolbase@1.0.0@boolbase
│ │ │ │ ├── css-what@2.1.3
│ │ │ │ ├── domutils@1.5.1
│ │ │ │ └── nth-check@1.0.2
│ │ │ ├─┬ UNMET DEPENDENCY dom-serializer@0.1.1
│ │ │ │ ├── domelementtype@1.3.1
│ │ │ │ └── entities@1.1.2
│ │ │ ├── UNMET DEPENDENCY entities@1.1.2
│ │ │ ├─┬ UNMET DEPENDENCY htmlparser2@3.10.1
│ │ │ │ ├── domelementtype@1.3.1
│ │ │ │ ├── domhandler@2.4.2
│ │ │ │ ├── domutils@1.7.0
│ │ │ │ ├── entities@1.1.2
│ │ │ │ ├── inherits@2.0.3
│ │ │ │ └── readable-stream@3.4.0
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.4
│ │ │ └─┬ UNMET DEPENDENCY parse5@3.0.3
│ │ │   └── @types/node@12.12.21
│ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ ├── co@4.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_co@4.6.0@co deduped
│ │ ├─┬ co-fs@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_co-fs@1.2.0@co-fs
│ │ │ ├── co-from-stream@0.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_co-from-stream@0.0.0@co-from-stream
│ │ │ └── thunkify@0.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_thunkify@0.0.1@thunkify
│ │ ├─┬ UNMET DEPENDENCY debug@3.1.0
│ │ │ └── UNMET DEPENDENCY ms@2.0.0
│ │ ├─┬ detect-port@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_detect-port@1.3.0@detect-port
│ │ │ ├── address@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_address@1.1.2@address
│ │ │ └── debug@2.6.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_debug@2.6.9@debug
│ │ ├─┬ UNMET DEPENDENCY dva@2.5.0-beta.2
│ │ │ ├── UNMET DEPENDENCY @babel/runtime@7.7.7
│ │ │ ├── UNMET DEPENDENCY @types/isomorphic-fetch@0.0.34
│ │ │ ├─┬ UNMET DEPENDENCY @types/react-router-dom@4.3.5
│ │ │ │ ├── @types/history@4.7.3
│ │ │ │ ├── @types/react@16.9.16
│ │ │ │ └── @types/react-router@5.1.3
│ │ │ ├─┬ UNMET DEPENDENCY @types/react-router-redux@5.0.18
│ │ │ │ ├── @types/history@4.7.3
│ │ │ │ ├── @types/react@16.9.16
│ │ │ │ ├── @types/react-router@5.1.3
│ │ │ │ └── redux@4.0.4
│ │ │ ├─┬ UNMET DEPENDENCY dva-core@1.5.0-beta.2
│ │ │ │ ├── @babel/runtime@7.7.7
│ │ │ │ ├── flatten@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_flatten@1.0.3@flatten
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── invariant@2.2.2
│ │ │ │ ├── is-plain-object@2.0.4
│ │ │ │ ├── redux@3.7.2
│ │ │ │ ├── redux-saga@0.16.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_redux-saga@0.16.2@redux-saga
│ │ │ │ └── warning@3.0.0
│ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global
│ │ │ ├── history@4.10.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_history@4.10.1@history
│ │ │ ├── UNMET DEPENDENCY invariant@2.2.2
│ │ │ ├── UNMET DEPENDENCY isomorphic-fetch@2.2.1
│ │ │ ├─┬ UNMET DEPENDENCY react-redux@5.1.2
│ │ │ │ ├── @babel/runtime@7.7.7
│ │ │ │ ├── hoist-non-react-statics@3.3.0
│ │ │ │ ├── invariant@2.2.4
│ │ │ │ ├── loose-envify@1.3.1
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react-is@16.8.3
│ │ │ │ └── react-lifecycles-compat@3.0.4
│ │ │ ├─┬ UNMET DEPENDENCY react-router-dom@4.3.1
│ │ │ │ ├── history@4.10.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_history@4.10.1@history deduped
│ │ │ │ ├── invariant@2.2.4
│ │ │ │ ├── loose-envify@1.3.1
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react-router@4.3.1
│ │ │ │ └── warning@4.0.3
│ │ │ ├── react-router-redux@5.0.0-alpha.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-router-redux@5.0.0-alpha.9@react-router-redux deduped
│ │ │ └─┬ UNMET DEPENDENCY redux@3.7.2
│ │ │   ├── lodash@4.17.4
│ │ │   ├── lodash-es@4.17.15
│ │ │   ├── loose-envify@1.3.1
│ │ │   └── symbol-observable@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_symbol-observable@1.2.0@symbol-observable deduped
│ │ ├─┬ UNMET DEPENDENCY eslint@5.16.0
│ │ │ ├─┬ UNMET DEPENDENCY @babel/code-frame@7.5.5
│ │ │ │ └── @babel/highlight@7.5.0
│ │ │ ├─┬ UNMET DEPENDENCY ajv@6.10.2
│ │ │ │ ├── fast-deep-equal@2.0.1
│ │ │ │ ├── fast-json-stable-stringify@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_fast-json-stable-stringify@2.1.0@fast-json-stable-stringify
│ │ │ │ ├── json-schema-traverse@0.4.1
│ │ │ │ └── uri-js@4.2.2
│ │ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ │ ├── ansi-styles@3.2.1
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ └── supports-color@5.5.0
│ │ │ ├── UNMET DEPENDENCY cross-spawn@6.0.5
│ │ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ │ └── ms@2.1.2
│ │ │ ├─┬ UNMET DEPENDENCY doctrine@3.0.0
│ │ │ │ └── esutils@2.0.2
│ │ │ ├─┬ UNMET DEPENDENCY eslint-scope@4.0.3
│ │ │ │ ├── esrecurse@4.2.1
│ │ │ │ └── estraverse@4.3.0
│ │ │ ├─┬ UNMET DEPENDENCY eslint-utils@1.4.3
│ │ │ │ └── eslint-visitor-keys@1.1.0
│ │ │ ├── UNMET DEPENDENCY eslint-visitor-keys@1.1.0
│ │ │ ├─┬ UNMET DEPENDENCY espree@5.0.1
│ │ │ │ ├── acorn@6.4.0
│ │ │ │ ├── acorn-jsx@5.1.0
│ │ │ │ └── eslint-visitor-keys@1.1.0
│ │ │ ├─┬ UNMET DEPENDENCY esquery@1.0.1
│ │ │ │ └── estraverse@4.3.0
│ │ │ ├── UNMET DEPENDENCY esutils@2.0.2
│ │ │ ├─┬ UNMET DEPENDENCY file-entry-cache@5.0.1
│ │ │ │ └── flat-cache@2.0.1
│ │ │ ├── functional-red-black-tree@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_functional-red-black-tree@1.0.1@functional-red-black-tree
│ │ │ ├── UNMET DEPENDENCY glob@7.1.6
│ │ │ ├── UNMET DEPENDENCY globals@11.12.0
│ │ │ ├── UNMET DEPENDENCY ignore@4.0.6
│ │ │ ├─┬ UNMET DEPENDENCY import-fresh@3.2.1
│ │ │ │ ├── parent-module@1.0.1
│ │ │ │ └── resolve-from@4.0.0
│ │ │ ├── imurmurhash@0.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_imurmurhash@0.1.4@imurmurhash
│ │ │ ├─┬ UNMET DEPENDENCY inquirer@6.5.2
│ │ │ │ ├── ansi-escapes@3.2.0
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── cli-cursor@2.1.0
│ │ │ │ ├── cli-width@2.2.0
│ │ │ │ ├── external-editor@3.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_external-editor@3.1.0@external-editor deduped
│ │ │ │ ├── figures@2.0.0
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── mute-stream@0.0.7
│ │ │ │ ├── run-async@2.3.0
│ │ │ │ ├── rxjs@6.5.3
│ │ │ │ ├── string-width@2.1.1
│ │ │ │ ├── strip-ansi@5.2.0
│ │ │ │ └── through@2.3.8 -> /Users/<USER>/git/orange/orangeview/node_modules/_through@2.3.8@through deduped
│ │ │ ├── UNMET DEPENDENCY js-yaml@3.13.1
│ │ │ ├── json-stable-stringify-without-jsonify@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_json-stable-stringify-without-jsonify@1.0.1@json-stable-stringify-without-jsonify
│ │ │ ├─┬ UNMET DEPENDENCY levn@0.3.0
│ │ │ │ ├── prelude-ls@1.1.2
│ │ │ │ └── type-check@0.3.2
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── minimatch@3.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_minimatch@3.0.4@minimatch
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ ├── natural-compare@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_natural-compare@1.4.0@natural-compare
│ │ │ ├─┬ UNMET DEPENDENCY optionator@0.8.3
│ │ │ │ ├── deep-is@0.1.3
│ │ │ │ ├── fast-levenshtein@2.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_fast-levenshtein@2.0.6@fast-levenshtein
│ │ │ │ ├── levn@0.3.0
│ │ │ │ ├── prelude-ls@1.1.2
│ │ │ │ ├── type-check@0.3.2
│ │ │ │ └── word-wrap@1.2.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_word-wrap@1.2.3@word-wrap
│ │ │ ├── path-is-inside@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-is-inside@1.0.2@path-is-inside
│ │ │ ├── progress@2.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_progress@2.0.3@progress
│ │ │ ├── UNMET DEPENDENCY regexpp@2.0.1
│ │ │ ├── UNMET DEPENDENCY semver@5.7.1
│ │ │ ├─┬ UNMET DEPENDENCY strip-ansi@4.0.0
│ │ │ │ └── ansi-regex@3.0.0
│ │ │ ├── UNMET DEPENDENCY strip-json-comments@2.0.1
│ │ │ ├── UNMET DEPENDENCY table@5.4.6
│ │ │ └── text-table@0.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_text-table@0.2.0@text-table
│ │ ├─┬ UNMET DEPENDENCY eslint-config-egg@7.5.1
│ │ │ ├─┬ UNMET DEPENDENCY @typescript-eslint/eslint-plugin@2.12.0
│ │ │ │ ├── @typescript-eslint/experimental-utils@2.12.0
│ │ │ │ ├── eslint-utils@1.4.3
│ │ │ │ ├── functional-red-black-tree@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_functional-red-black-tree@1.0.1@functional-red-black-tree deduped
│ │ │ │ ├── regexpp@3.0.0
│ │ │ │ └── tsutils@3.17.1
│ │ │ ├─┬ UNMET DEPENDENCY @typescript-eslint/parser@2.12.0
│ │ │ │ ├── @types/eslint-visitor-keys@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@types_eslint-visitor-keys@1.0.0@@types/eslint-visitor-keys
│ │ │ │ ├── @typescript-eslint/experimental-utils@2.12.0
│ │ │ │ ├── @typescript-eslint/typescript-estree@2.12.0
│ │ │ │ └── eslint-visitor-keys@1.1.0
│ │ │ ├─┬ UNMET DEPENDENCY babel-eslint@8.2.6
│ │ │ │ ├── @babel/code-frame@7.0.0-beta.44
│ │ │ │ ├── @babel/traverse@7.0.0-beta.44
│ │ │ │ ├── @babel/types@7.0.0-beta.44
│ │ │ │ ├── babylon@7.0.0-beta.44
│ │ │ │ ├── eslint-scope@3.7.1
│ │ │ │ └── eslint-visitor-keys@1.1.0
│ │ │ ├── UNMET DEPENDENCY eslint-plugin-eggache@1.0.0
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-import@2.19.1
│ │ │ │ ├── array-includes@3.1.1
│ │ │ │ ├── array.prototype.flat@1.2.3
│ │ │ │ ├── contains-path@0.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_contains-path@0.1.0@contains-path
│ │ │ │ ├── debug@2.6.9
│ │ │ │ ├── doctrine@1.5.0
│ │ │ │ ├── eslint-import-resolver-node@0.3.2
│ │ │ │ ├── eslint-module-utils@2.5.0
│ │ │ │ ├── has@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_has@1.0.3@has
│ │ │ │ ├── minimatch@3.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_minimatch@3.0.4@minimatch deduped
│ │ │ │ ├── object.values@1.1.1
│ │ │ │ ├── read-pkg-up@2.0.0
│ │ │ │ └── resolve@1.14.1
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-jsdoc@4.8.4
│ │ │ │ ├── comment-parser@0.5.5
│ │ │ │ ├── jsdoctypeparser@3.1.0
│ │ │ │ └── lodash@4.17.15
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-jsx-a11y@6.2.3
│ │ │ │ ├── @babel/runtime@7.7.7
│ │ │ │ ├── aria-query@3.0.0
│ │ │ │ ├── array-includes@3.1.1
│ │ │ │ ├── ast-types-flow@0.0.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_ast-types-flow@0.0.7@ast-types-flow
│ │ │ │ ├── axobject-query@2.1.1
│ │ │ │ ├── damerau-levenshtein@1.0.5
│ │ │ │ ├── emoji-regex@7.0.3
│ │ │ │ ├── has@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_has@1.0.3@has deduped
│ │ │ │ └── jsx-ast-utils@2.2.3
│ │ │ └─┬ UNMET DEPENDENCY eslint-plugin-react@7.17.0
│ │ │   ├── array-includes@3.1.1
│ │ │   ├── doctrine@2.1.0
│ │ │   ├── eslint-plugin-eslint-plugin@2.1.0
│ │ │   ├── has@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_has@1.0.3@has deduped
│ │ │   ├── jsx-ast-utils@2.2.3
│ │ │   ├── object.entries@1.1.1
│ │ │   ├── object.fromentries@2.0.2
│ │ │   ├── object.values@1.1.1
│ │ │   ├── prop-types@15.7.2
│ │ │   └── resolve@1.14.1
│ │ ├─┬ UNMET DEPENDENCY execa@1.0.0
│ │ │ ├── UNMET DEPENDENCY cross-spawn@6.0.5
│ │ │ ├─┬ UNMET DEPENDENCY get-stream@4.1.0
│ │ │ │ └── pump@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_pump@3.0.0@pump deduped
│ │ │ ├── UNMET DEPENDENCY is-stream@1.1.0
│ │ │ ├─┬ UNMET DEPENDENCY npm-run-path@2.0.2
│ │ │ │ └── path-key@2.0.1
│ │ │ ├── p-finally@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_p-finally@1.0.0@p-finally
│ │ │ ├── UNMET DEPENDENCY signal-exit@3.0.2
│ │ │ └── strip-eof@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_strip-eof@1.0.0@strip-eof
│ │ ├─┬ express@4.17.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_express@4.17.1@express
│ │ │ ├── accepts@1.3.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_accepts@1.3.7@accepts
│ │ │ ├── UNMET DEPENDENCY array-flatten@1.1.1
│ │ │ ├── body-parser@1.19.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_body-parser@1.19.0@body-parser
│ │ │ ├── content-disposition@0.5.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_content-disposition@0.5.3@content-disposition
│ │ │ ├── content-type@1.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_content-type@1.0.4@content-type deduped
│ │ │ ├── cookie@0.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_cookie@0.4.0@cookie
│ │ │ ├── cookie-signature@1.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_cookie-signature@1.0.6@cookie-signature
│ │ │ ├── debug@2.6.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_debug@2.6.9@debug
│ │ │ ├── UNMET DEPENDENCY depd@1.1.2
│ │ │ ├── encodeurl@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_encodeurl@1.0.2@encodeurl
│ │ │ ├── escape-html@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_escape-html@1.0.3@escape-html
│ │ │ ├── etag@1.8.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_etag@1.8.1@etag
│ │ │ ├── finalhandler@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_finalhandler@1.1.2@finalhandler
│ │ │ ├── fresh@0.5.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_fresh@0.5.2@fresh
│ │ │ ├── merge-descriptors@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_merge-descriptors@1.0.1@merge-descriptors
│ │ │ ├── methods@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_methods@1.1.2@methods
│ │ │ ├── on-finished@2.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_on-finished@2.3.0@on-finished
│ │ │ ├── parseurl@1.3.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_parseurl@1.3.3@parseurl
│ │ │ ├── path-to-regexp@0.1.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-to-regexp@0.1.7@path-to-regexp
│ │ │ ├─┬ UNMET DEPENDENCY proxy-addr@2.0.5
│ │ │ │ ├── forwarded@0.1.2
│ │ │ │ └── ipaddr.js@1.9.0
│ │ │ ├── qs@6.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_qs@6.7.0@qs
│ │ │ ├── range-parser@1.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_range-parser@1.2.1@range-parser
│ │ │ ├── safe-buffer@5.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_safe-buffer@5.1.2@safe-buffer
│ │ │ ├── send@0.17.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_send@0.17.1@send
│ │ │ ├── serve-static@1.14.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_serve-static@1.14.1@serve-static
│ │ │ ├── setprototypeof@1.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_setprototypeof@1.1.1@setprototypeof
│ │ │ ├── statuses@1.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_statuses@1.5.0@statuses deduped
│ │ │ ├── type-is@1.6.18 -> /Users/<USER>/git/orange/orangeview/node_modules/_type-is@1.6.18@type-is
│ │ │ ├── utils-merge@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_utils-merge@1.0.1@utils-merge
│ │ │ └── vary@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_vary@1.1.2@vary
│ │ ├─┬ UNMET DEPENDENCY father@2.29.1
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-decorators@7.4.4
│ │ │ │ ├── @babel/helper-create-class-features-plugin@7.7.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-decorators@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-a11y@5.2.8
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/api@5.2.8
│ │ │ │ ├── @storybook/client-logger@5.2.8
│ │ │ │ ├── @storybook/components@5.2.8
│ │ │ │ ├── @storybook/core-events@5.2.8
│ │ │ │ ├── @storybook/theming@5.2.8
│ │ │ │ ├── axe-core@3.4.1
│ │ │ │ ├── common-tags@1.8.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_common-tags@1.8.0@common-tags deduped
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── hoist-non-react-statics@3.3.0
│ │ │ │ ├── memoizerific@1.11.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_memoizerific@1.11.3@memoizerific
│ │ │ │ ├── react@16.12.0
│ │ │ │ ├── react-redux@7.1.3
│ │ │ │ ├── react-sizeme@2.6.10
│ │ │ │ ├── redux@4.0.4
│ │ │ │ └── util-deprecate@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_util-deprecate@1.0.2@util-deprecate
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-actions@5.2.8
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/api@5.2.8
│ │ │ │ ├── @storybook/client-api@5.2.8
│ │ │ │ ├── @storybook/components@5.2.8
│ │ │ │ ├── @storybook/core-events@5.2.8
│ │ │ │ ├── @storybook/theming@5.2.8
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── fast-deep-equal@2.0.1
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── polished@3.4.2
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react@16.12.0
│ │ │ │ ├── react-inspector@3.0.2
│ │ │ │ └── uuid@3.3.3
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-console@1.2.1
│ │ │ │ └── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-knobs@5.2.8
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/api@5.2.8
│ │ │ │ ├── @storybook/client-api@5.2.8
│ │ │ │ ├── @storybook/components@5.2.8
│ │ │ │ ├── @storybook/core-events@5.2.8
│ │ │ │ ├── @storybook/theming@5.2.8
│ │ │ │ ├── @types/react-color@3.0.1
│ │ │ │ ├── copy-to-clipboard@3.2.0
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── escape-html@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_escape-html@1.0.3@escape-html deduped
│ │ │ │ ├── fast-deep-equal@2.0.1
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── qs@6.9.1
│ │ │ │ ├── react-color@2.17.3
│ │ │ │ ├── react-lifecycles-compat@3.0.4
│ │ │ │ └── react-select@3.0.8
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-links@5.2.8
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/core-events@5.2.8
│ │ │ │ ├── @storybook/router@5.2.8
│ │ │ │ ├── common-tags@1.8.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_common-tags@1.8.0@common-tags deduped
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ └── qs@6.9.1
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-notes@5.2.8
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/api@5.2.8
│ │ │ │ ├── @storybook/client-logger@5.2.8
│ │ │ │ ├── @storybook/components@5.2.8
│ │ │ │ ├── @storybook/core-events@5.2.8
│ │ │ │ ├── @storybook/router@5.2.8
│ │ │ │ ├── @storybook/theming@5.2.8
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── markdown-to-jsx@6.10.3
│ │ │ │ ├── memoizerific@1.11.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_memoizerific@1.11.3@memoizerific deduped
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ └── util-deprecate@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_util-deprecate@1.0.2@util-deprecate deduped
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-options@5.2.8
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ └── util-deprecate@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_util-deprecate@1.0.2@util-deprecate deduped
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addon-storysource@5.2.8
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/components@5.2.8
│ │ │ │ ├── @storybook/router@5.2.8
│ │ │ │ ├── @storybook/source-loader@5.2.8
│ │ │ │ ├── @storybook/theming@5.2.8
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── estraverse@4.3.0
│ │ │ │ ├── loader-utils@1.2.3
│ │ │ │ ├── prettier@1.16.4
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react-syntax-highlighter@8.1.0
│ │ │ │ ├── regenerator-runtime@0.12.1
│ │ │ │ └── util-deprecate@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_util-deprecate@1.0.2@util-deprecate deduped
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/api@5.2.8
│ │ │ │ ├── @storybook/channels@5.2.8
│ │ │ │ ├── @storybook/client-logger@5.2.8
│ │ │ │ ├── @storybook/core-events@5.2.8
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ └── util-deprecate@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_util-deprecate@1.0.2@util-deprecate deduped
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/cli@5.2.8
│ │ │ │ ├── @babel/core@7.7.7
│ │ │ │ ├── @babel/preset-env@7.7.7
│ │ │ │ ├── @storybook/codemod@5.2.8
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── commander@2.20.3
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── cross-spawn@6.0.5
│ │ │ │ ├── didyoumean@1.2.1
│ │ │ │ ├── envinfo@7.5.0
│ │ │ │ ├── esm@3.2.25 -> /Users/<USER>/git/orange/orangeview/node_modules/_esm@3.2.25@esm
│ │ │ │ ├── fs-extra@8.1.0
│ │ │ │ ├── inquirer@6.2.0
│ │ │ │ ├── jscodeshift@0.6.4
│ │ │ │ ├── json5@2.1.1
│ │ │ │ ├── pkg-add-deps@0.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_pkg-add-deps@0.1.0@pkg-add-deps
│ │ │ │ ├── semver@6.3.0
│ │ │ │ ├── shelljs@0.8.3
│ │ │ │ └── update-notifier@3.0.0
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/react@5.2.8
│ │ │ │ ├── @babel/plugin-transform-react-constant-elements@7.7.4
│ │ │ │ ├── @babel/preset-flow@7.7.4
│ │ │ │ ├── @babel/preset-react@7.7.4
│ │ │ │ ├── @storybook/addons@5.2.8
│ │ │ │ ├── @storybook/core@5.2.8
│ │ │ │ ├── @storybook/node-logger@5.2.8
│ │ │ │ ├── @svgr/webpack@4.3.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_@svgr_webpack@4.3.3@@svgr/webpack
│ │ │ │ ├── @types/webpack-env@1.14.1
│ │ │ │ ├── babel-plugin-add-react-displayname@0.0.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-add-react-displayname@0.0.5@babel-plugin-add-react-displayname
│ │ │ │ ├── babel-plugin-named-asset-import@0.3.5
│ │ │ │ ├── babel-plugin-react-docgen@3.2.0
│ │ │ │ ├── babel-preset-react-app@9.1.0
│ │ │ │ ├── common-tags@1.8.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_common-tags@1.8.0@common-tags deduped
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── mini-css-extract-plugin@0.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mini-css-extract-plugin@0.7.0@mini-css-extract-plugin
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react-dev-utils@9.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-dev-utils@9.1.0@react-dev-utils
│ │ │ │ ├── regenerator-runtime@0.12.1
│ │ │ │ ├── semver@6.3.0
│ │ │ │ └── webpack@4.41.4
│ │ │ ├─┬ UNMET DEPENDENCY @storybook/theming@5.2.8
│ │ │ │ ├── @emotion/core@10.0.22
│ │ │ │ ├── @emotion/styled@10.0.27 -> /Users/<USER>/git/orange/orangeview/node_modules/_@emotion_styled@10.0.27@@emotion/styled
│ │ │ │ ├── @storybook/client-logger@5.2.8
│ │ │ │ ├── common-tags@1.8.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_common-tags@1.8.0@common-tags deduped
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── deep-object-diff@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_deep-object-diff@1.1.0@deep-object-diff deduped
│ │ │ │ ├── emotion-theming@10.0.27 -> /Users/<USER>/git/orange/orangeview/node_modules/_emotion-theming@10.0.27@emotion-theming
│ │ │ │ ├── global@4.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global@4.4.0@global deduped
│ │ │ │ ├── memoizerific@1.11.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_memoizerific@1.11.3@memoizerific deduped
│ │ │ │ ├── polished@3.4.2
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ └── resolve-from@5.0.0
│ │ │ ├─┬ UNMET DEPENDENCY @typescript-eslint/eslint-plugin@1.10.2
│ │ │ │ ├── @typescript-eslint/experimental-utils@1.10.2
│ │ │ │ ├── eslint-utils@1.4.3
│ │ │ │ ├── functional-red-black-tree@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_functional-red-black-tree@1.0.1@functional-red-black-tree deduped
│ │ │ │ ├── regexpp@2.0.1
│ │ │ │ └── tsutils@3.17.1
│ │ │ ├─┬ UNMET DEPENDENCY @typescript-eslint/parser@1.10.2
│ │ │ │ ├── @types/eslint-visitor-keys@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@types_eslint-visitor-keys@1.0.0@@types/eslint-visitor-keys deduped
│ │ │ │ ├── @typescript-eslint/experimental-utils@1.10.2
│ │ │ │ ├── @typescript-eslint/typescript-estree@1.10.2
│ │ │ │ └── eslint-visitor-keys@1.1.0
│ │ │ ├── UNMET DEPENDENCY @umijs/fabric@1.2.12
│ │ │ ├─┬ UNMET DEPENDENCY babel-loader@8.0.6
│ │ │ │ ├── find-cache-dir@2.1.0
│ │ │ │ ├── loader-utils@1.2.3
│ │ │ │ ├── mkdirp@0.5.1
│ │ │ │ └── pify@4.0.1
│ │ │ ├─┬ UNMET DEPENDENCY docz@1.2.0
│ │ │ │ ├── @loadable/component@5.11.0
│ │ │ │ ├── @mdx-js/react@1.5.3
│ │ │ │ ├── @reach/router@1.2.1
│ │ │ │ ├── array-sort@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_array-sort@1.0.0@array-sort
│ │ │ │ ├── capitalize@2.0.1
│ │ │ │ ├── docz-core@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_docz-core@1.2.0@docz-core deduped
│ │ │ │ ├── fast-deep-equal@2.0.1
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── match-sorter@3.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_match-sorter@3.1.1@match-sorter
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react@16.12.0
│ │ │ │ ├── react-dom@16.12.0
│ │ │ │ ├── ulid@2.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_ulid@2.3.0@ulid
│ │ │ │ └── yargs@13.3.0
│ │ │ ├── docz-core@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_docz-core@1.2.0@docz-core
│ │ │ ├── docz-plugin-umi-css@0.14.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_docz-plugin-umi-css@0.14.1@docz-plugin-umi-css
│ │ │ ├── docz-theme-umi@2.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_docz-theme-umi@2.1.1@docz-theme-umi
│ │ │ ├── UNMET DEPENDENCY eslint@5.16.0
│ │ │ ├── eslint-config-airbnb@17.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-config-airbnb@17.1.1@eslint-config-airbnb
│ │ │ ├── eslint-config-airbnb-base@13.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-config-airbnb-base@13.2.0@eslint-config-airbnb-base
│ │ │ ├── eslint-config-airbnb-typescript@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-config-airbnb-typescript@4.0.1@eslint-config-airbnb-typescript
│ │ │ ├── UNMET DEPENDENCY eslint-config-egg@7.5.1
│ │ │ ├─┬ UNMET DEPENDENCY eslint-config-prettier@4.3.0
│ │ │ │ └── get-stdin@6.0.0
│ │ │ ├─┬ UNMET DEPENDENCY eslint-formatter-pretty@2.1.1
│ │ │ │ ├── ansi-escapes@3.2.0
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── eslint-rule-docs@1.1.171
│ │ │ │ ├── log-symbols@2.2.0
│ │ │ │ ├── plur@3.1.1
│ │ │ │ ├── string-width@2.1.1
│ │ │ │ └── supports-hyperlinks@1.0.1
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-babel@5.3.0
│ │ │ │ └── eslint-rule-composer@0.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-rule-composer@0.3.0@eslint-rule-composer
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-compat@3.3.0
│ │ │ │ ├── @babel/runtime@7.7.7
│ │ │ │ ├── ast-metadata-inferer@0.1.1
│ │ │ │ ├── browserslist@4.8.2
│ │ │ │ ├── caniuse-db@1.0.30001016
│ │ │ │ ├── lodash.memoize@4.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.memoize@<EMAIL>
│ │ │ │ ├── mdn-browser-compat-data@0.0.84
│ │ │ │ └── semver@6.3.0
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-eslint-comments@3.1.2
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ └── ignore@5.1.4
│ │ │ ├── UNMET DEPENDENCY eslint-plugin-import@2.19.1
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-jest@22.21.0
│ │ │ │ └── @typescript-eslint/experimental-utils@1.13.0
│ │ │ ├── UNMET DEPENDENCY eslint-plugin-jsx-a11y@6.2.3
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-markdown@1.0.1
│ │ │ │ ├── object-assign@4.1.1
│ │ │ │ ├── remark-parse@5.0.0
│ │ │ │ └── unified@6.2.0
│ │ │ ├── UNMET DEPENDENCY eslint-plugin-promise@4.2.1
│ │ │ ├── UNMET DEPENDENCY eslint-plugin-react@7.17.0
│ │ │ ├─┬ UNMET DEPENDENCY eslint-plugin-unicorn@8.0.2
│ │ │ │ ├── clean-regexp@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_clean-regexp@1.0.0@clean-regexp
│ │ │ │ ├── eslint-ast-utils@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-ast-utils@1.1.0@eslint-ast-utils
│ │ │ │ ├── import-modules@1.1.0
│ │ │ │ ├── lodash.camelcase@4.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.camelcase@<EMAIL>
│ │ │ │ ├── lodash.defaultsdeep@4.6.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.defaultsdeep@<EMAIL>
│ │ │ │ ├── lodash.kebabcase@4.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.kebabcase@<EMAIL>
│ │ │ │ ├── lodash.snakecase@4.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.snakecase@<EMAIL>
│ │ │ │ ├── lodash.topairs@4.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.topairs@<EMAIL>
│ │ │ │ ├── lodash.upperfirst@4.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.upperfirst@<EMAIL>
│ │ │ │ ├── reserved-words@0.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_reserved-words@0.1.2@reserved-words
│ │ │ │ └── safe-regex@2.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_safe-regex@2.1.1@safe-regex
│ │ │ ├─┬ UNMET DEPENDENCY father-build@1.17.1
│ │ │ │ ├── @babel/core@7.4.5
│ │ │ │ ├── @babel/plugin-proposal-class-properties@7.4.4
│ │ │ │ ├── @babel/plugin-proposal-decorators@7.4.4
│ │ │ │ ├── @babel/plugin-proposal-do-expressions@7.2.0
│ │ │ │ ├── @babel/plugin-proposal-export-default-from@7.2.0
│ │ │ │ ├── @babel/plugin-proposal-export-namespace-from@7.2.0
│ │ │ │ ├── @babel/plugin-proposal-nullish-coalescing-operator@7.7.4
│ │ │ │ ├── @babel/plugin-proposal-optional-chaining@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-dynamic-import@7.2.0
│ │ │ │ ├── @babel/plugin-transform-modules-commonjs@7.5.0
│ │ │ │ ├── @babel/plugin-transform-runtime@7.4.4
│ │ │ │ ├── @babel/preset-env@7.4.5
│ │ │ │ ├── @babel/preset-react@7.0.0
│ │ │ │ ├── @babel/preset-typescript@7.3.3
│ │ │ │ ├── @babel/register@7.4.4
│ │ │ │ ├── @svgr/rollup@4.3.3
│ │ │ │ ├── ajv@6.10.0
│ │ │ │ ├── autoprefixer@9.6.0
│ │ │ │ ├── babel-plugin-istanbul@5.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-istanbul@5.2.0@babel-plugin-istanbul
│ │ │ │ ├── babel-plugin-react-require@3.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-react-require@3.1.1@babel-plugin-react-require
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── chokidar@3.3.1
│ │ │ │ ├── glob@7.1.6
│ │ │ │ ├── gulp-if@2.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_gulp-if@2.0.2@gulp-if
│ │ │ │ ├── gulp-less@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_gulp-less@4.0.1@gulp-less
│ │ │ │ ├── gulp-typescript@5.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_gulp-typescript@5.0.1@gulp-typescript
│ │ │ │ ├── less@3.9.0
│ │ │ │ ├── less-plugin-npm-import@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_less-plugin-npm-import@2.1.0@less-plugin-npm-import
│ │ │ │ ├── rimraf@2.6.3
│ │ │ │ ├── rollup@1.27.8
│ │ │ │ ├── rollup-plugin-babel@4.3.3
│ │ │ │ ├── rollup-plugin-commonjs@10.0.0
│ │ │ │ ├── rollup-plugin-inject@3.0.1
│ │ │ │ ├── rollup-plugin-json@4.0.0
│ │ │ │ ├── rollup-plugin-node-resolve@5.0.1
│ │ │ │ ├── rollup-plugin-postcss-umi@2.0.3
│ │ │ │ ├── rollup-plugin-replace@2.2.0
│ │ │ │ ├── rollup-plugin-terser@5.1.3
│ │ │ │ ├── rollup-plugin-typescript2@0.25.3
│ │ │ │ ├── rollup-plugin-url@2.2.4
│ │ │ │ ├── signale@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_signale@1.4.0@signale deduped
│ │ │ │ ├── slash2@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_slash2@2.0.0@slash2 deduped
│ │ │ │ ├── temp-dir@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_temp-dir@2.0.0@temp-dir
│ │ │ │ ├── through2@3.0.1
│ │ │ │ ├── ts-loader@6.2.1
│ │ │ │ ├── typescript@3.7.4
│ │ │ │ ├── update-notifier@3.0.0
│ │ │ │ ├── vinyl-fs@3.0.3
│ │ │ │ └── yargs-parser@13.1.1
│ │ │ ├─┬ UNMET DEPENDENCY fs-extra@8.1.0
│ │ │ │ ├── graceful-fs@4.2.3
│ │ │ │ ├── jsonfile@4.0.0
│ │ │ │ └── universalify@0.1.2
│ │ │ ├── gh-pages@2.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_gh-pages@2.0.1@gh-pages
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.13
│ │ │ ├── UNMET DEPENDENCY prettier@1.18.2
│ │ │ ├── rc-source-loader@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_rc-source-loader@1.0.2@rc-source-loader
│ │ │ ├─┬ UNMET DEPENDENCY react-markdown@4.2.2
│ │ │ │ ├── html-to-react@1.4.2
│ │ │ │ ├── mdast-add-list-metadata@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_mdast-add-list-metadata@1.0.1@mdast-add-list-metadata
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react-is@16.12.0
│ │ │ │ ├── remark-parse@5.0.0
│ │ │ │ ├── unified@6.2.0
│ │ │ │ ├── unist-util-visit@1.4.1
│ │ │ │ └── xtend@4.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_xtend@4.0.2@xtend deduped
│ │ │ ├─┬ UNMET DEPENDENCY sass-loader@8.0.0
│ │ │ │ ├── clone-deep@4.0.1
│ │ │ │ ├── loader-utils@1.2.3
│ │ │ │ ├── neo-async@2.6.1
│ │ │ │ ├── schema-utils@2.6.1
│ │ │ │ └── semver@6.3.0
│ │ │ ├── signale@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_signale@1.4.0@signale deduped
│ │ │ ├── slash2@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_slash2@2.0.0@slash2 deduped
│ │ │ ├── staged-git-files@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_staged-git-files@1.2.0@staged-git-files
│ │ │ ├── storybook-addon-source@2.0.11 -> /Users/<USER>/git/orange/orangeview/node_modules/_storybook-addon-source@2.0.11@storybook-addon-source
│ │ │ ├─┬ UNMET DEPENDENCY umi-test@1.9.0
│ │ │ │ ├── @babel/core@7.4.5
│ │ │ │ ├── @babel/preset-typescript@7.3.3
│ │ │ │ ├── babel-core@7.0.0-bridge.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-core@7.0.0-bridge.0@babel-core
│ │ │ │ ├── babel-jest@24.9.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-jest@24.9.0@babel-jest
│ │ │ │ ├── babel-plugin-module-resolver@3.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-module-resolver@3.2.0@babel-plugin-module-resolver deduped
│ │ │ │ ├── babel-preset-umi@1.8.1
│ │ │ │ ├── core-js@3.1.4
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── enzyme@3.11.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_enzyme@3.11.0@enzyme
│ │ │ │ ├── enzyme-adapter-react-16@1.15.2
│ │ │ │ ├── identity-obj-proxy@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_identity-obj-proxy@3.0.0@identity-obj-proxy
│ │ │ │ ├── jest@24.9.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_jest@24.9.0@jest
│ │ │ │ ├── jest-cli@24.9.0
│ │ │ │ ├── jest-pnp-resolver@1.2.1
│ │ │ │ ├── jest-resolve@24.9.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_jest-resolve@24.9.0@jest-resolve
│ │ │ │ ├── react@16.12.0
│ │ │ │ ├── react-dom@16.12.0
│ │ │ │ ├── regenerator-runtime@0.13.2
│ │ │ │ ├── typescript@3.7.4
│ │ │ │ ├── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ │ │ └── yargs-parser@13.1.1
│ │ │ ├── UNMET DEPENDENCY update-notifier@3.0.0
│ │ │ └─┬ UNMET DEPENDENCY yargs-parser@13.1.1
│ │ │   ├── camelcase@5.3.1
│ │ │   └── decamelize@1.2.0
│ │ ├── UNMET DEPENDENCY fecha@2.3.3
│ │ ├─┬ UNMET DEPENDENCY form-data@2.5.1
│ │ │ ├── asynckit@0.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_asynckit@0.4.0@asynckit
│ │ │ ├── combined-stream@1.0.8 -> /Users/<USER>/git/orange/orangeview/node_modules/_combined-stream@1.0.8@combined-stream
│ │ │ └─┬ UNMET DEPENDENCY mime-types@2.1.25
│ │ │   └── mime-db@1.42.0
│ │ ├─┬ UNMET DEPENDENCY fs-extra@7.0.1
│ │ │ ├── UNMET DEPENDENCY graceful-fs@4.2.3
│ │ │ ├─┬ UNMET DEPENDENCY jsonfile@4.0.0
│ │ │ │ └── graceful-fs@4.2.3
│ │ │ └── UNMET DEPENDENCY universalify@0.1.2
│ │ ├─┬ git-local-info@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_git-local-info@1.0.1@git-local-info
│ │ │ └── UNMET DEPENDENCY ini@1.3.5
│ │ ├─┬ UNMET DEPENDENCY glob@7.1.6
│ │ │ ├── fs.realpath@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_fs.realpath@<EMAIL>
│ │ │ ├── inflight@1.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_inflight@1.0.6@inflight
│ │ │ ├── UNMET DEPENDENCY inherits@2.0.3
│ │ │ ├── minimatch@3.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_minimatch@3.0.4@minimatch deduped
│ │ │ ├── once@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_once@1.4.0@once
│ │ │ └── path-is-absolute@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-is-absolute@1.0.1@path-is-absolute
│ │ ├─┬ got@9.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_got@9.6.0@got
│ │ │ ├── @sindresorhus/is@0.14.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@sindresorhus_is@0.14.0@@sindresorhus/is
│ │ │ ├── @szmarczak/http-timer@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_@szmarczak_http-timer@1.1.2@@szmarczak/http-timer
│ │ │ ├── cacheable-request@6.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_cacheable-request@6.1.0@cacheable-request
│ │ │ ├── decompress-response@3.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_decompress-response@3.3.0@decompress-response
│ │ │ ├── duplexer3@0.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_duplexer3@0.1.4@duplexer3
│ │ │ ├── UNMET DEPENDENCY get-stream@4.1.0
│ │ │ ├── UNMET DEPENDENCY lowercase-keys@1.0.1
│ │ │ ├── mimic-response@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_mimic-response@1.0.1@mimic-response
│ │ │ ├── p-cancelable@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_p-cancelable@1.1.0@p-cancelable
│ │ │ ├── to-readable-stream@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_to-readable-stream@1.0.0@to-readable-stream
│ │ │ └── url-parse-lax@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_url-parse-lax@3.0.0@url-parse-lax
│ │ ├─┬ husky@0.14.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_husky@0.14.3@husky
│ │ │ ├── UNMET DEPENDENCY ci-info@1.6.0
│ │ │ ├── is-ci@1.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-ci@1.2.1@is-ci
│ │ │ ├── normalize-path@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_normalize-path@1.0.0@normalize-path
│ │ │ └── strip-indent@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_strip-indent@2.0.0@strip-indent
│ │ ├── js-cookie@2.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_js-cookie@2.2.1@js-cookie
│ │ ├─┬ UNMET DEPENDENCY js-yaml@3.13.1
│ │ │ ├── argparse@1.0.10 -> /Users/<USER>/git/orange/orangeview/node_modules/_argparse@1.0.10@argparse
│ │ │ └── UNMET DEPENDENCY esprima@4.0.1
│ │ ├─┬ lint-staged@7.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lint-staged@7.3.0@lint-staged
│ │ │ ├── UNMET DEPENDENCY ansi-styles@3.2.1
│ │ │ ├── chalk@2.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_chalk@2.4.2@chalk
│ │ │ ├── UNMET DEPENDENCY commander@2.20.3
│ │ │ ├─┬ UNMET DEPENDENCY cosmiconfig@5.2.1
│ │ │ │ ├── import-fresh@2.0.0
│ │ │ │ ├── is-directory@0.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-directory@0.3.1@is-directory
│ │ │ │ ├── js-yaml@3.13.1
│ │ │ │ └── parse-json@4.0.0
│ │ │ ├── UNMET DEPENDENCY cross-spawn@5.1.0
│ │ │ ├── UNMET DEPENDENCY debug@3.1.0
│ │ │ ├── dedent@0.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_dedent@0.7.0@dedent
│ │ │ ├── execa@0.9.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_execa@0.9.0@execa
│ │ │ ├── UNMET DEPENDENCY extend-shallow@3.0.2
│ │ │ ├── UNMET DEPENDENCY find-parent-dir@0.3.0
│ │ │ ├── UNMET DEPENDENCY get-stream@3.0.0
│ │ │ ├── is-glob@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-glob@4.0.1@is-glob
│ │ │ ├── is-windows@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-windows@1.0.2@is-windows deduped
│ │ │ ├── UNMET DEPENDENCY jest-get-type@22.4.3
│ │ │ ├── jest-validate@23.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_jest-validate@23.6.0@jest-validate
│ │ │ ├── UNMET DEPENDENCY leven@2.1.0
│ │ │ ├── listr@0.14.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_listr@0.14.3@listr
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├─┬ UNMET DEPENDENCY log-symbols@2.2.0
│ │ │ │ └── chalk@2.4.2
│ │ │ ├── micromatch@3.1.10 -> /Users/<USER>/git/orange/orangeview/node_modules/_micromatch@3.1.10@micromatch
│ │ │ ├── npm-which@3.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_npm-which@3.0.1@npm-which
│ │ │ ├── p-map@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_p-map@1.2.0@p-map
│ │ │ ├── path-is-inside@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-is-inside@1.0.2@path-is-inside deduped
│ │ │ ├── UNMET DEPENDENCY pify@3.0.0
│ │ │ ├── please-upgrade-node@3.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_please-upgrade-node@3.2.0@please-upgrade-node
│ │ │ ├── staged-git-files@1.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_staged-git-files@1.1.1@staged-git-files
│ │ │ ├── string-argv@0.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_string-argv@0.0.2@string-argv
│ │ │ ├── stringify-object@3.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_stringify-object@3.3.0@stringify-object
│ │ │ └── UNMET DEPENDENCY supports-color@5.5.0
│ │ ├── UNMET DEPENDENCY loadash@1.0.0
│ │ ├── UNMET DEPENDENCY lodash@4.17.4
│ │ ├── UNMET DEPENDENCY moment@2.20.1
│ │ ├── UNMET DEPENDENCY mustache@2.3.2
│ │ ├─┬ np@4.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_np@4.0.2@np
│ │ │ ├─┬ UNMET DEPENDENCY @samverschueren/stream-to-observable@0.3.0
│ │ │ │ └── any-observable@0.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_any-observable@0.3.0@any-observable deduped
│ │ │ ├── UNMET DEPENDENCY ansi-styles@3.2.1
│ │ │ ├── any-observable@0.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_any-observable@0.3.0@any-observable
│ │ │ ├── UNMET DEPENDENCY boxen@1.3.0
│ │ │ ├── UNMET DEPENDENCY camelcase-keys@4.2.0
│ │ │ ├── chalk@2.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_chalk@2.4.2@chalk
│ │ │ ├── UNMET DEPENDENCY configstore@3.1.2
│ │ │ ├── del@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_del@3.0.0@del
│ │ │ ├── UNMET DEPENDENCY execa@1.0.0
│ │ │ ├── github-url-from-git@1.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_github-url-from-git@1.5.0@github-url-from-git
│ │ │ ├── UNMET DEPENDENCY globby@6.1.0
│ │ │ ├── has-yarn@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_has-yarn@1.0.0@has-yarn
│ │ │ ├── UNMET DEPENDENCY hosted-git-info@2.8.5
│ │ │ ├── UNMET DEPENDENCY import-lazy@2.1.0
│ │ │ ├── inquirer@6.5.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_inquirer@6.5.2@inquirer
│ │ │ ├── UNMET DEPENDENCY is-ci@1.2.1
│ │ │ ├── UNMET DEPENDENCY is-npm@1.0.0
│ │ │ ├── UNMET DEPENDENCY is-path-cwd@1.0.0
│ │ │ ├── UNMET DEPENDENCY is-path-in-cwd@1.0.1
│ │ │ ├─┬ UNMET DEPENDENCY is-scoped@1.0.0
│ │ │ │ └── scoped-regex@1.0.0
│ │ │ ├── issue-regex@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_issue-regex@2.0.0@issue-regex
│ │ │ ├── UNMET DEPENDENCY latest-version@3.1.0
│ │ │ ├── listr@0.14.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_listr@0.14.3@listr deduped
│ │ │ ├── listr-input@0.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_listr-input@0.1.3@listr-input
│ │ │ ├── UNMET DEPENDENCY locate-path@3.0.0
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY log-symbols@2.2.0
│ │ │ ├── meow@5.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_meow@5.0.0@meow
│ │ │ ├── UNMET DEPENDENCY minimist-options@3.0.2
│ │ │ ├── npm-name@5.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_npm-name@5.5.0@npm-name
│ │ │ ├── opn@5.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_opn@5.5.0@opn deduped
│ │ │ ├── ow@0.10.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_ow@0.10.0@ow
│ │ │ ├── UNMET DEPENDENCY p-map@1.2.0
│ │ │ ├── p-memoize@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_p-memoize@2.1.0@p-memoize
│ │ │ ├─┬ UNMET DEPENDENCY p-timeout@2.0.1
│ │ │ │ └── p-finally@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_p-finally@1.0.0@p-finally deduped
│ │ │ ├── pkg-dir@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_pkg-dir@3.0.0@pkg-dir
│ │ │ ├── UNMET DEPENDENCY read-pkg@3.0.0
│ │ │ ├── read-pkg-up@4.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_read-pkg-up@4.0.0@read-pkg-up
│ │ │ ├── UNMET DEPENDENCY redent@2.0.0
│ │ │ ├─┬ UNMET DEPENDENCY rxjs@6.5.3
│ │ │ │ └── tslib@1.10.0
│ │ │ ├── UNMET DEPENDENCY semver@5.7.1
│ │ │ ├── split@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_split@1.0.1@split
│ │ │ ├── UNMET DEPENDENCY strip-ansi@5.2.0
│ │ │ ├── UNMET DEPENDENCY supports-color@5.5.0
│ │ │ ├── symbol-observable@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_symbol-observable@1.2.0@symbol-observable
│ │ │ ├── terminal-link@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_terminal-link@1.3.0@terminal-link
│ │ │ ├── UNMET DEPENDENCY trim-newlines@2.0.0
│ │ │ └── update-notifier@2.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_update-notifier@2.5.0@update-notifier
│ │ ├─┬ UNMET DEPENDENCY nunjucks@3.2.0
│ │ │ ├── a-sync-waterfall@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_a-sync-waterfall@1.0.1@a-sync-waterfall
│ │ │ ├── UNMET DEPENDENCY asap@2.0.6
│ │ │ ├─┬ UNMET OPTIONAL DEPENDENCY chokidar@2.1.8
│ │ │ │ ├── anymatch@2.0.0
│ │ │ │ ├── async-each@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_async-each@1.0.3@async-each
│ │ │ │ ├── braces@2.3.2
│ │ │ │ ├── fsevents@1.2.11
│ │ │ │ ├── glob-parent@3.1.0
│ │ │ │ ├── inherits@2.0.3
│ │ │ │ ├── is-binary-path@1.0.1
│ │ │ │ ├── is-glob@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-glob@4.0.1@is-glob deduped
│ │ │ │ ├── normalize-path@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_normalize-path@3.0.0@normalize-path deduped
│ │ │ │ ├── path-is-absolute@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-is-absolute@1.0.1@path-is-absolute deduped
│ │ │ │ ├── readdirp@2.2.1
│ │ │ │ └── upath@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_upath@1.2.0@upath
│ │ │ └─┬ UNMET DEPENDENCY yargs@3.32.0
│ │ │   ├── camelcase@2.1.1
│ │ │   ├── cliui@3.2.0
│ │ │   ├── decamelize@1.2.0
│ │ │   ├── os-locale@1.4.0
│ │ │   ├── string-width@1.0.2
│ │ │   ├── window-size@0.1.4
│ │ │   └── y18n@3.2.1
│ │ ├── UNMET DEPENDENCY openapi3-ts@1.3.0
│ │ ├─┬ opn@5.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_opn@5.5.0@opn
│ │ │ └── UNMET DEPENDENCY is-wsl@1.1.0
│ │ ├─┬ UNMET DEPENDENCY path-to-regexp@1.8.0
│ │ │ └── UNMET DEPENDENCY isarray@0.0.1
│ │ ├─┬ postcss-plugin-px2rem@0.8.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-plugin-px2rem@0.8.1@postcss-plugin-px2rem
│ │ │ ├── postcss@5.2.18 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss@5.2.18@postcss
│ │ │ ├── UNMET DEPENDENCY source-map@0.5.7
│ │ │ └── UNMET DEPENDENCY supports-color@3.2.3
│ │ ├── UNMET DEPENDENCY prettier@1.16.4
│ │ ├─┬ UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├─┬ UNMET DEPENDENCY loose-envify@1.4.0
│ │ │ │ └── js-tokens@3.0.2
│ │ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ │ └── UNMET DEPENDENCY react-is@16.8.3
│ │ ├─┬ UNMET DEPENDENCY query-string@6.9.0
│ │ │ ├── decode-uri-component@0.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_decode-uri-component@0.2.0@decode-uri-component
│ │ │ ├── split-on-first@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_split-on-first@1.1.0@split-on-first
│ │ │ └── strict-uri-encode@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_strict-uri-encode@2.0.0@strict-uri-encode
│ │ ├── react-helmet@5.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-helmet@5.2.1@react-helmet deduped
│ │ ├── UNMET PEER DEPENDENCY react-router-config@^1.0.0
│ │ ├─┬ react-router-redux@5.0.0-alpha.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-router-redux@5.0.0-alpha.9@react-router-redux
│ │ │ ├── history@4.10.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_history@4.10.1@history deduped
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ └─┬ UNMET DEPENDENCY react-router@4.3.1
│ │ │   ├── history@4.10.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_history@4.10.1@history deduped
│ │ │   ├── hoist-non-react-statics@2.5.5
│ │ │   ├── invariant@2.2.4
│ │ │   ├── loose-envify@1.3.1
│ │ │   ├── path-to-regexp@1.8.0
│ │ │   ├── prop-types@15.7.2
│ │ │   └── warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rimraf@2.7.1
│ │ │ └── UNMET DEPENDENCY glob@7.1.6
│ │ ├── UNMET DEPENDENCY semver@5.7.1
│ │ ├─┬ UNMET DEPENDENCY strip-ansi@5.2.0
│ │ │ └── UNMET DEPENDENCY ansi-regex@4.1.0
│ │ ├─┬ UNMET DEPENDENCY tar-fs@2.0.0
│ │ │ ├── UNMET DEPENDENCY chownr@1.1.3
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ ├── pump@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_pump@3.0.0@pump deduped
│ │ │ └─┬ UNMET DEPENDENCY tar-stream@2.1.0
│ │ │   ├── bl@3.0.0
│ │ │   ├── end-of-stream@1.4.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_end-of-stream@1.4.4@end-of-stream deduped
│ │ │   ├── fs-constants@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_fs-constants@1.0.0@fs-constants deduped
│ │ │   ├── inherits@2.0.3
│ │ │   └── readable-stream@3.4.0
│ │ ├─┬ UNMET DEPENDENCY umi-core@1.9.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/preset-typescript@7.3.3
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-transform-typescript@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/register@7.4.4
│ │ │ │ ├── core-js@3.6.0
│ │ │ │ ├── find-cache-dir@2.1.0
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── mkdirp@0.5.1
│ │ │ │ ├── pirates@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_pirates@4.0.1@pirates
│ │ │ │ └── source-map-support@0.5.16
│ │ │ ├── @umijs/error-code-map@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@umijs_error-code-map@1.0.1@@umijs/error-code-map
│ │ │ ├── UNMET DEPENDENCY babel-preset-umi@1.8.1
│ │ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ │ ├── ansi-styles@3.2.1
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ └── supports-color@5.5.0
│ │ │ ├── extend2@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_extend2@1.0.0@extend2
│ │ │ ├── marked@0.6.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_marked@0.6.2@marked
│ │ │ ├── marked-terminal@3.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_marked-terminal@3.2.0@marked-terminal
│ │ │ ├─┬ UNMET DEPENDENCY os-locale@4.0.0
│ │ │ │ ├── execa@1.0.0
│ │ │ │ ├── lcid@3.1.1
│ │ │ │ └── mem@5.1.1
│ │ │ ├── signale@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_signale@1.4.0@signale deduped
│ │ │ ├── slash2@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_slash2@2.0.0@slash2 deduped
│ │ │ └── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ ├─┬ umi-plugin-block-dev@2.2.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-plugin-block-dev@2.2.2@umi-plugin-block-dev
│ │ │ ├── path-to-regexp@2.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-to-regexp@2.4.0@path-to-regexp
│ │ │ └── uppercamelcase@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_uppercamelcase@3.0.0@uppercamelcase
│ │ ├─┬ UNMET DEPENDENCY umi-plugin-father-doc@1.0.0-alpha.9
│ │ │ ├── UNMET DEPENDENCY @babel/core@7.7.7
│ │ │ ├── UNMET DEPENDENCY @babel/generator@7.7.7
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-class-properties@7.7.4
│ │ │ │ ├── @babel/helper-create-class-features-plugin@7.7.4
│ │ │ │ └── @babel/helper-plugin-utils@7.0.0
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-transform-typescript@7.7.4
│ │ │ │ ├── @babel/helper-create-class-features-plugin@7.7.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-typescript@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/preset-env@7.7.7
│ │ │ │ ├── @babel/helper-module-imports@7.7.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ ├── @babel/plugin-proposal-async-generator-functions@7.7.4
│ │ │ │ ├── @babel/plugin-proposal-dynamic-import@7.7.4
│ │ │ │ ├── @babel/plugin-proposal-json-strings@7.7.4
│ │ │ │ ├── @babel/plugin-proposal-object-rest-spread@7.7.7
│ │ │ │ ├── @babel/plugin-proposal-optional-catch-binding@7.7.4
│ │ │ │ ├── @babel/plugin-proposal-unicode-property-regex@7.7.7
│ │ │ │ ├── @babel/plugin-syntax-async-generators@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-dynamic-import@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-json-strings@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-object-rest-spread@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-optional-catch-binding@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-top-level-await@7.7.4
│ │ │ │ ├── @babel/plugin-transform-arrow-functions@7.7.4
│ │ │ │ ├── @babel/plugin-transform-async-to-generator@7.7.4
│ │ │ │ ├── @babel/plugin-transform-block-scoped-functions@7.7.4
│ │ │ │ ├── @babel/plugin-transform-block-scoping@7.7.4
│ │ │ │ ├── @babel/plugin-transform-classes@7.7.4
│ │ │ │ ├── @babel/plugin-transform-computed-properties@7.7.4
│ │ │ │ ├── @babel/plugin-transform-destructuring@7.7.4
│ │ │ │ ├── @babel/plugin-transform-dotall-regex@7.7.7
│ │ │ │ ├── @babel/plugin-transform-duplicate-keys@7.7.4
│ │ │ │ ├── @babel/plugin-transform-exponentiation-operator@7.7.4
│ │ │ │ ├── @babel/plugin-transform-for-of@7.7.4
│ │ │ │ ├── @babel/plugin-transform-function-name@7.7.4
│ │ │ │ ├── @babel/plugin-transform-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-member-expression-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-modules-amd@7.7.5
│ │ │ │ ├── @babel/plugin-transform-modules-commonjs@7.7.5
│ │ │ │ ├── @babel/plugin-transform-modules-systemjs@7.7.4
│ │ │ │ ├── @babel/plugin-transform-modules-umd@7.7.4
│ │ │ │ ├── @babel/plugin-transform-named-capturing-groups-regex@7.7.4
│ │ │ │ ├── @babel/plugin-transform-new-target@7.7.4
│ │ │ │ ├── @babel/plugin-transform-object-super@7.7.4
│ │ │ │ ├── @babel/plugin-transform-parameters@7.7.7
│ │ │ │ ├── @babel/plugin-transform-property-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-regenerator@7.7.5
│ │ │ │ ├── @babel/plugin-transform-reserved-words@7.7.4
│ │ │ │ ├── @babel/plugin-transform-shorthand-properties@7.7.4
│ │ │ │ ├── @babel/plugin-transform-spread@7.7.4
│ │ │ │ ├── @babel/plugin-transform-sticky-regex@7.7.4
│ │ │ │ ├── @babel/plugin-transform-template-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-typeof-symbol@7.7.4
│ │ │ │ ├── @babel/plugin-transform-unicode-regex@7.7.4
│ │ │ │ ├── @babel/types@7.7.4
│ │ │ │ ├── browserslist@4.8.2
│ │ │ │ ├── core-js-compat@3.6.0
│ │ │ │ ├── invariant@2.2.2
│ │ │ │ ├── js-levenshtein@1.1.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_js-levenshtein@1.1.6@js-levenshtein
│ │ │ │ └── semver@5.7.1
│ │ │ ├─┬ UNMET DEPENDENCY @babel/preset-react@7.7.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ ├── @babel/plugin-transform-react-display-name@7.7.4
│ │ │ │ ├── @babel/plugin-transform-react-jsx@7.7.7
│ │ │ │ ├── @babel/plugin-transform-react-jsx-self@7.7.4
│ │ │ │ └── @babel/plugin-transform-react-jsx-source@7.7.4
│ │ │ ├── UNMET DEPENDENCY @babel/traverse@7.7.4
│ │ │ ├── UNMET DEPENDENCY @babel/types@7.7.4
│ │ │ ├── @mapbox/rehype-prism@0.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@mapbox_rehype-prism@0.3.1@@mapbox/rehype-prism
│ │ │ ├─┬ UNMET DEPENDENCY assert@2.0.0
│ │ │ │ ├── es6-object-assign@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_es6-object-assign@1.1.0@es6-object-assign
│ │ │ │ ├── is-nan@1.3.0
│ │ │ │ ├── object-is@1.0.2
│ │ │ │ └── util@0.12.1
│ │ │ ├── UNMET DEPENDENCY deepmerge@4.2.2
│ │ │ ├── UNMET DEPENDENCY glob@7.1.6
│ │ │ ├── hast-util-to-html@6.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_hast-util-to-html@6.1.0@hast-util-to-html
│ │ │ ├── intersection-observer@0.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_intersection-observer@0.7.0@intersection-observer
│ │ │ ├── UNMET DEPENDENCY js-yaml@3.13.1
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── lz-string@1.4.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_lz-string@1.4.4@lz-string
│ │ │ ├─┬ UNMET DEPENDENCY mdast-util-to-hast@7.0.0
│ │ │ │ ├── collapse-white-space@1.0.5
│ │ │ │ ├── detab@2.0.2
│ │ │ │ ├── mdast-util-definitions@1.2.5
│ │ │ │ ├── mdurl@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_mdurl@1.0.1@mdurl
│ │ │ │ ├── trim-lines@1.1.2
│ │ │ │ ├── unist-builder@2.0.2
│ │ │ │ ├── unist-util-generated@1.1.5
│ │ │ │ ├── unist-util-position@3.0.4
│ │ │ │ └── unist-util-visit@2.0.1
│ │ │ ├── react-clipboard.js@2.0.16 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-clipboard.js@<EMAIL>
│ │ │ ├── rehype-autolink-headings@2.0.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_rehype-autolink-headings@2.0.5@rehype-autolink-headings
│ │ │ ├── rehype-remove-comments@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_rehype-remove-comments@3.0.0@rehype-remove-comments
│ │ │ ├── rehype-slug@2.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_rehype-slug@2.0.3@rehype-slug
│ │ │ ├── rehype-stringify@6.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_rehype-stringify@6.0.1@rehype-stringify
│ │ │ ├─┬ UNMET DEPENDENCY remark-frontmatter@1.3.2
│ │ │ │ ├── fault@1.0.3
│ │ │ │ └── xtend@4.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_xtend@4.0.2@xtend deduped
│ │ │ ├─┬ UNMET DEPENDENCY remark-parse@7.0.2
│ │ │ │ ├── collapse-white-space@1.0.5
│ │ │ │ ├── is-alphabetical@1.0.3
│ │ │ │ ├── is-decimal@1.0.3
│ │ │ │ ├── is-whitespace-character@1.0.3
│ │ │ │ ├── is-word-character@1.0.3
│ │ │ │ ├── markdown-escapes@1.0.3
│ │ │ │ ├── parse-entities@1.2.2
│ │ │ │ ├── repeat-string@1.6.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_repeat-string@1.6.1@repeat-string
│ │ │ │ ├── state-toggle@1.0.2
│ │ │ │ ├── trim@0.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_trim@0.0.1@trim
│ │ │ │ ├── trim-trailing-lines@1.1.2
│ │ │ │ ├── unherit@1.1.2
│ │ │ │ ├── unist-util-remove-position@1.1.4
│ │ │ │ ├── vfile-location@2.0.6
│ │ │ │ └── xtend@4.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_xtend@4.0.2@xtend deduped
│ │ │ ├── remark-rehype@5.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_remark-rehype@5.0.0@remark-rehype
│ │ │ ├── UNMET DEPENDENCY scrollama@2.1.3
│ │ │ ├── slash2@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_slash2@2.0.0@slash2 deduped
│ │ │ ├─┬ UNMET DEPENDENCY sylvanas@0.4.3
│ │ │ │ ├── @babel/core@7.7.7
│ │ │ │ ├── @babel/plugin-syntax-decorators@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-dynamic-import@7.7.4
│ │ │ │ ├── @babel/plugin-transform-typescript@7.7.4
│ │ │ │ ├── @types/prettier@1.19.0
│ │ │ │ ├── @umijs/fabric@1.2.12
│ │ │ │ ├── eslint@5.16.0
│ │ │ │ ├── fs-extra@8.1.0
│ │ │ │ ├── import-fresh@3.2.1
│ │ │ │ └── prettier@1.19.1
│ │ │ ├── UNMET DEPENDENCY umi@2.12.7
│ │ │ ├── UNMET DEPENDENCY umi-build-dev@1.16.10
│ │ │ ├── UNMET DEPENDENCY umi-plugin-react@1.14.10
│ │ │ ├── UNMET DEPENDENCY umi-types@0.5.9
│ │ │ ├─┬ UNMET DEPENDENCY unified@8.4.2
│ │ │ │ ├── bail@1.0.4
│ │ │ │ ├── extend@3.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_extend@3.0.2@extend deduped
│ │ │ │ ├── is-plain-obj@2.0.0
│ │ │ │ ├── trough@1.0.4
│ │ │ │ └── vfile@4.0.2
│ │ │ ├─┬ UNMET DEPENDENCY unist-util-visit@2.0.1
│ │ │ │ ├── @types/unist@2.0.3
│ │ │ │ ├── unist-util-is@4.0.1
│ │ │ │ └── unist-util-visit-parents@3.0.1
│ │ │ └─┬ UNMET DEPENDENCY unist-util-visit-parents@3.0.1
│ │ │   ├── @types/unist@2.0.3
│ │ │   └── unist-util-is@4.0.1
│ │ ├─┬ umi-plugin-mpa@2.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-plugin-mpa@2.1.2@umi-plugin-mpa
│ │ │ ├─┬ UNMET DEPENDENCY ajv@6.10.2
│ │ │ │ ├── fast-deep-equal@2.0.1
│ │ │ │ ├── fast-json-stable-stringify@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_fast-json-stable-stringify@2.1.0@fast-json-stable-stringify deduped
│ │ │ │ ├── json-schema-traverse@0.4.1
│ │ │ │ └── uri-js@4.2.2
│ │ │ ├── UNMET DEPENDENCY html-minifier@3.5.21
│ │ │ ├── html-webpack-plugin@3.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_html-webpack-plugin@3.2.0@html-webpack-plugin
│ │ │ ├── UNMET DEPENDENCY loader-utils@0.2.17
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ └── UNMET DEPENDENCY semver@5.7.1
│ │ ├─┬ umi-plugin-pro-block@1.3.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-plugin-pro-block@1.3.6@umi-plugin-pro-block
│ │ │ └─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │   └── ms@2.1.2
│ │ ├─┬ UNMET DEPENDENCY umi-plugin-react@1.14.10
│ │ │ ├── UNMET DEPENDENCY antd@3.13.6
│ │ │ ├─┬ UNMET DEPENDENCY antd-mobile@2.3.1
│ │ │ │ ├── array-tree-filter@2.1.0
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── classnames@2.2.5
│ │ │ │ ├── normalize.css@7.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_normalize.css@<EMAIL>
│ │ │ │ ├── rc-checkbox@2.0.3
│ │ │ │ ├── rc-collapse@1.9.3
│ │ │ │ ├── rc-slider@8.2.0
│ │ │ │ ├── rc-swipeout@2.0.11 -> /Users/<USER>/git/orange/orangeview/node_modules/_rc-swipeout@2.0.11@rc-swipeout
│ │ │ │ ├── rmc-calendar@1.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-calendar@1.1.4@rmc-calendar
│ │ │ │ ├── rmc-cascader@5.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-cascader@5.0.3@rmc-cascader
│ │ │ │ ├── rmc-date-picker@6.0.10 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-date-picker@6.0.10@rmc-date-picker
│ │ │ │ ├── rmc-dialog@1.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-dialog@1.1.1@rmc-dialog
│ │ │ │ ├── rmc-drawer@0.4.11 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-drawer@0.4.11@rmc-drawer
│ │ │ │ ├── rmc-feedback@2.0.0
│ │ │ │ ├── rmc-input-number@1.0.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-input-number@1.0.5@rmc-input-number
│ │ │ │ ├── rmc-list-view@0.11.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-list-view@0.11.5@rmc-list-view
│ │ │ │ ├── rmc-notification@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-notification@1.0.0@rmc-notification
│ │ │ │ ├── rmc-nuka-carousel@3.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-nuka-carousel@3.0.1@rmc-nuka-carousel
│ │ │ │ ├── rmc-picker@5.0.10 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-picker@5.0.10@rmc-picker
│ │ │ │ ├── rmc-pull-to-refresh@1.0.11
│ │ │ │ ├── rmc-steps@1.0.0
│ │ │ │ ├── rmc-tabs@1.2.29 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-tabs@1.2.29@rmc-tabs
│ │ │ │ └── rmc-tooltip@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_rmc-tooltip@1.0.1@rmc-tooltip
│ │ │ ├── UNMET DEPENDENCY babel-plugin-import@1.13.0
│ │ │ ├── fastclick@1.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_fastclick@1.0.6@fastclick deduped
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.13
│ │ │ ├── UNMET DEPENDENCY mustache@3.0.1
│ │ │ ├── preact@8.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_preact@8.4.2@preact
│ │ │ ├── preact-compat@3.19.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_preact-compat@3.19.0@preact-compat
│ │ │ ├── register-service-worker@1.6.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_register-service-worker@1.6.2@register-service-worker
│ │ │ ├─┬ UNMET DEPENDENCY umi-plugin-dll@1.6.0
│ │ │ │ ├── rimraf@2.6.3
│ │ │ │ └── serve-static@1.14.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_serve-static@1.14.1@serve-static deduped
│ │ │ ├─┬ UNMET DEPENDENCY umi-plugin-dva@1.11.0
│ │ │ │ ├── babel-plugin-dva-hmr@0.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-dva-hmr@0.4.2@babel-plugin-dva-hmr
│ │ │ │ ├── dva@2.6.0-beta.20
│ │ │ │ ├── UNMET PEER DEPENDENCY dva-core@^1.1.0 | ^1.5.0-0 | ^1.6.0-0
│ │ │ │ ├── dva-immer@0.4.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_dva-immer@0.4.5@dva-immer
│ │ │ │ ├── dva-loading@3.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_dva-loading@3.0.6@dva-loading
│ │ │ │ ├── globby@7.1.1
│ │ │ │ ├── lodash.uniq@4.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.uniq@<EMAIL>
│ │ │ │ ├── object-assign@4.1.1
│ │ │ │ ├── path-is-root@0.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-is-root@0.1.0@path-is-root
│ │ │ │ ├── path-to-regexp@1.7.0
│ │ │ │ ├── UNMET PEER DEPENDENCY redbox-react@1.x
│ │ │ │ └── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ │ ├─┬ UNMET DEPENDENCY umi-plugin-hd@1.7.0-beta.2
│ │ │ │ ├── postcss-plugin-px2rem@0.8.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-plugin-px2rem@0.8.1@postcss-plugin-px2rem deduped
│ │ │ │ └── umi-hd@5.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-hd@5.0.1@umi-hd
│ │ │ ├─┬ UNMET DEPENDENCY umi-plugin-locale@2.11.3
│ │ │ │ ├── @ant-design/create-react-context@0.2.4
│ │ │ │ ├── globby@7.1.1
│ │ │ │ ├── intl@1.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_intl@1.2.5@intl
│ │ │ │ ├── lodash.groupby@4.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.groupby@<EMAIL>
│ │ │ │ ├── moment@2.20.1
│ │ │ │ ├── mustache@3.0.1
│ │ │ │ ├── prop-types@15.6.2
│ │ │ │ ├── react-intl@2.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-intl@2.7.2@react-intl
│ │ │ │ └── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ │ ├── umi-plugin-polyfills@1.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-plugin-polyfills@1.4.2@umi-plugin-polyfills
│ │ │ ├── UNMET DEPENDENCY umi-plugin-routes@1.8.4
│ │ │ ├─┬ UNMET DEPENDENCY umi-plugin-ui@1.4.10
│ │ │ │ ├── got@9.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_got@9.6.0@got deduped
│ │ │ │ ├── immer@5.1.0
│ │ │ │ ├── is-mobile@2.1.0
│ │ │ │ ├── mkdirp@0.5.1
│ │ │ │ ├── sockjs-client@1.3.0
│ │ │ │ ├── styled-components@4.4.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_styled-components@4.4.1@styled-components deduped
│ │ │ │ └── umi-ui-tasks@1.3.7
│ │ │ ├── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ │ ├─┬ UNMET DEPENDENCY webpack@4.41.1
│ │ │ │ ├── @webassemblyjs/ast@1.8.5
│ │ │ │ ├── @webassemblyjs/helper-module-context@1.8.5
│ │ │ │ ├── @webassemblyjs/wasm-edit@1.8.5
│ │ │ │ ├── @webassemblyjs/wasm-parser@1.8.5
│ │ │ │ ├── acorn@6.4.0
│ │ │ │ ├── ajv@6.10.2
│ │ │ │ ├── ajv-keywords@3.4.1
│ │ │ │ ├── chrome-trace-event@1.0.2
│ │ │ │ ├── enhanced-resolve@4.1.1
│ │ │ │ ├── eslint-scope@4.0.3
│ │ │ │ ├── json-parse-better-errors@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_json-parse-better-errors@1.0.2@json-parse-better-errors
│ │ │ │ ├── loader-runner@2.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_loader-runner@2.4.0@loader-runner
│ │ │ │ ├── loader-utils@1.2.3
│ │ │ │ ├── memory-fs@0.4.1
│ │ │ │ ├── micromatch@3.1.10
│ │ │ │ ├── mkdirp@0.5.1
│ │ │ │ ├── neo-async@2.6.1
│ │ │ │ ├── node-libs-browser@2.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_node-libs-browser@2.2.1@node-libs-browser
│ │ │ │ ├── schema-utils@1.0.0
│ │ │ │ ├── tapable@1.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_tapable@1.1.3@tapable
│ │ │ │ ├── terser-webpack-plugin@1.4.3
│ │ │ │ ├── watchpack@1.6.0
│ │ │ │ └── webpack-sources@1.4.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_webpack-sources@1.4.3@webpack-sources
│ │ │ └── workbox-webpack-plugin@3.6.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_workbox-webpack-plugin@3.6.3@workbox-webpack-plugin
│ │ ├─┬ umi-plugin-react-router-four@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-plugin-react-router-four@1.0.1@umi-plugin-react-router-four
│ │ │ ├── UNMET DEPENDENCY react-router@4.3.1
│ │ │ ├── UNMET DEPENDENCY react-router-config@1.0.0-beta.4
│ │ │ └── UNMET DEPENDENCY react-router-dom@4.3.1
│ │ ├─┬ UNMET DEPENDENCY umi-plugin-remax@1.10.7
│ │ │ ├─┬ UNMET DEPENDENCY remax-cli@1.10.7
│ │ │ │ ├── @babel/core@7.7.7
│ │ │ │ ├── @babel/helper-module-imports@7.7.4
│ │ │ │ ├── @babel/preset-env@7.7.7
│ │ │ │ ├── @babel/preset-typescript@7.7.4
│ │ │ │ ├── @babel/register@7.7.7
│ │ │ │ ├── @babel/types@7.7.4
│ │ │ │ ├── @remax/postcss-px2units@0.2.0
│ │ │ │ ├── @remax/rollup-plugin-postcss@2.0.5
│ │ │ │ ├── @remax/rollup-plugin-url@3.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_@remax_rollup-plugin-url@3.0.2@@remax/rollup-plugin-url
│ │ │ │ ├── @rollup/plugin-alias@2.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@rollup_plugin-alias@2.2.0@@rollup/plugin-alias
│ │ │ │ ├── @rollup/plugin-json@4.0.1
│ │ │ │ ├── @rollup/plugin-replace@2.3.0
│ │ │ │ ├── acorn-jsx@5.1.0
│ │ │ │ ├── acorn-walk@7.0.0
│ │ │ │ ├── babel-preset-remax@1.10.7
│ │ │ │ ├── chokidar@3.3.1
│ │ │ │ ├── command-exists@1.2.8
│ │ │ │ ├── commander@2.20.3
│ │ │ │ ├── dot-prop@5.2.0
│ │ │ │ ├── ejs@3.0.1
│ │ │ │ ├── esm@3.2.25 -> /Users/<USER>/git/orange/orangeview/node_modules/_esm@3.2.25@esm deduped
│ │ │ │ ├── htmlparser2@4.0.0
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── mkdirp@0.5.1
│ │ │ │ ├── named-exports-db@0.1.4
│ │ │ │ ├── postcss-url@8.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-url@8.0.0@postcss-url
│ │ │ │ ├── regenerator-runtime@0.13.3
│ │ │ │ ├── remax@1.10.7
│ │ │ │ ├── remax-stats@0.1.2
│ │ │ │ ├── resolve@1.14.1
│ │ │ │ ├── rollup@1.27.8
│ │ │ │ ├── rollup-plugin-babel@4.3.3
│ │ │ │ ├── rollup-plugin-clear@2.0.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_rollup-plugin-clear@2.0.7@rollup-plugin-clear
│ │ │ │ ├── rollup-plugin-commonjs@10.0.0
│ │ │ │ ├── rollup-plugin-copy@3.1.0
│ │ │ │ ├── rollup-plugin-delete@1.1.0
│ │ │ │ ├── rollup-plugin-node-resolve@5.2.0
│ │ │ │ ├── rollup-plugin-progress@1.1.1
│ │ │ │ ├── schema-utils@2.6.1
│ │ │ │ ├── semver@6.3.0
│ │ │ │ ├── slash2@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_slash2@2.0.0@slash2 deduped
│ │ │ │ └── yargs@15.0.2
│ │ │ └── UNMET DEPENDENCY umi-types@0.5.9
│ │ ├─┬ UNMET DEPENDENCY umi-request@1.2.15
│ │ │ ├── UNMET DEPENDENCY isomorphic-fetch@2.2.1
│ │ │ └── UNMET DEPENDENCY qs@6.9.1
│ │ ├─┬ UNMET DEPENDENCY umi-types@0.5.9
│ │ │ ├─┬ UNMET DEPENDENCY @types/cheerio@0.22.15
│ │ │ │ └── @types/node@12.12.21
│ │ │ ├── UNMET DEPENDENCY @types/debug@4.1.5
│ │ │ ├── UNMET DEPENDENCY @types/lodash@4.14.149
│ │ │ ├── @types/moment@2.13.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@types_moment@2.13.0@@types/moment
│ │ │ ├── UNMET DEPENDENCY @types/react@16.9.16
│ │ │ ├─┬ UNMET DEPENDENCY @types/react-redux@7.1.5
│ │ │ │ ├── @types/hoist-non-react-statics@3.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@types_hoist-non-react-statics@3.3.1@@types/hoist-non-react-statics deduped
│ │ │ │ ├── @types/react@16.9.16
│ │ │ │ ├── hoist-non-react-statics@3.3.0
│ │ │ │ └── redux@4.0.4
│ │ │ ├─┬ UNMET DEPENDENCY @types/signale@1.2.1
│ │ │ │ └── @types/node@12.12.21
│ │ │ ├─┬ UNMET DEPENDENCY @types/webpack@4.41.0
│ │ │ │ ├── @types/anymatch@1.3.1
│ │ │ │ ├── @types/node@12.12.21
│ │ │ │ ├── @types/tapable@1.0.4
│ │ │ │ ├── @types/uglify-js@3.0.4
│ │ │ │ ├── @types/webpack-sources@0.1.5
│ │ │ │ └── source-map@0.6.1
│ │ │ ├── @types/webpack-chain@5.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@types_webpack-chain@5.2.0@@types/webpack-chain
│ │ │ └── @types/xterm@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@types_xterm@3.0.0@@types/xterm
│ │ ├─┬ umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils
│ │ │ ├── UNMET DEPENDENCY ansi-styles@3.2.1
│ │ │ ├── chalk@2.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_chalk@2.4.2@chalk
│ │ │ ├── UNMET DEPENDENCY dotenv@8.2.0
│ │ │ ├── is-url@1.2.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-url@1.2.4@is-url
│ │ │ ├── node-fetch@2.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_node-fetch@2.6.0@node-fetch
│ │ │ ├── prettier@1.15.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_prettier@1.15.3@prettier
│ │ │ ├── slash2@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_slash2@2.0.0@slash2 deduped
│ │ │ └── UNMET DEPENDENCY supports-color@5.5.0
│ │ ├── UNMET DEPENDENCY urllib@2.34.1
│ │ ├─┬ user-home@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_user-home@2.0.0@user-home
│ │ │ └── UNMET DEPENDENCY os-homedir@1.0.2
│ │ ├─┬ UNMET DEPENDENCY utility@1.16.3
│ │ │ ├── copy-to@2.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_copy-to@2.0.1@copy-to
│ │ │ ├── escape-html@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_escape-html@1.0.3@escape-html deduped
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ ├── mz@2.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mz@2.7.0@mz deduped
│ │ │ └── unescape@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_unescape@1.0.1@unescape
│ │ ├── UNMET DEPENDENCY uuid@3.3.3
│ │ ├─┬ UNMET DEPENDENCY yargs-parser@13.1.1
│ │ │ ├── UNMET DEPENDENCY camelcase@5.3.1
│ │ │ └── UNMET DEPENDENCY decamelize@1.2.0
│ │ └─┬ yazl@2.5.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_yazl@2.5.1@yazl
│ │   └── buffer-crc32@0.2.13 -> /Users/<USER>/git/orange/orangeview/node_modules/_buffer-crc32@0.2.13@buffer-crc32
│ ├── UNMET DEPENDENCY @types/classnames@2.2.9
│ ├── UNMET DEPENDENCY @types/js-cookie@2.2.4
│ ├── UNMET DEPENDENCY @types/lodash@4.14.149
│ ├── UNMET DEPENDENCY @types/prop-types@15.7.3
│ ├─┬ UNMET DEPENDENCY @types/react@16.9.16
│ │ ├── UNMET DEPENDENCY @types/prop-types@15.7.3
│ │ └── UNMET DEPENDENCY csstype@2.6.8
│ ├─┬ UNMET DEPENDENCY @types/react-document-title@2.0.3
│ │ └── UNMET DEPENDENCY @types/react@16.9.16
│ ├─┬ UNMET DEPENDENCY @types/react-dom@16.9.4
│ │ └── UNMET DEPENDENCY @types/react@16.9.16
│ ├─┬ UNMET DEPENDENCY @types/react-helmet@5.0.14
│ │ └── UNMET DEPENDENCY @types/react@16.9.16
│ ├─┬ UNMET DEPENDENCY antd@3.26.4
│ │ ├─┬ @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context
│ │ │ ├── UNMET DEPENDENCY gud@1.0.0
│ │ │ └── warning@4.0.3
│ │ ├── UNMET DEPENDENCY @ant-design/icons@2.1.1
│ │ ├─┬ UNMET DEPENDENCY @ant-design/icons-react@2.0.1
│ │ │ ├─┬ UNMET DEPENDENCY @ant-design/colors@3.2.2
│ │ │ │ └── tinycolor2@1.4.1
│ │ │ └── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├─┬ UNMET DEPENDENCY @types/react-slick@0.23.4
│ │ │ └── UNMET DEPENDENCY @types/react@16.9.16
│ │ ├── UNMET DEPENDENCY array-tree-filter@2.1.0
│ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ ├─┬ UNMET DEPENDENCY copy-to-clipboard@3.2.0
│ │ │ └── toggle-selection@1.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_toggle-selection@1.0.6@toggle-selection
│ │ ├─┬ UNMET DEPENDENCY css-animation@1.6.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ └── UNMET DEPENDENCY component-classes@1.2.6
│ │ ├── UNMET DEPENDENCY dom-closest@0.2.0
│ │ ├── UNMET DEPENDENCY enquire.js@2.1.6
│ │ ├── UNMET DEPENDENCY is-mobile@2.1.0
│ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ ├─┬ UNMET DEPENDENCY omit.js@1.0.2
│ │ │ └── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├─┬ UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├─┬ UNMET DEPENDENCY loose-envify@1.4.0
│ │ │ │ └── js-tokens@3.0.2
│ │ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ │ └── UNMET DEPENDENCY react-is@16.8.3
│ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ ├─┬ UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY css-animation@1.6.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-calendar@9.15.8
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-cascader@0.17.5
│ │ │ ├── UNMET DEPENDENCY array-tree-filter@2.1.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallow-equal@1.1.0
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├── UNMET DEPENDENCY rc-checkbox@2.1.6
│ │ ├─┬ UNMET DEPENDENCY rc-collapse@1.11.7
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY css-animation@1.6.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY react-is@16.8.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├─┬ UNMET DEPENDENCY rc-dialog@7.6.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ └── UNMET DEPENDENCY rc-util@4.16.3
│ │ ├─┬ UNMET DEPENDENCY rc-drawer@3.1.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├── UNMET DEPENDENCY rc-dropdown@2.4.1
│ │ ├─┬ UNMET DEPENDENCY rc-editor-mention@1.1.13
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY draft-js@0.10.5
│ │ │ ├── UNMET DEPENDENCY immutable@3.7.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ └── UNMET DEPENDENCY rc-editor-core@0.8.9
│ │ ├─┬ UNMET DEPENDENCY rc-form@2.4.11
│ │ │ ├── UNMET DEPENDENCY async-validator@1.11.5
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY create-react-class@15.6.3
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY hoist-non-react-statics@3.3.0
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-input-number@4.5.3
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ └── UNMET DEPENDENCY rmc-feedback@2.0.0
│ │ ├─┬ UNMET DEPENDENCY rc-mentions@0.4.1
│ │ │ ├── @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context deduped
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├─┬ UNMET DEPENDENCY rc-menu@7.5.3
│ │ │ │ ├── classnames@2.2.6
│ │ │ │ ├── dom-scroll-into-view@1.2.1
│ │ │ │ ├── mini-store@2.0.0
│ │ │ │ ├── mutationobserver-shim@0.3.3
│ │ │ │ ├── rc-animate@2.10.2
│ │ │ │ ├── rc-trigger@2.6.2
│ │ │ │ ├── rc-util@4.16.2
│ │ │ │ ├── resize-observer-polyfill@1.5.1
│ │ │ │ └── shallowequal@1.1.0
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.6.0
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-menu@7.5.3
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY mini-store@2.0.0
│ │ │ ├── UNMET DEPENDENCY mutationobserver-shim@0.3.3
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├── UNMET DEPENDENCY rc-notification@3.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-pagination@1.20.11
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-progress@2.5.2
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ └── UNMET DEPENDENCY prop-types@15.7.2
│ │ ├── UNMET DEPENDENCY rc-rate@2.5.0
│ │ ├── rc-resize-observer@0.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_rc-resize-observer@0.1.3@rc-resize-observer deduped
│ │ ├─┬ UNMET DEPENDENCY rc-select@9.2.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY component-classes@1.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-menu@7.5.3
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-slider@8.7.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-tooltip@3.7.3
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-steps@3.5.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ └── UNMET DEPENDENCY prop-types@15.7.2
│ │ ├── UNMET DEPENDENCY rc-switch@1.9.0
│ │ ├─┬ UNMET DEPENDENCY rc-table@6.10.7
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY component-classes@1.2.6
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY mini-store@2.0.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├─┬ UNMET DEPENDENCY rc-tabs@9.7.0
│ │ │ ├── @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context deduped
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-hammerjs@0.6.9
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-time-picker@3.7.3
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├── UNMET DEPENDENCY rc-tooltip@3.7.3
│ │ ├─┬ UNMET DEPENDENCY rc-tree@2.1.3
│ │ │ ├── @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context deduped
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-tree-select@2.9.4
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-tree@2.1.3
│ │ │ ├─┬ UNMET DEPENDENCY rc-trigger@3.0.0
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── classnames@2.2.6
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── raf@3.4.1
│ │ │ │ ├── rc-align@2.4.5
│ │ │ │ ├── rc-animate@3.0.0-rc.6
│ │ │ │ └── rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ ├─┬ UNMET DEPENDENCY rc-upload@2.9.4
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-util@4.16.3
│ │ │ ├── UNMET DEPENDENCY add-dom-event-listener@1.1.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├── UNMET DEPENDENCY react-lazy-load@3.0.13
│ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY react-slick@0.25.2
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY enquire.js@2.1.6
│ │ │ ├── UNMET DEPENDENCY json2mq@0.2.0
│ │ │ ├── UNMET DEPENDENCY lodash.debounce@4.0.8
│ │ │ └── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ ├── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ └─┬ UNMET DEPENDENCY warning@4.0.3
│ │   └── UNMET DEPENDENCY loose-envify@1.4.0
│ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ ├─┬ UNMET DEPENDENCY ansi-styles@3.2.1
│ │ │ └─┬ UNMET DEPENDENCY color-convert@1.9.3
│ │ │   └── color-name@1.1.3
│ │ ├── UNMET DEPENDENCY escape-string-regexp@1.0.5
│ │ └─┬ UNMET DEPENDENCY supports-color@5.5.0
│ │   └── UNMET DEPENDENCY has-flag@3.0.0
│ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ └── UNMET DEPENDENCY ms@2.1.2
│ ├── macaddress@0.2.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_macaddress@0.2.9@macaddress
│ ├─┬ npm-updater@3.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_npm-updater@3.0.3@npm-updater
│ │ ├─┬ UNMET DEPENDENCY chalk@1.1.3
│ │ │ ├── UNMET DEPENDENCY ansi-styles@2.2.1
│ │ │ ├── UNMET DEPENDENCY escape-string-regexp@1.0.5
│ │ │ ├─┬ UNMET DEPENDENCY has-ansi@2.0.0
│ │ │ │ └── ansi-regex@2.1.1
│ │ │ ├─┬ UNMET DEPENDENCY strip-ansi@3.0.1
│ │ │ │ └── ansi-regex@2.1.1
│ │ │ └── UNMET DEPENDENCY supports-color@2.0.0
│ │ ├── co@4.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_co@4.6.0@co deduped
│ │ ├─┬ UNMET DEPENDENCY configstore@3.1.2
│ │ │ ├─┬ UNMET DEPENDENCY dot-prop@4.2.0
│ │ │ │ └── is-obj@1.0.1
│ │ │ ├── UNMET DEPENDENCY graceful-fs@4.2.3
│ │ │ ├─┬ UNMET DEPENDENCY make-dir@1.3.0
│ │ │ │ └── pify@3.0.0
│ │ │ ├── unique-string@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_unique-string@1.0.0@unique-string
│ │ │ ├─┬ UNMET DEPENDENCY write-file-atomic@2.4.3
│ │ │ │ ├── graceful-fs@4.2.3
│ │ │ │ ├── imurmurhash@0.1.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_imurmurhash@0.1.4@imurmurhash deduped
│ │ │ │ └── signal-exit@3.0.2
│ │ │ └── xdg-basedir@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_xdg-basedir@3.0.0@xdg-basedir
│ │ ├─┬ debug@2.6.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_debug@2.6.9@debug
│ │ │ └── UNMET DEPENDENCY ms@2.0.0
│ │ ├─┬ humanize-ms@1.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_humanize-ms@1.2.1@humanize-ms
│ │ │ └── UNMET DEPENDENCY ms@2.0.0
│ │ ├─┬ semver-diff@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_semver-diff@2.1.0@semver-diff
│ │ │ └── UNMET DEPENDENCY semver@5.7.1
│ │ └── UNMET DEPENDENCY urllib@2.34.1
│ ├─┬ UNMET DEPENDENCY umi@2.12.7
│ │ ├─┬ UNMET DEPENDENCY @babel/core@7.4.5
│ │ │ ├── UNMET DEPENDENCY @babel/code-frame@7.5.5
│ │ │ ├── UNMET DEPENDENCY @babel/generator@7.7.7
│ │ │ ├─┬ UNMET DEPENDENCY @babel/helpers@7.7.4
│ │ │ │ ├── @babel/template@7.7.4
│ │ │ │ ├── @babel/traverse@7.7.4
│ │ │ │ └── @babel/types@7.7.4
│ │ │ ├── UNMET DEPENDENCY @babel/parser@7.7.7
│ │ │ ├─┬ UNMET DEPENDENCY @babel/template@7.7.4
│ │ │ │ ├── @babel/code-frame@7.5.5
│ │ │ │ ├── @babel/parser@7.7.7
│ │ │ │ └── @babel/types@7.7.4
│ │ │ ├── UNMET DEPENDENCY @babel/traverse@7.7.4
│ │ │ ├── UNMET DEPENDENCY @babel/types@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY convert-source-map@1.7.0
│ │ │ │ └── safe-buffer@5.1.2
│ │ │ ├── UNMET DEPENDENCY debug@4.1.1
│ │ │ ├─┬ UNMET DEPENDENCY json5@2.1.1
│ │ │ │ └── minimist@1.2.0
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.13
│ │ │ ├─┬ UNMET DEPENDENCY resolve@1.14.1
│ │ │ │ └── path-parse@1.0.6
│ │ │ ├── UNMET DEPENDENCY semver@5.7.1
│ │ │ └── UNMET DEPENDENCY source-map@0.5.7
│ │ ├─┬ UNMET DEPENDENCY @babel/runtime@7.4.5
│ │ │ └── UNMET DEPENDENCY regenerator-runtime@0.13.3
│ │ ├── UNMET DEPENDENCY @types/react@16.9.16
│ │ ├─┬ UNMET DEPENDENCY @types/react-router-dom@5.1.3
│ │ │ ├── UNMET DEPENDENCY @types/history@4.7.3
│ │ │ ├── UNMET DEPENDENCY @types/react@16.9.16
│ │ │ └─┬ UNMET DEPENDENCY @types/react-router@5.1.3
│ │ │   ├── @types/history@4.7.3
│ │ │   └── @types/react@16.9.16
│ │ ├─┬ UNMET DEPENDENCY babel-preset-umi@1.8.1
│ │ │ ├─┬ UNMET DEPENDENCY @babel/core@7.4.5
│ │ │ │ ├── @babel/code-frame@7.5.5
│ │ │ │ ├── @babel/generator@7.7.7
│ │ │ │ ├── @babel/helpers@7.7.4
│ │ │ │ ├── @babel/parser@7.7.7
│ │ │ │ ├── @babel/template@7.7.4
│ │ │ │ ├── @babel/traverse@7.7.4
│ │ │ │ ├── @babel/types@7.7.4
│ │ │ │ ├── convert-source-map@1.7.0
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── json5@2.1.1
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ ├── resolve@1.14.1
│ │ │ │ ├── semver@5.7.1
│ │ │ │ └── source-map@0.5.7
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-async-generator-functions@7.2.0
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ ├── @babel/helper-remap-async-to-generator@7.7.4
│ │ │ │ └── @babel/plugin-syntax-async-generators@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-class-properties@7.4.4
│ │ │ │ ├── @babel/helper-create-class-features-plugin@7.7.4
│ │ │ │ └── @babel/helper-plugin-utils@7.0.0
│ │ │ ├── UNMET DEPENDENCY @babel/plugin-proposal-decorators@7.4.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-do-expressions@7.2.0
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-do-expressions@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-export-default-from@7.2.0
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-export-default-from@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-export-namespace-from@7.2.0
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-export-namespace-from@7.7.4
│ │ │ ├── @babel/plugin-proposal-function-bind@7.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_@babel_plugin-proposal-function-bind@7.2.0@@babel/plugin-proposal-function-bind
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-nullish-coalescing-operator@7.4.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-nullish-coalescing-operator@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-object-rest-spread@7.4.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-object-rest-spread@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-optional-catch-binding@7.2.0
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-optional-catch-binding@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-proposal-optional-chaining@7.2.0
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ └── @babel/plugin-syntax-optional-chaining@7.7.4
│ │ │ ├── @babel/plugin-proposal-pipeline-operator@7.3.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_@babel_plugin-proposal-pipeline-operator@7.3.2@@babel/plugin-proposal-pipeline-operator
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-syntax-dynamic-import@7.2.0
│ │ │ │ └── @babel/helper-plugin-utils@7.0.0
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-transform-destructuring@7.4.4
│ │ │ │ └── @babel/helper-plugin-utils@7.0.0
│ │ │ ├─┬ UNMET DEPENDENCY @babel/plugin-transform-runtime@7.4.4
│ │ │ │ ├── @babel/helper-module-imports@7.7.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ ├── resolve@1.14.1
│ │ │ │ └── semver@5.7.1
│ │ │ ├─┬ UNMET DEPENDENCY @babel/preset-env@7.4.5
│ │ │ │ ├── @babel/helper-module-imports@7.7.4
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ ├── @babel/plugin-proposal-async-generator-functions@7.2.0
│ │ │ │ ├── @babel/plugin-proposal-json-strings@7.7.4
│ │ │ │ ├── @babel/plugin-proposal-object-rest-spread@7.4.4
│ │ │ │ ├── @babel/plugin-proposal-optional-catch-binding@7.2.0
│ │ │ │ ├── @babel/plugin-proposal-unicode-property-regex@7.7.7
│ │ │ │ ├── @babel/plugin-syntax-async-generators@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-json-strings@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-object-rest-spread@7.7.4
│ │ │ │ ├── @babel/plugin-syntax-optional-catch-binding@7.7.4
│ │ │ │ ├── @babel/plugin-transform-arrow-functions@7.7.4
│ │ │ │ ├── @babel/plugin-transform-async-to-generator@7.7.4
│ │ │ │ ├── @babel/plugin-transform-block-scoped-functions@7.7.4
│ │ │ │ ├── @babel/plugin-transform-block-scoping@7.7.4
│ │ │ │ ├── @babel/plugin-transform-classes@7.7.4
│ │ │ │ ├── @babel/plugin-transform-computed-properties@7.7.4
│ │ │ │ ├── @babel/plugin-transform-destructuring@7.4.4
│ │ │ │ ├── @babel/plugin-transform-dotall-regex@7.7.7
│ │ │ │ ├── @babel/plugin-transform-duplicate-keys@7.7.4
│ │ │ │ ├── @babel/plugin-transform-exponentiation-operator@7.7.4
│ │ │ │ ├── @babel/plugin-transform-for-of@7.7.4
│ │ │ │ ├── @babel/plugin-transform-function-name@7.7.4
│ │ │ │ ├── @babel/plugin-transform-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-member-expression-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-modules-amd@7.7.5
│ │ │ │ ├── @babel/plugin-transform-modules-commonjs@7.7.5
│ │ │ │ ├── @babel/plugin-transform-modules-systemjs@7.7.4
│ │ │ │ ├── @babel/plugin-transform-modules-umd@7.7.4
│ │ │ │ ├── @babel/plugin-transform-named-capturing-groups-regex@7.7.4
│ │ │ │ ├── @babel/plugin-transform-new-target@7.7.4
│ │ │ │ ├── @babel/plugin-transform-object-super@7.7.4
│ │ │ │ ├── @babel/plugin-transform-parameters@7.7.7
│ │ │ │ ├── @babel/plugin-transform-property-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-regenerator@7.7.5
│ │ │ │ ├── @babel/plugin-transform-reserved-words@7.7.4
│ │ │ │ ├── @babel/plugin-transform-shorthand-properties@7.7.4
│ │ │ │ ├── @babel/plugin-transform-spread@7.7.4
│ │ │ │ ├── @babel/plugin-transform-sticky-regex@7.7.4
│ │ │ │ ├── @babel/plugin-transform-template-literals@7.7.4
│ │ │ │ ├── @babel/plugin-transform-typeof-symbol@7.7.4
│ │ │ │ ├── @babel/plugin-transform-unicode-regex@7.7.4
│ │ │ │ ├── @babel/types@7.7.4
│ │ │ │ ├── browserslist@4.8.2
│ │ │ │ ├── core-js-compat@3.6.0
│ │ │ │ ├── invariant@2.2.2
│ │ │ │ ├── js-levenshtein@1.1.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_js-levenshtein@1.1.6@js-levenshtein deduped
│ │ │ │ └── semver@5.7.1
│ │ │ ├─┬ UNMET DEPENDENCY @babel/preset-react@7.0.0
│ │ │ │ ├── @babel/helper-plugin-utils@7.0.0
│ │ │ │ ├── @babel/plugin-transform-react-display-name@7.7.4
│ │ │ │ ├── @babel/plugin-transform-react-jsx@7.7.7
│ │ │ │ ├── @babel/plugin-transform-react-jsx-self@7.7.4
│ │ │ │ └── @babel/plugin-transform-react-jsx-source@7.7.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/runtime@7.4.5
│ │ │ │ └── regenerator-runtime@0.13.3
│ │ │ ├─┬ UNMET DEPENDENCY babel-plugin-macros@2.6.1
│ │ │ │ ├── @babel/runtime@7.4.5
│ │ │ │ ├── cosmiconfig@5.2.1
│ │ │ │ └── resolve@1.14.1
│ │ │ ├── UNMET DEPENDENCY babel-plugin-react-require@3.0.0
│ │ │ └── babel-plugin-transform-react-remove-prop-types@0.4.24 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-transform-react-remove-prop-types@0.4.24@babel-plugin-transform-react-remove-prop-types
│ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ └── UNMET DEPENDENCY ms@2.1.2
│ │ ├── UNMET DEPENDENCY dotenv@8.0.0
│ │ ├── is-windows@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-windows@1.0.2@is-windows
│ │ ├── UNMET DEPENDENCY lodash@4.17.13
│ │ ├─┬ react-loadable@5.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-loadable@5.5.0@react-loadable
│ │ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ │ ├─┬ UNMET DEPENDENCY resolve-cwd@3.0.0
│ │ │ └── UNMET DEPENDENCY resolve-from@5.0.0
│ │ ├── UNMET DEPENDENCY semver@6.1.1
│ │ ├─┬ signale@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_signale@1.4.0@signale
│ │ │ ├── UNMET DEPENDENCY ansi-styles@3.2.1
│ │ │ ├── chalk@2.4.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_chalk@2.4.2@chalk
│ │ │ ├─┬ UNMET DEPENDENCY figures@2.0.0
│ │ │ │ └── escape-string-regexp@1.0.5
│ │ │ ├── pkg-conf@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_pkg-conf@2.1.0@pkg-conf
│ │ │ └── UNMET DEPENDENCY supports-color@5.5.0
│ │ ├─┬ UNMET DEPENDENCY umi-build-dev@1.16.10
│ │ │ ├─┬ UNMET DEPENDENCY @babel/code-frame@7.0.0
│ │ │ │ └── @babel/highlight@7.5.0
│ │ │ ├─┬ UNMET DEPENDENCY @babel/generator@7.4.4
│ │ │ │ ├── @babel/types@7.4.4
│ │ │ │ ├── jsesc@2.5.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_jsesc@2.5.2@jsesc deduped
│ │ │ │ ├── lodash@4.17.13
│ │ │ │ ├── source-map@0.5.7
│ │ │ │ └── trim-right@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_trim-right@1.0.1@trim-right
│ │ │ ├── UNMET DEPENDENCY @babel/parser@7.4.5
│ │ │ ├─┬ UNMET DEPENDENCY @babel/runtime@7.4.5
│ │ │ │ └── regenerator-runtime@0.13.2
│ │ │ ├─┬ UNMET DEPENDENCY @babel/template@7.4.4
│ │ │ │ ├── @babel/code-frame@7.0.0
│ │ │ │ ├── @babel/parser@7.4.5
│ │ │ │ └── @babel/types@7.4.4
│ │ │ ├─┬ UNMET DEPENDENCY @babel/traverse@7.4.5
│ │ │ │ ├── @babel/code-frame@7.0.0
│ │ │ │ ├── @babel/generator@7.4.4
│ │ │ │ ├── @babel/helper-function-name@7.7.4
│ │ │ │ ├── @babel/helper-split-export-declaration@7.7.4
│ │ │ │ ├── @babel/parser@7.4.5
│ │ │ │ ├── @babel/types@7.4.4
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── globals@11.12.0
│ │ │ │ └── lodash@4.17.13
│ │ │ ├─┬ UNMET DEPENDENCY @babel/types@7.4.4
│ │ │ │ ├── esutils@2.0.2
│ │ │ │ ├── lodash@4.17.13
│ │ │ │ └── to-fast-properties@2.0.0
│ │ │ ├─┬ UNMET DEPENDENCY af-webpack@1.14.4
│ │ │ │ ├── @babel/core@7.4.5
│ │ │ │ ├── @babel/plugin-transform-react-constant-elements@7.2.0
│ │ │ │ ├── @babel/preset-env@7.4.5
│ │ │ │ ├── @babel/preset-react@7.0.0
│ │ │ │ ├── @babel/preset-typescript@7.3.3
│ │ │ │ ├── @babel/register@7.4.4
│ │ │ │ ├── @babel/runtime@7.4.5
│ │ │ │ ├── @svgr/core@3.1.0
│ │ │ │ ├── address@1.1.0
│ │ │ │ ├── assert@1.4.1
│ │ │ │ ├── autoprefixer@9.6.0
│ │ │ │ ├── babel-eslint@10.0.2
│ │ │ │ ├── babel-loader@8.0.6
│ │ │ │ ├── babel-plugin-dynamic-import-node@2.2.0
│ │ │ │ ├── babel-plugin-named-asset-import@0.3.2
│ │ │ │ ├── babel-preset-umi@1.8.1
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── chokidar@3.0.2
│ │ │ │ ├── clipboardy@2.1.0
│ │ │ │ ├── copy-webpack-plugin@5.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_copy-webpack-plugin@5.0.3@copy-webpack-plugin
│ │ │ │ ├── css-loader@2.1.1
│ │ │ │ ├── css-loader-1@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_css-loader-1@2.0.0@css-loader-1
│ │ │ │ ├── css-modules-typescript-loader@2.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_css-modules-typescript-loader@2.0.4@css-modules-typescript-loader
│ │ │ │ ├── cssnano@4.1.10
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── didyoumean@1.2.1
│ │ │ │ ├── duplicate-package-checker-webpack-plugin@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_duplicate-package-checker-webpack-plugin@3.0.0@duplicate-package-checker-webpack-plugin
│ │ │ │ ├── es5-imcompatible-versions@0.1.57
│ │ │ │ ├── eslint@5.16.0
│ │ │ │ ├── eslint-config-umi@1.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-config-umi@1.6.0@eslint-config-umi
│ │ │ │ ├── eslint-loader@2.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-loader@2.1.2@eslint-loader
│ │ │ │ ├── eslint-plugin-flowtype@2.50.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_eslint-plugin-flowtype@2.50.3@eslint-plugin-flowtype
│ │ │ │ ├── eslint-plugin-import@2.17.3
│ │ │ │ ├── eslint-plugin-jsx-a11y@6.2.1
│ │ │ │ ├── eslint-plugin-react@7.13.0
│ │ │ │ ├── eslint-plugin-react-hooks@1.6.0
│ │ │ │ ├── file-loader@2.0.0
│ │ │ │ ├── fork-ts-checker-webpack-plugin@3.1.1
│ │ │ │ ├── friendly-errors-webpack-plugin@1.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_friendly-errors-webpack-plugin@1.7.0@friendly-errors-webpack-plugin
│ │ │ │ ├── graphql@14.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_graphql@14.3.1@graphql
│ │ │ │ ├── graphql-tag@2.10.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_graphql-tag@2.10.1@graphql-tag
│ │ │ │ ├── inquirer@6.3.1
│ │ │ │ ├── is-plain-object@3.0.0
│ │ │ │ ├── is-root@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-root@2.1.0@is-root
│ │ │ │ ├── less@3.9.0
│ │ │ │ ├── less-loader@5.0.0
│ │ │ │ ├── loader-utils@1.2.3
│ │ │ │ ├── lodash@4.17.13
│ │ │ │ ├── mini-css-extract-plugin@0.7.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_mini-css-extract-plugin@0.7.0@mini-css-extract-plugin deduped
│ │ │ │ ├── pkg-up@3.1.0
│ │ │ │ ├── postcss@7.0.17
│ │ │ │ ├── postcss-flexbugs-fixes@4.1.0
│ │ │ │ ├── postcss-loader@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_postcss-loader@3.0.0@postcss-loader deduped
│ │ │ │ ├── progress-bar-webpack-plugin@1.12.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_progress-bar-webpack-plugin@1.12.1@progress-bar-webpack-plugin
│ │ │ │ ├── react-dev-utils@9.0.1
│ │ │ │ ├── react-error-overlay@5.1.6
│ │ │ │ ├── requireindex@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_requireindex@1.2.0@requireindex deduped
│ │ │ │ ├── resolve@1.11.0
│ │ │ │ ├── rimraf@2.6.3
│ │ │ │ ├── semver@6.1.1
│ │ │ │ ├── sockjs-client@1.3.0
│ │ │ │ ├── speed-measure-webpack-plugin@1.3.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_speed-measure-webpack-plugin@1.3.1@speed-measure-webpack-plugin
│ │ │ │ ├── strip-ansi@5.2.0
│ │ │ │ ├── strip-json-comments@3.0.1
│ │ │ │ ├── style-loader@0.23.1
│ │ │ │ ├── terser-webpack-plugin@1.3.0
│ │ │ │ ├── ts-loader@6.0.3
│ │ │ │ ├── tslint@5.17.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_tslint@5.17.0@tslint
│ │ │ │ ├── tslint-loader@3.5.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_tslint-loader@3.5.4@tslint-loader
│ │ │ │ ├── typescript@3.7.2
│ │ │ │ ├── uglifyjs-webpack-plugin@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_uglifyjs-webpack-plugin@1.3.0@uglifyjs-webpack-plugin
│ │ │ │ ├── umi-url-pnp-loader@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-url-pnp-loader@1.1.2@umi-url-pnp-loader
│ │ │ │ ├── umi-webpack-bundle-analyzer@3.5.0
│ │ │ │ ├── webpack@4.41.1
│ │ │ │ ├── webpack-chain@6.0.0
│ │ │ │ ├── webpack-dev-middleware@3.7.2
│ │ │ │ ├── webpack-dev-server@3.2.1
│ │ │ │ ├── webpack-manifest-plugin@2.0.4
│ │ │ │ ├── webpack-merge@4.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_webpack-merge@4.2.1@webpack-merge
│ │ │ │ └── webpackbar@3.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_webpackbar@3.2.0@webpackbar deduped
│ │ │ ├── babel-plugin-module-resolver@3.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-plugin-module-resolver@3.2.0@babel-plugin-module-resolver
│ │ │ ├── UNMET DEPENDENCY babel-preset-umi@1.8.1
│ │ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ │ ├── ansi-styles@3.2.1
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ └── supports-color@5.5.0
│ │ │ ├── UNMET DEPENDENCY cheerio@1.0.0-rc.3
│ │ │ ├─┬ UNMET DEPENDENCY chokidar@3.0.2
│ │ │ │ ├── anymatch@3.1.1
│ │ │ │ ├── braces@3.0.2
│ │ │ │ ├── fsevents@2.1.2
│ │ │ │ ├── glob-parent@5.1.0
│ │ │ │ ├── is-binary-path@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-binary-path@2.1.0@is-binary-path deduped
│ │ │ │ ├── is-glob@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-glob@4.0.1@is-glob deduped
│ │ │ │ ├── normalize-path@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_normalize-path@3.0.0@normalize-path deduped
│ │ │ │ └── readdirp@3.3.0
│ │ │ ├─┬ UNMET DEPENDENCY clipboardy@2.1.0
│ │ │ │ ├── arch@2.1.1
│ │ │ │ └── execa@1.0.0
│ │ │ ├── UNMET DEPENDENCY core-js@2.6.9
│ │ │ ├── crequire@1.8.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_crequire@1.8.1@crequire
│ │ │ ├── UNMET DEPENDENCY cross-spawn@6.0.5
│ │ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ │ └── ms@2.1.2
│ │ │ ├─┬ UNMET DEPENDENCY decamelize@3.2.0
│ │ │ │ └── xregexp@4.2.4
│ │ │ ├── UNMET DEPENDENCY didyoumean@1.2.1
│ │ │ ├── UNMET DEPENDENCY dotenv@8.0.0
│ │ │ ├── UNMET DEPENDENCY ejs@2.6.2
│ │ │ ├── esprima-extract-comments@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_esprima-extract-comments@1.1.0@esprima-extract-comments
│ │ │ ├── UNMET DEPENDENCY execa@1.0.0
│ │ │ ├── express@4.17.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_express@4.17.1@express deduped
│ │ │ ├── extend2@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_extend2@1.0.0@extend2 deduped
│ │ │ ├── getnpmregistry@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_getnpmregistry@1.0.1@getnpmregistry
│ │ │ ├─┬ UNMET DEPENDENCY git-url-parse@11.1.2
│ │ │ │ └── git-up@4.0.1
│ │ │ ├─┬ UNMET DEPENDENCY glob@7.1.4
│ │ │ │ ├── fs.realpath@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_fs.realpath@<EMAIL> deduped
│ │ │ │ ├── inflight@1.0.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_inflight@1.0.6@inflight deduped
│ │ │ │ ├── inherits@2.0.3
│ │ │ │ ├── minimatch@3.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_minimatch@3.0.4@minimatch deduped
│ │ │ │ ├── once@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_once@1.4.0@once deduped
│ │ │ │ └── path-is-absolute@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_path-is-absolute@1.0.1@path-is-absolute deduped
│ │ │ ├── got@9.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_got@9.6.0@got deduped
│ │ │ ├── html-minifier@4.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_html-minifier@4.0.0@html-minifier
│ │ │ ├─┬ UNMET DEPENDENCY http-proxy-middleware@0.19.1
│ │ │ │ ├── http-proxy@1.18.0
│ │ │ │ ├── is-glob@4.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-glob@4.0.1@is-glob deduped
│ │ │ │ ├── lodash@4.17.15
│ │ │ │ └── micromatch@3.1.10
│ │ │ ├── UNMET DEPENDENCY js-yaml@3.13.1
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.13
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ ├── UNMET DEPENDENCY mustache@3.0.1
│ │ │ ├─┬ UNMET DEPENDENCY ora@3.4.0
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── cli-cursor@2.1.0
│ │ │ │ ├── cli-spinners@2.2.0
│ │ │ │ ├── log-symbols@2.2.0
│ │ │ │ ├── strip-ansi@5.2.0
│ │ │ │ └── wcwidth@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_wcwidth@1.0.1@wcwidth
│ │ │ ├─┬ UNMET DEPENDENCY path-to-regexp@1.7.0
│ │ │ │ └── isarray@0.0.1
│ │ │ ├─┬ UNMET DEPENDENCY portfinder@1.0.21
│ │ │ │ ├── async@1.5.2
│ │ │ │ ├── debug@2.6.9
│ │ │ │ └── mkdirp@0.5.1
│ │ │ ├── UNMET DEPENDENCY prettier@1.18.2
│ │ │ ├── random-color@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_random-color@1.0.1@random-color
│ │ │ ├── UNMET DEPENDENCY react@16.12.0
│ │ │ ├─┬ UNMET DEPENDENCY react-dom@16.12.0
│ │ │ │ ├── loose-envify@1.3.1
│ │ │ │ ├── object-assign@4.1.1
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ └── scheduler@0.18.0
│ │ │ ├─┬ UNMET DEPENDENCY react-router@5.1.2
│ │ │ │ ├── @babel/runtime@7.4.5
│ │ │ │ ├── history@4.10.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_history@4.10.1@history deduped
│ │ │ │ ├── hoist-non-react-statics@3.3.0
│ │ │ │ ├── loose-envify@1.3.1
│ │ │ │ ├── mini-create-react-context@0.3.2
│ │ │ │ ├── path-to-regexp@1.7.0
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react-is@16.8.3
│ │ │ │ ├── tiny-invariant@1.0.6
│ │ │ │ └── tiny-warning@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_tiny-warning@1.0.3@tiny-warning deduped
│ │ │ ├─┬ UNMET DEPENDENCY react-router-config@5.1.1
│ │ │ │ └── @babel/runtime@7.4.5
│ │ │ ├─┬ UNMET DEPENDENCY react-router-dom@5.1.2
│ │ │ │ ├── @babel/runtime@7.4.5
│ │ │ │ ├── history@4.10.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_history@4.10.1@history deduped
│ │ │ │ ├── loose-envify@1.3.1
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── react-router@5.1.2
│ │ │ │ ├── tiny-invariant@1.0.6
│ │ │ │ └── tiny-warning@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_tiny-warning@1.0.3@tiny-warning deduped
│ │ │ ├── UNMET DEPENDENCY regenerator-runtime@0.13.2
│ │ │ ├── requireindex@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_requireindex@1.2.0@requireindex
│ │ │ ├─┬ UNMET DEPENDENCY resolve@1.11.0
│ │ │ │ └── path-parse@1.0.6
│ │ │ ├─┬ UNMET DEPENDENCY rimraf@2.6.3
│ │ │ │ └── glob@7.1.4
│ │ │ ├── UNMET DEPENDENCY semver@6.1.1
│ │ │ ├── UNMET DEPENDENCY serialize-javascript@2.1.1
│ │ │ ├── serve-static@1.14.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_serve-static@1.14.1@serve-static deduped
│ │ │ ├── signale@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_signale@1.4.0@signale deduped
│ │ │ ├─┬ UNMET DEPENDENCY sockjs@0.3.19
│ │ │ │ ├── faye-websocket@0.10.0
│ │ │ │ └── uuid@3.3.3
│ │ │ ├─┬ UNMET DEPENDENCY sort-package-json@1.31.0
│ │ │ │ ├── detect-indent@6.0.0
│ │ │ │ ├── detect-newline@3.1.0
│ │ │ │ ├── glob@7.1.6
│ │ │ │ └── sort-object-keys@1.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_sort-object-keys@1.1.3@sort-object-keys
│ │ │ ├── stringify-object@3.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_stringify-object@3.3.0@stringify-object deduped
│ │ │ ├── UNMET DEPENDENCY sylvanas@0.4.3
│ │ │ ├── terminal-link@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_terminal-link@1.3.0@terminal-link deduped
│ │ │ ├── UNMET DEPENDENCY umi-core@1.9.4
│ │ │ ├── umi-history@0.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-history@0.1.2@umi-history
│ │ │ ├─┬ UNMET DEPENDENCY umi-mock@2.1.3
│ │ │ │ ├── body-parser@1.19.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_body-parser@1.19.0@body-parser deduped
│ │ │ │ ├── chokidar@3.0.2
│ │ │ │ ├── glob@7.1.4
│ │ │ │ ├── multer@1.4.2
│ │ │ │ ├── path-to-regexp@1.7.0
│ │ │ │ ├── signale@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_signale@1.4.0@signale deduped
│ │ │ │ └── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ │ ├── umi-notify@0.1.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-notify@0.1.5@umi-notify
│ │ │ ├── UNMET DEPENDENCY umi-plugin-ui@1.4.10
│ │ │ ├── UNMET DEPENDENCY umi-test@1.9.0
│ │ │ ├─┬ UNMET DEPENDENCY umi-uni18n@1.1.6
│ │ │ │ ├── @babel/core@7.7.7
│ │ │ │ ├── @babel/generator@7.7.7
│ │ │ │ ├── @babel/parser@7.7.7
│ │ │ │ ├── @babel/preset-env@7.7.7
│ │ │ │ ├── @babel/preset-typescript@7.7.4
│ │ │ │ ├── @babel/traverse@7.7.4
│ │ │ │ ├── @umijs/fabric@1.2.12
│ │ │ │ ├── babel-types@6.26.0
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── eslint@5.16.0
│ │ │ │ ├── glob@7.1.6
│ │ │ │ ├── import-fresh@3.2.1
│ │ │ │ ├── lodash.groupby@4.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.groupby@<EMAIL> deduped
│ │ │ │ ├── node-eval@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_node-eval@2.0.0@node-eval
│ │ │ │ ├── node-import-ts@1.0.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_node-import-ts@1.0.5@node-import-ts
│ │ │ │ ├── ora@3.4.0
│ │ │ │ ├── prettier@1.19.1
│ │ │ │ ├── semver@6.3.0
│ │ │ │ ├── typescript@3.7.4
│ │ │ │ ├── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ │ │ └── yargs-parser@13.1.1
│ │ │ ├── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ │ ├── uppercamelcase@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_uppercamelcase@3.0.0@uppercamelcase deduped
│ │ │ ├── url-polyfill@1.1.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_url-polyfill@1.1.5@url-polyfill
│ │ │ ├── user-home@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_user-home@2.0.0@user-home deduped
│ │ │ ├── webpack-node-externals@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_webpack-node-externals@1.7.2@webpack-node-externals
│ │ │ ├── UNMET DEPENDENCY whatwg-fetch@3.0.0
│ │ │ ├── write-file-webpack-plugin@4.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_write-file-webpack-plugin@4.5.0@write-file-webpack-plugin
│ │ │ └─┬ UNMET DEPENDENCY yeoman-generator@4.0.1
│ │ │   ├── async@2.6.3
│ │ │   ├── chalk@2.4.2
│ │ │   ├── cli-table@0.3.1
│ │ │   ├── cross-spawn@6.0.5
│ │ │   ├── dargs@6.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_dargs@6.1.0@dargs
│ │ │   ├── dateformat@3.0.3
│ │ │   ├── debug@4.1.1
│ │ │   ├── detect-conflict@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_detect-conflict@1.0.1@detect-conflict
│ │ │   ├── error@7.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_error@7.2.1@error
│ │ │   ├── find-up@3.0.0
│ │ │   ├── github-username@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_github-username@3.0.0@github-username
│ │ │   ├── istextorbinary@2.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_istextorbinary@2.6.0@istextorbinary
│ │ │   ├── lodash@4.17.15
│ │ │   ├── make-dir@3.0.0
│ │ │   ├── mem-fs-editor@6.0.0
│ │ │   ├── minimist@1.2.0
│ │ │   ├── pretty-bytes@5.3.0
│ │ │   ├── read-chunk@3.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_read-chunk@3.2.0@read-chunk
│ │ │   ├── read-pkg-up@5.0.0
│ │ │   ├── rimraf@2.7.1
│ │ │   ├── run-async@2.3.0
│ │ │   ├── shelljs@0.8.3
│ │ │   ├── text-table@0.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_text-table@0.2.0@text-table deduped
│ │ │   ├── through2@3.0.1
│ │ │   └── yeoman-environment@2.7.0
│ │ ├── UNMET DEPENDENCY umi-core@1.9.4
│ │ ├─┬ UNMET DEPENDENCY umi-ui@1.3.10
│ │ │ ├── @umijs/launch-editor@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_@umijs_launch-editor@1.0.1@@umijs/launch-editor
│ │ │ ├── binary-mirror-config@1.20.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_binary-mirror-config@1.20.3@binary-mirror-config
│ │ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ │ ├── ansi-styles@3.2.1
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ └── supports-color@5.5.0
│ │ │ ├── clear-module@4.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_clear-module@4.0.0@clear-module
│ │ │ ├─┬ UNMET DEPENDENCY compression@1.7.4
│ │ │ │ ├── accepts@1.3.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_accepts@1.3.7@accepts deduped
│ │ │ │ ├── bytes@3.0.0
│ │ │ │ ├── compressible@2.0.17
│ │ │ │ ├── debug@2.6.9
│ │ │ │ ├── on-headers@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_on-headers@1.0.2@on-headers
│ │ │ │ ├── safe-buffer@5.1.2
│ │ │ │ └── vary@1.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_vary@1.1.2@vary deduped
│ │ │ ├── UNMET DEPENDENCY cross-spawn@6.0.5
│ │ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ │ └── ms@2.1.2
│ │ │ ├── empty-dir@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_empty-dir@2.0.0@empty-dir
│ │ │ ├── express@4.17.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_express@4.17.1@express deduped
│ │ │ ├── got@9.6.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_got@9.6.0@got deduped
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── macaddress@0.2.9 -> /Users/<USER>/git/orange/orangeview/node_modules/_macaddress@0.2.9@macaddress deduped
│ │ │ ├── UNMET DEPENDENCY mkdirp@0.5.1
│ │ │ ├─┬ UNMET DEPENDENCY node-notifier@5.4.3
│ │ │ │ ├── growly@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_growly@1.3.0@growly
│ │ │ │ ├── is-wsl@1.1.0
│ │ │ │ ├── semver@5.7.1
│ │ │ │ ├── shellwords@0.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_shellwords@0.1.1@shellwords
│ │ │ │ └── which@1.3.1
│ │ │ ├─┬ UNMET OPTIONAL DEPENDENCY node-pty@0.10.0-beta3
│ │ │ │ └── nan@2.14.0
│ │ │ ├─┬ UNMET DEPENDENCY portfinder@1.0.21
│ │ │ │ ├── async@1.5.2
│ │ │ │ ├── debug@2.6.9
│ │ │ │ └── mkdirp@0.5.1
│ │ │ ├─┬ UNMET DEPENDENCY react-dev-utils@9.0.1
│ │ │ │ ├── @babel/code-frame@7.0.0
│ │ │ │ ├── address@1.0.3
│ │ │ │ ├── browserslist@4.5.4
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── cross-spawn@6.0.5
│ │ │ │ ├── detect-port-alt@1.1.6
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ ├── filesize@3.6.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_filesize@3.6.1@filesize deduped
│ │ │ │ ├── find-up@3.0.0
│ │ │ │ ├── fork-ts-checker-webpack-plugin@1.1.1
│ │ │ │ ├── global-modules@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_global-modules@2.0.0@global-modules deduped
│ │ │ │ ├── globby@8.0.2
│ │ │ │ ├── gzip-size@5.0.0
│ │ │ │ ├── immer@1.10.0
│ │ │ │ ├── inquirer@6.2.2
│ │ │ │ ├── is-root@2.0.0
│ │ │ │ ├── loader-utils@1.2.3
│ │ │ │ ├── opn@5.4.0
│ │ │ │ ├── pkg-up@2.0.0
│ │ │ │ ├── react-error-overlay@5.1.6
│ │ │ │ ├── recursive-readdir@2.2.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_recursive-readdir@2.2.2@recursive-readdir
│ │ │ │ ├── shell-quote@1.6.1
│ │ │ │ ├── sockjs-client@1.3.0
│ │ │ │ ├── strip-ansi@5.2.0
│ │ │ │ └── text-table@0.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_text-table@0.2.0@text-table deduped
│ │ │ ├── UNMET DEPENDENCY regenerator-runtime@0.13.2
│ │ │ ├─┬ UNMET DEPENDENCY resolve-cwd@3.0.0
│ │ │ │ └── resolve-from@5.0.0
│ │ │ ├── UNMET DEPENDENCY resolve-from@5.0.0
│ │ │ ├─┬ UNMET DEPENDENCY rimraf@3.0.0
│ │ │ │ └── glob@7.1.6
│ │ │ ├── UNMET DEPENDENCY semver@6.3.0
│ │ │ ├── UNMET DEPENDENCY sockjs@0.3.19
│ │ │ ├── UNMET DEPENDENCY umi-build-dev@1.16.10
│ │ │ ├─┬ UNMET DEPENDENCY umi-ui-theme@1.2.1
│ │ │ │ └── less-vars-to-js@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_less-vars-to-js@1.3.0@less-vars-to-js
│ │ │ └── user-home@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_user-home@2.0.0@user-home deduped
│ │ ├── umi-utils@1.7.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_umi-utils@1.7.2@umi-utils deduped
│ │ ├─┬ UNMET DEPENDENCY update-notifier@3.0.0
│ │ │ ├─┬ UNMET DEPENDENCY boxen@3.2.0
│ │ │ │ ├── ansi-align@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_ansi-align@3.0.0@ansi-align
│ │ │ │ ├── camelcase@5.3.1
│ │ │ │ ├── chalk@2.4.2
│ │ │ │ ├── cli-boxes@2.2.0
│ │ │ │ ├── string-width@3.1.0
│ │ │ │ ├── term-size@1.2.0
│ │ │ │ ├── type-fest@0.3.1
│ │ │ │ └── widest-line@2.0.1
│ │ │ ├─┬ UNMET DEPENDENCY chalk@2.4.2
│ │ │ │ ├── ansi-styles@3.2.1
│ │ │ │ ├── escape-string-regexp@1.0.5
│ │ │ │ └── supports-color@5.5.0
│ │ │ ├── configstore@4.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_configstore@4.0.0@configstore
│ │ │ ├── has-yarn@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_has-yarn@2.1.0@has-yarn
│ │ │ ├── UNMET DEPENDENCY import-lazy@2.1.0
│ │ │ ├── is-ci@2.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-ci@2.0.0@is-ci
│ │ │ ├── is-installed-globally@0.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-installed-globally@0.1.0@is-installed-globally
│ │ │ ├── is-npm@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-npm@3.0.0@is-npm
│ │ │ ├── is-yarn-global@0.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_is-yarn-global@0.3.0@is-yarn-global
│ │ │ ├── latest-version@5.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_latest-version@5.1.0@latest-version
│ │ │ ├── semver-diff@2.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_semver-diff@2.1.0@semver-diff deduped
│ │ │ └── xdg-basedir@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_xdg-basedir@3.0.0@xdg-basedir deduped
│ │ └─┬ UNMET DEPENDENCY yargs-parser@13.1.1
│ │   ├── UNMET DEPENDENCY camelcase@5.3.1
│ │   └── UNMET DEPENDENCY decamelize@1.2.0
│ ├─┬ UNMET DEPENDENCY urllib@2.34.1
│ │ ├── any-promise@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_any-promise@1.3.0@any-promise
│ │ ├── content-type@1.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_content-type@1.0.4@content-type
│ │ ├─┬ UNMET DEPENDENCY debug@2.6.9
│ │ │ └── UNMET DEPENDENCY ms@2.0.0
│ │ ├─┬ default-user-agent@1.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_default-user-agent@1.0.0@default-user-agent
│ │ │ └── os-name@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_os-name@1.0.3@os-name
│ │ ├─┬ digest-header@0.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_digest-header@0.0.1@digest-header
│ │ │ └── utility@0.1.11 -> /Users/<USER>/git/orange/orangeview/node_modules/_utility@0.1.11@utility
│ │ ├── ee-first@1.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_ee-first@1.1.1@ee-first
│ │ ├─┬ UNMET DEPENDENCY formstream@1.1.0
│ │ │ ├── destroy@1.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_destroy@1.0.4@destroy
│ │ │ ├── UNMET DEPENDENCY mime@1.6.0
│ │ │ └── pause-stream@0.0.11 -> /Users/<USER>/git/orange/orangeview/node_modules/_pause-stream@0.0.11@pause-stream
│ │ ├── humanize-ms@1.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_humanize-ms@1.2.1@humanize-ms deduped
│ │ ├── UNMET DEPENDENCY iconv-lite@0.4.19
│ │ ├── ip@1.1.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_ip@1.1.5@ip
│ │ ├─┬ UNMET DEPENDENCY proxy-agent@3.1.1
│ │ │ ├─┬ UNMET DEPENDENCY agent-base@4.3.0
│ │ │ │ └── es6-promisify@5.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_es6-promisify@5.0.0@es6-promisify
│ │ │ ├─┬ UNMET DEPENDENCY debug@4.1.1
│ │ │ │ └── ms@2.1.2
│ │ │ ├─┬ UNMET DEPENDENCY http-proxy-agent@2.1.0
│ │ │ │ ├── agent-base@4.3.0
│ │ │ │ └── debug@3.1.0
│ │ │ ├─┬ UNMET DEPENDENCY https-proxy-agent@3.0.1
│ │ │ │ ├── agent-base@4.3.0
│ │ │ │ └── debug@3.1.0
│ │ │ ├─┬ UNMET DEPENDENCY lru-cache@5.1.1
│ │ │ │ └── yallist@3.1.1
│ │ │ ├─┬ UNMET DEPENDENCY pac-proxy-agent@3.0.1
│ │ │ │ ├── agent-base@4.3.0
│ │ │ │ ├── debug@4.1.1
│ │ │ │ ├── get-uri@2.0.4
│ │ │ │ ├── http-proxy-agent@2.1.0
│ │ │ │ ├── https-proxy-agent@3.0.1
│ │ │ │ ├── pac-resolver@3.0.0
│ │ │ │ ├── raw-body@2.4.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_raw-body@2.4.1@raw-body
│ │ │ │ └── socks-proxy-agent@4.0.2
│ │ │ ├── UNMET DEPENDENCY proxy-from-env@1.0.0
│ │ │ └─┬ UNMET DEPENDENCY socks-proxy-agent@4.0.2
│ │ │   ├── agent-base@4.2.1
│ │ │   └── socks@2.3.3
│ │ ├─┬ pump@3.0.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_pump@3.0.0@pump
│ │ │ ├── end-of-stream@1.4.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_end-of-stream@1.4.4@end-of-stream
│ │ │ └── once@1.4.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_once@1.4.0@once deduped
│ │ ├── UNMET DEPENDENCY qs@6.5.1
│ │ ├── statuses@1.5.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_statuses@1.5.0@statuses
│ │ └── UNMET DEPENDENCY utility@1.16.3
│ └─┬ UNMET DEPENDENCY yargs-parser@10.1.0
│   └── UNMET DEPENDENCY camelcase@4.1.0
├─┬ UNMET DEPENDENCY @ant-design/pro-layout@4.9.11
│ ├─┬ UNMET DEPENDENCY antd@3.26.3
│ │ ├── @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context deduped
│ │ ├── UNMET DEPENDENCY @ant-design/icons@2.1.1
│ │ ├─┬ UNMET DEPENDENCY @ant-design/icons-react@2.0.1
│ │ │ ├── UNMET DEPENDENCY @ant-design/colors@3.2.2
│ │ │ └── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├── UNMET DEPENDENCY @types/react-slick@0.23.4
│ │ ├── UNMET DEPENDENCY array-tree-filter@2.1.0
│ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ ├── UNMET DEPENDENCY copy-to-clipboard@3.2.0
│ │ ├─┬ UNMET DEPENDENCY css-animation@1.6.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ └── UNMET DEPENDENCY component-classes@1.2.6
│ │ ├── UNMET DEPENDENCY dom-closest@0.2.0
│ │ ├── UNMET DEPENDENCY enquire.js@2.1.6
│ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ ├─┬ UNMET DEPENDENCY omit.js@1.0.2
│ │ │ └── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ ├─┬ UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY loose-envify@1.4.0
│ │ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ │ └── UNMET DEPENDENCY react-is@16.8.3
│ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ ├─┬ UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY css-animation@1.6.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-calendar@9.15.8
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-cascader@0.17.5
│ │ │ ├── UNMET DEPENDENCY array-tree-filter@2.1.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallow-equal@1.1.0
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├── UNMET DEPENDENCY rc-checkbox@2.1.6
│ │ ├─┬ UNMET DEPENDENCY rc-collapse@1.11.7
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY css-animation@1.6.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY react-is@16.8.3
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├─┬ UNMET DEPENDENCY rc-dialog@7.6.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ └── UNMET DEPENDENCY rc-util@4.16.2
│ │ ├─┬ UNMET DEPENDENCY rc-drawer@3.1.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├── UNMET DEPENDENCY rc-dropdown@2.4.1
│ │ ├─┬ UNMET DEPENDENCY rc-editor-mention@1.1.13
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY draft-js@0.10.5
│ │ │ ├── UNMET DEPENDENCY immutable@3.7.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ └── UNMET DEPENDENCY rc-editor-core@0.8.9
│ │ ├─┬ UNMET DEPENDENCY rc-form@2.4.11
│ │ │ ├── UNMET DEPENDENCY async-validator@1.11.5
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY create-react-class@15.6.3
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY hoist-non-react-statics@3.3.0
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-input-number@4.5.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ └── UNMET DEPENDENCY rmc-feedback@2.0.0
│ │ ├── UNMET DEPENDENCY rc-mentions@0.4.1
│ │ ├─┬ UNMET DEPENDENCY rc-menu@7.5.3
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY mini-store@2.0.0
│ │ │ ├── UNMET DEPENDENCY mutationobserver-shim@0.3.3
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├── UNMET DEPENDENCY rc-notification@3.3.1
│ │ ├─┬ UNMET DEPENDENCY rc-pagination@1.20.11
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY rc-progress@2.5.2
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ └── UNMET DEPENDENCY prop-types@15.7.2
│ │ ├── UNMET DEPENDENCY rc-rate@2.5.0
│ │ ├── rc-resize-observer@0.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_rc-resize-observer@0.1.3@rc-resize-observer deduped
│ │ ├─┬ UNMET DEPENDENCY rc-select@9.2.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY component-classes@1.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-menu@7.5.3
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-slider@8.7.1
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-tooltip@3.7.3
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-steps@3.5.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ └── UNMET DEPENDENCY prop-types@15.7.2
│ │ ├── UNMET DEPENDENCY rc-switch@1.9.0
│ │ ├─┬ UNMET DEPENDENCY rc-table@6.10.7
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY component-classes@1.2.6
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY mini-store@2.0.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├─┬ UNMET DEPENDENCY rc-tabs@9.7.0
│ │ │ ├── @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context deduped
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY lodash@4.17.15
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-hammerjs@0.6.9
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-time-picker@3.7.3
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY moment@2.24.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ │ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├── UNMET DEPENDENCY rc-tooltip@3.7.3
│ │ ├─┬ UNMET DEPENDENCY rc-tree@2.1.3
│ │ │ ├── @ant-design/create-react-context@0.2.5 -> /Users/<USER>/git/orange/orangeview/node_modules/_@ant-design_create-react-context@0.2.5@@ant-design/create-react-context deduped
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-tree-select@2.9.4
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY dom-scroll-into-view@1.2.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY raf@3.4.1
│ │ │ ├── UNMET DEPENDENCY rc-animate@2.10.2
│ │ │ ├── UNMET DEPENDENCY rc-tree@2.1.3
│ │ │ ├─┬ UNMET DEPENDENCY rc-trigger@3.0.0
│ │ │ │ ├── babel-runtime@6.26.0
│ │ │ │ ├── classnames@2.2.6
│ │ │ │ ├── prop-types@15.7.2
│ │ │ │ ├── raf@3.4.1
│ │ │ │ ├── rc-align@2.4.5
│ │ │ │ ├── rc-animate@3.0.0-rc.6
│ │ │ │ └── rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├── UNMET DEPENDENCY rc-trigger@2.6.2
│ │ ├─┬ UNMET DEPENDENCY rc-upload@2.9.4
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ └── UNMET DEPENDENCY warning@4.0.3
│ │ ├─┬ UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY add-dom-event-listener@1.1.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ ├── UNMET DEPENDENCY react-lazy-load@3.0.13
│ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ ├─┬ UNMET DEPENDENCY react-slick@0.25.2
│ │ │ ├── UNMET DEPENDENCY classnames@2.2.6
│ │ │ ├── UNMET DEPENDENCY enquire.js@2.1.6
│ │ │ ├── UNMET DEPENDENCY json2mq@0.2.0
│ │ │ ├── UNMET DEPENDENCY lodash.debounce@4.0.8
│ │ │ └── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ ├── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ │ ├── UNMET DEPENDENCY shallowequal@1.1.0
│ │ └── UNMET DEPENDENCY warning@4.0.3
│ ├── UNMET DEPENDENCY classnames@2.2.6
│ ├─┬ hash.js@1.1.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_hash.js@<EMAIL>
│ │ ├── UNMET DEPENDENCY inherits@2.0.3
│ │ └── minimalistic-assert@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_minimalistic-assert@1.0.1@minimalistic-assert
│ ├─┬ UNMET DEPENDENCY history@4.10.1
│ │ ├─┬ UNMET DEPENDENCY @babel/runtime@7.7.7
│ │ │ └── UNMET DEPENDENCY regenerator-runtime@0.13.3
│ │ ├─┬ UNMET DEPENDENCY loose-envify@1.4.0
│ │ │ └── UNMET DEPENDENCY js-tokens@3.0.2
│ │ ├── UNMET DEPENDENCY resolve-pathname@3.0.0
│ │ ├── UNMET DEPENDENCY tiny-invariant@1.0.6
│ │ ├── tiny-warning@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_tiny-warning@1.0.3@tiny-warning
│ │ └── UNMET DEPENDENCY value-equal@1.0.1
│ ├── UNMET DEPENDENCY lodash@4.17.15
│ ├── UNMET DEPENDENCY memoize-one@5.1.1
│ ├─┬ UNMET DEPENDENCY omit.js@1.0.0
│ │ └── UNMET DEPENDENCY babel-runtime@6.26.0
│ ├── UNMET DEPENDENCY path-to-regexp@2.4.0
│ ├── UNMET DEPENDENCY qs@6.9.1
│ ├─┬ rc-resize-observer@0.1.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_rc-resize-observer@0.1.3@rc-resize-observer
│ │ ├── UNMET DEPENDENCY classnames@2.2.5
│ │ ├─┬ UNMET DEPENDENCY rc-util@4.16.2
│ │ │ ├── UNMET DEPENDENCY add-dom-event-listener@1.1.0
│ │ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ │ ├── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
│ │ │ └── UNMET DEPENDENCY shallowequal@1.1.0
│ │ └── UNMET DEPENDENCY resize-observer-polyfill@1.5.1
│ ├─┬ UNMET DEPENDENCY react-copy-to-clipboard@5.0.2
│ │ ├── UNMET DEPENDENCY copy-to-clipboard@3.2.0
│ │ └── UNMET DEPENDENCY prop-types@15.6.0
│ ├─┬ react-helmet@5.2.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-helmet@5.2.1@react-helmet
│ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ ├── UNMET DEPENDENCY prop-types@15.6.0
│ │ ├── UNMET DEPENDENCY react-fast-compare@2.0.4
│ │ └─┬ react-side-effect@1.2.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_react-side-effect@1.2.0@react-side-effect
│ │   └── UNMET DEPENDENCY shallowequal@1.1.0
│ ├─┬ UNMET DEPENDENCY react-responsive@8.0.1
│ │ ├── UNMET DEPENDENCY hyphenate-style-name@1.0.3
│ │ ├─┬ UNMET DEPENDENCY matchmediaquery@0.3.0
│ │ │ └── css-mediaquery@0.1.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_css-mediaquery@0.1.2@css-mediaquery
│ │ ├─┬ UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├─┬ UNMET DEPENDENCY loose-envify@1.4.0
│ │ │ │ └── js-tokens@3.0.2
│ │ │ ├── UNMET DEPENDENCY object-assign@4.1.1
│ │ │ └── UNMET DEPENDENCY react-is@16.8.3
│ │ └── UNMET DEPENDENCY shallow-equal@1.1.0
│ ├─┬ UNMET DEPENDENCY react-router-dom@5.1.2
│ │ ├── UNMET DEPENDENCY @babel/runtime@7.7.7
│ │ ├── UNMET DEPENDENCY history@4.10.1
│ │ ├── UNMET DEPENDENCY loose-envify@1.4.0
│ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ ├─┬ UNMET DEPENDENCY react-router@5.1.2
│ │ │ ├── UNMET DEPENDENCY @babel/runtime@7.7.7
│ │ │ ├── UNMET DEPENDENCY history@4.10.1
│ │ │ ├── UNMET DEPENDENCY hoist-non-react-statics@3.3.0
│ │ │ ├── UNMET DEPENDENCY loose-envify@1.4.0
│ │ │ ├─┬ UNMET DEPENDENCY mini-create-react-context@0.3.2
│ │ │ │ ├── @babel/runtime@7.7.7
│ │ │ │ ├── gud@1.0.0
│ │ │ │ └── tiny-warning@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_tiny-warning@1.0.3@tiny-warning deduped
│ │ │ ├─┬ UNMET DEPENDENCY path-to-regexp@1.8.0
│ │ │ │ └── isarray@0.0.1
│ │ │ ├── UNMET DEPENDENCY prop-types@15.7.2
│ │ │ ├── UNMET DEPENDENCY react-is@16.8.3
│ │ │ ├── UNMET DEPENDENCY tiny-invariant@1.0.6
│ │ │ └── tiny-warning@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_tiny-warning@1.0.3@tiny-warning deduped
│ │ ├── UNMET DEPENDENCY tiny-invariant@1.0.6
│ │ └── tiny-warning@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_tiny-warning@1.0.3@tiny-warning deduped
│ ├── unstated-next@1.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_unstated-next@1.1.0@unstated-next
│ ├── UNMET DEPENDENCY use-merge-value@1.0.1
│ └─┬ UNMET DEPENDENCY warning@4.0.3
│   └── UNMET DEPENDENCY loose-envify@1.4.0
├── @mdx-js/mdxast@0.16.8 -> /Users/<USER>/git/orange/orangeview/node_modules/_@mdx-js_mdxast@0.16.8@@mdx-js/mdxast extraneous
├── UNMET DEPENDENCY axios@0.17.1
├── babel-preset-docz@0.13.6 -> /Users/<USER>/git/orange/orangeview/node_modules/_babel-preset-docz@0.13.6@babel-preset-docz extraneous
├── browser-stdout@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_browser-stdout@1.3.0@browser-stdout extraneous
├── UNMET DEPENDENCY classnames@2.2.5
├── UNMET DEPENDENCY codemirror@5.49.2
├── css-animation@2.0.4 -> /Users/<USER>/git/orange/orangeview/node_modules/_css-animation@2.0.4@css-animation
├─┬ UNMET DEPENDENCY dva-model-extend@0.1.2
│ └─┬ UNMET DEPENDENCY babel-runtime@6.26.0
│   ├── UNMET DEPENDENCY core-js@2.5.3
│   └── UNMET DEPENDENCY regenerator-runtime@0.11.1
├─┬ UNMET DEPENDENCY echarts@4.5.0
│ └── UNMET DEPENDENCY zrender@4.1.2
├─┬ UNMET DEPENDENCY echarts-for-react@2.0.14
│ ├── UNMET DEPENDENCY fast-deep-equal@2.0.1
│ └── UNMET DEPENDENCY size-sensor@0.2.5
├── UNMET DEPENDENCY file-entry-cache@2.0.0
├── graceful-readlink@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_graceful-readlink@1.0.1@graceful-readlink extraneous
├── growl@1.9.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_growl@1.9.2@growl extraneous
├── happypack@5.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_happypack@5.0.1@happypack extraneous
├── UNMET DEPENDENCY is-descriptor@0.1.6
├── UNMET DEPENDENCY js-base64@2.5.1
├── koa-range@0.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_koa-range@0.3.0@koa-range extraneous
├── left-pad@1.3.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_left-pad@1.3.0@left-pad extraneous
├─┬ lodash-decorators@6.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash-decorators@6.0.1@lodash-decorators
│ └── UNMET DEPENDENCY tslib@1.10.0
├── lodash.create@3.1.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_lodash.create@<EMAIL> extraneous
├── memoize-one@4.1.0 -> /Users/<USER>/git/orange/orangeview/node_modules/_memoize-one@4.1.0@memoize-one
├── UNMET DEPENDENCY moment@2.20.1
├── UNMET DEPENDENCY nzh@1.0.4
├── UNMET DEPENDENCY path-to-regexp@2.1.0
├── UNMET DEPENDENCY pluralize@7.0.0
├── UNMET DEPENDENCY postcss-sorting@4.1.0
├── progress-estimator@0.2.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_progress-estimator@0.2.2@progress-estimator extraneous
├─┬ UNMET DEPENDENCY qrcode.react@0.7.2
│ ├── UNMET DEPENDENCY prop-types@15.6.0
│ └── UNMET DEPENDENCY qr.js@0.0.0
├── UNMET DEPENDENCY qs@6.5.1
├─┬ UNMET DEPENDENCY rc-animate@2.6.0
│ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ ├── UNMET DEPENDENCY classnames@2.2.6
│ ├─┬ UNMET DEPENDENCY css-animation@1.6.1
│ │ ├── UNMET DEPENDENCY babel-runtime@6.26.0
│ │ └─┬ UNMET DEPENDENCY component-classes@1.2.6
│ │   └── UNMET DEPENDENCY component-indexof@0.0.3
│ ├── UNMET DEPENDENCY prop-types@15.6.0
│ ├─┬ UNMET DEPENDENCY raf@3.4.1
│ │ └── UNMET DEPENDENCY performance-now@2.1.0
│ └── UNMET DEPENDENCY react-lifecycles-compat@3.0.4
├── UNMET DEPENDENCY react-docgen-typescript-loader@3.6.0
├── UNMET DEPENDENCY regexpp@1.1.0
├── require-uncached@1.0.3 -> /Users/<USER>/git/orange/orangeview/node_modules/_require-uncached@1.0.3@require-uncached extraneous
├── sigmund@1.0.1 -> /Users/<USER>/git/orange/orangeview/node_modules/_sigmund@1.0.1@sigmund extraneous
├── UNMET DEPENDENCY sort-keys@1.1.2
├── toposort@1.0.7 -> /Users/<USER>/git/orange/orangeview/node_modules/_toposort@1.0.7@toposort extraneous
└── uglify-to-browserify@1.0.2 -> /Users/<USER>/git/orange/orangeview/node_modules/_uglify-to-browserify@1.0.2@uglify-to-browserify extraneous

