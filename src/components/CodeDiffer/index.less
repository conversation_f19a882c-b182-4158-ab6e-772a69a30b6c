.emasd-codediffer {
  @padLeft: 16px;
  @ttHeight: 48px;
  @padTop: 8px;
  padding-top: 8px;

  .clearfix {
    &:before,
    &:after {
      content: ' ';
      display: table;
    }

    &:after {
      clear: both;
    }
  }

  .title {
    .clearfix;
    border: 1px solid #e6e8ea;
    border-bottom: none;
    height: @ttHeight;
    line-height: @ttHeight;
    font-size: 14px;
    color: #1d2c41;

    .dataTitle {
      float: left;
      width: 53%;
      padding-left: @padLeft;
      box-sizing: border-box;
      border-right: 1px solid #e6e8ea;
    }

    .compareTitle {
      float: right;
      width: 47%;
      padding-left: @padLeft;
      box-sizing: border-box;
    }
  }
}
