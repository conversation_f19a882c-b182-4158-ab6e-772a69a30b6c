import React from 'react';
import PropTypes from 'prop-types';
import CodeMirror from 'codemirror';
import 'codemirror/mode/xml/xml.js';
import 'codemirror/mode/css/css.js';
import 'codemirror/mode/properties/properties.js';
import 'codemirror/mode/htmlmixed/htmlmixed.js';
import 'codemirror/addon/merge/merge.js';
import 'codemirror/lib/codemirror.css';
import 'codemirror/addon/merge/merge.css';
import './diff_match_patch.js';
import './index.less';

class CodeDiffer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    setTimeout(() => {
      const {mode, data, compareData, codeMirror} = this.props;
      let showMode;
      switch (mode) {
        case 'text':
          showMode = 'text/html';
          break;
        case 'properties':
          showMode = 'text/x-properties';
          break;
        case 'json':
          showMode = 'application/json';
          break;
        default:
          showMode = 'text/javascript';
      }
      CodeMirror.MergeView(this.codeWrap, {
        value: data,
        orig: compareData,
        mode: showMode,
        lineNumbers: true,
        highlightDifferences: true,
        collapseIdentical: true,
        connect: 'align',
        ...codeMirror,
      });
    }, 100);
  }

  // 获取类名,重新组合
  getClassName = () => {
    if (this.props.className) {
      const classNameArray = this.props.className.split(/\s+/);
      if (!classNameArray.includes('emasd-codediffer')) {
        classNameArray.push('emasd-codediffer');
      }
      return classNameArray.join(' ');
    }
    return 'emasd-codediffer';
  };

  render() {
    const {dataTitle, compareTitle} = this.props;
    const props = {
      ...this.props,
      className: this.getClassName(),
    };
    // 删除默认props，可能引起react的安全提示warn
    const rmPropsArr = ['mode', 'data', 'compareData', 'dataTitle', 'compareTitle', 'codeMirror'];
    rmPropsArr.forEach((ele) => {
      delete props[ele];
    });
    return (
      <div {...props}>
        <div className="title">
          <div className="dataTitle">{dataTitle}</div>
          <div className="compareTitle">{compareTitle}</div>
        </div>
        <div
          ref={(node) => {
            this.codeWrap = node;
          }}
        />
      </div>
    );
  }
}

CodeDiffer.propsTypes = {
  mode: PropTypes.oneOf(['text', 'properties', 'json']),
  data: PropTypes.string,
  compareData: PropTypes.string,
  dataTitle: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  compareTitle: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  codeMirror: PropTypes.object,
};
CodeDiffer.defaultProps = {
  mode: 'text',
  data: '',
  compareData: '',
  dataTitle: '当前版本',
  compareTitle: '变更后',
  codeMirror: {},
};

export default CodeDiffer;
