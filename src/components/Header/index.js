/* global window */
import React from 'react';
import {Link} from 'dva/router';
import { Button, Dropdown, Icon, Menu } from '@alipay/bigfish/antd';
import Logo from '@/assets/orange-icon.png';
import {ENV_GROUP_HOSTS, getLabel, getUrl, isSystemAdmin, isYoukuGroup} from '@/utils/EnvUtils';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';


import styles from './index.less';

export default class Header extends React.Component {
  render() {
    const options = (
      <Menu>
        {ENV_GROUP_HOSTS.map((item) => {
          return <Menu.Item key={item.host}>
            <a
              rel="noopener noreferrer"
              href={getUrl(item)}
            >
              {item.label}
            </a>
          </Menu.Item>
        })}
      </Menu>
    );

    const {userId, userName} = window._DATA_;
    const newPlatformEnabled = isSystemAdmin() || window._DATA_.newPlatformEnabled === 'true';
    let label = getLabel();

    return (
      <div className={`${styles.header} clearfix`}>
        <div className={`${styles.brand} clearfix`}>
          <Link className={styles.headLink} to={LinkUtils.getHome()}>
            <img className={styles.logo} src={Logo}/>
            <div className="pull-left">
              <div className={styles.title}>Orange</div>
              <div className={styles.subTitle}>发布平台</div>
            </div>
          </Link>
        </div>


        <div className={`${styles.menu}`}>

          <Menu mode="horizontal" selectedKeys={["3"]}>
            {isSystemAdmin() ? (<Menu.Item key="1">
              <a href="/admin.htm" target="_blank">
                <Icon type="setting"/> 系统管理
              </a>
            </Menu.Item>) : null}
            <Menu.Item key="3">
              <Link to={LinkUtils.getHome()}><Icon type="home"/> Orange </Link>
            </Menu.Item>
            <Menu.Item key="4">
              <a href={UrlUtils.getMappcenterHomeUrl()} target="_blank">
                <Icon type="appstore"/> 应用中心
              </a>

            </Menu.Item>
            <Menu.Item key="5">
              <a href="https://max.m.taobao.com/accs/view/mainPage" target="_blank">
                <Icon type="mobile"/>ACCS
              </a>
            </Menu.Item>
            <Menu.Item key="6">
              <a href="https://ha.emas.alibaba-inc.com/" target="_blank">
                <Icon type="line-chart"/>EMAS
              </a>
            </Menu.Item>
            <Menu.Item key="7">
              <a href={UrlUtils.getHelpUrl(null)} target="_blank">
                <Icon type="book"/> 帮助文档
              </a>
            </Menu.Item>
          </Menu>

          {(newPlatformEnabled && <Button
            type="primary"
            href={'/v5' + UrlUtils.getNewPlatformUrl()}
            className={styles["new-platform-btn"]}
            style={{display: UrlUtils.getNewPlatformUrl() ? 'inline-block' : 'none'}}
          >
            体验新版
          </Button>)}

          <div className={styles.user}>
            <Dropdown overlay={options}>
              <a className={`${styles.env} ant-dropdown-link`}>
                {label} <Icon type="down"/>
              </a>
            </Dropdown>
            <span>{userName}</span>
            {isYoukuGroup() ? null : <img src={`//work.alibaba-inc.com/photo/${userId}.30x30.jpg`}/>}

          </div>
        </div>
      </div>
    );
  }
}
