@headerHeight: 80px;
@navLeft: 200px;
@bgColor: #001529;
@lightColor: #9b9ea0;

.header {
  // line-height: @headerHeight;
  height: @headerHeight;
  background: #001529;
  color: #fff;
  // display: flex;
  position: relative;

  .brand {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: @navLeft;
    display: flex;
    align-items: center;
    justify-content: center;

    .headLink {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }

    .logo {
      float: left;
      margin-right: 10px;
      width: 60px;
    }

    .title {
      font-size: 20px;
    }

    .subTitle {
      color: @lightColor;
    }
  }

  .menu {
    margin-left: @navLeft;
    display: flex;
    // align-items: center;
    color: #fff;
    justify-content: space-between;
    align-items: center; // 垂直居中

    :global {
      .ant-menu {
        background: @bgColor;
        border-bottom: none;
      }

      .ant-menu-item,
      .ant-menu-submenu {
        min-width: 150px;
        line-height: @headerHeight;
        height: @headerHeight;
        color: @lightColor;

        &:hover {
          color: #39f;
        }

        > a {
          color: @lightColor;
          text-align: center;
        }
      }
    }

    .env {
      line-height: @headerHeight;
      margin-right: 30px;
      color: @lightColor;
    }

    .user {
      display: -webkit-box;
      padding: 0 20px;
      color: @lightColor;

      > span {
        display: block;
        line-height: @headerHeight;
        margin-right: 30px;
        color: @lightColor;
      }

      > img {
        margin-top: 40px;
        transform: translateY(-15px);
        border: 1px solid #999;
        border-radius: 30px;
      }
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05); /* 放大 */
    }
    100% {
      transform: scale(1);
    }
  }

  .new-platform-btn {
    background: linear-gradient(45deg, #6a11cb, #2575fc);
    border: none;
    color: white;
    height: 40px;
    padding: 7px 15px;
    border-radius: 10px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    outline: none;
    animation: pulse 1.5s infinite;
  }

  .new-platform-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  }

  .new-platform-btn:focus {
    outline: none;
  }

  .new-platform-btn:active {
    transform: scale(0.95);
  }
}
