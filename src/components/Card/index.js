/**
 * card 容器
 */
'use strict';

import React from 'react';

import {Measurer} from "@utils/Measurer";
import './index.less';

@Measurer
class CommonCard extends React.Component {
  static propTypes = {
    style: React.PropTypes.object,
    className: React.PropTypes.string,
    onResize: React.PropTypes.func
  };

  constructor(props) {
    super(props);
    this.dom = null;
  }

  componentWillMount() {
    this._initMeasurer(this._onResize.bind(this), this._getDom.bind(this));
  }

  componentWillUnmount() {
  }

  componentDidMount() {
    setTimeout(() => {
      let {width, height} = this.dom.getBoundingClientRect();
      let onResize = this.props.onResize || function () {
      };
      onResize(width, height);
    }, 500);
  }

  _getDom() {
    return this.dom;
  }

  _onResize(info) {
    let {width, height} = info;
    let onResize = this.props.onResize || function () {
    };
    onResize(width, height);
  }

  render() {

    return (
      <div className={`card ${this.props.className || ""}`}
           ref={ref => this.dom = ref}
           style={this.props.style || {}}>
        {this.props.children}
      </div>
    );
  }
}

export default CommonCard;
