/**
 * @param {Boolean} multiple 是否多选
 * @param {String | Array}  value 值，多选时为 Array，单选时为字符串
 * @param {Function} onChange 值改变的回调
 * @param {Array} defaultOptions 默认的选项（用于初始化显示label）
 * @example
 <ModuleSelector
 multiple
 value={[]}
 defaultOptions={[]}
 onChange={(v) => {
        console.log(v);
      }}
 />
 */

import { getValidStr } from '@/utils/utils';
import RemoteSelector from '@/components/RemoteSelector';

class ModuleSelector extends RemoteSelector {}

ModuleSelector.defaultProps = {
  getValue(item) {
    return item.moduleId;
  },
  getLabel(item) {
    return getValidStr(item.name) || null;
  },
  style: { minWidth: '200px' },
};

export default ModuleSelector;
