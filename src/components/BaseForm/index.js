import React from 'react';
import {Form} from 'antd';

import styles from './index.less';

export default ({className, children, extra, showExtra, opt, layout = 'inline'}) => {
  return (
    <Form className={`${styles.form} ${className}`} layout={layout}>
      <div className={styles.main}>
        <div>{children}</div>
        {showExtra ? <div>{extra}</div> : null}
      </div>
      <div className={styles.opt}>{opt}</div>
    </Form>
  );
};
