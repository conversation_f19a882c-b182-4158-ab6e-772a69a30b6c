import React from 'react';
import {Icon} from 'antd';
import classnames from 'classnames';
import styles from './index.less';

export default ({title, type, className, loading}) => {
  return (
    <span
      className={classnames({
        [styles.statusSpan]: true,
        className: !!className,
      })}
    >
      <span className={`${styles.icon} ${loading ? '' : `dot ${type}`}`}>
        {loading ? <Icon type="loading"/> : null}
      </span>
      <span>{title}</span>
    </span>
  );
};
