/**
 * 远程搜索选择组件
 * @param {Boolean} multiple 是否为多选
 * @param {Function} getData 获取数据的promise
 * @param {Function} getResult 接受服务端返回，返回为数组
 * @param {Function} getValue 对数组的每一项，返回值（字符串）
 * @param {Function} getLabel 对数组的每一项，返回显示文字
 * @param {String | Array} value 选中值，multiple为true时，传数组；不传或为false时，传字符串 (不传时为非受控组件)
 * @param {Function} onChange 值变换的回调
 * 对外接口 getCurrentValue 获取当前选中值
 * @example
 <RemoteSelector
 multiple={true || false}
 getData={key => {
        return request({
          url: 'http://jsonplaceholder.typicode.com/users?page=1',
          params: {
            name: key
          }
        });
      }}
 getResult={res => res}
 getValue={item => String(item.id)}
 getLabel={item => `${item.username}-${item.name}`}
 value={state.braches}
 onChange={(v) => {
        this.setState({ braches: v });
      }}
 />
 */

import React from 'react';
import {Select, Spin} from  '@alipay/bigfish/antd';
import {getArr} from '@/utils/utils';

const {Option} = Select;

export default class RemoteSelector extends React.Component {
  constructor(props) {
    super(props);

    const state = {
      selfValue: null,
      options: [],
      fetching: false,
    };
    if (props.multiple) {
      state.selfValue = [];
    }
    this.state = state;

    // 保存已搜索过的label
    this.CACHED_LABELS = {};
  }

  getCurrentValue = () => {
    const {value, multiple} = this.props;
    if (this.getIsControlled()) {
      if (multiple) {
        return getArr(value).map(v => String(v));
      } else {
        return this.props.value;
      }
    } else {
      return this.state.selfValue;
    }
  };

  // 获取此组件是否为受控组件
  getIsControlled() {
    return 'value' in this.props;
  }

  getExtraOptions = () => {
    const {props, CACHED_LABELS} = this;
    const {multiple, getValue, getLabel} = props;
    const defaultOptions = getArr(props.defaultOptions);

    defaultOptions.forEach((item) => {
      CACHED_LABELS[getValue(item)] = getLabel(item);
    });

    const value = this.getCurrentValue();

    const {options} = this.state;
    let extraValue = [];
    if (multiple) {
      extraValue = getArr(value).slice(0);
    } else {
      if (!value) {
        return [];
      }
      extraValue.push(value);
    }
    options.forEach((item) => {
      const index = extraValue.indexOf(getValue(item));
      if (index > -1) {
        extraValue.splice(index, 1);
      }
    });

    const extraOptions = extraValue.map((v) => {
      return {
        value: v,
        label: CACHED_LABELS[v],
      };
    });
    return extraOptions;
  };

  handleSearch = (key) => {
    if(!key){
      return ;
    }
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      const {props} = this;
      const {getValue, getLabel, getData} = props;
      this.setState({fetching: true});

      getData(key).then((res) => {
        const options = props.getResult(res);
        options.forEach((item) => {
          this.CACHED_LABELS[getValue(item)] = getLabel(item);
        });
        this.setState({options, fetching: false});
      });
    }, 500);
  };

  handleChange = (value) => {
    const {onChange, multiple} = this.props;
    if (onChange) {
      onChange(value);
    }
    this.setState({
      options: [],
      fetching: false,
      selfValue: multiple ? value.slice(0) : value,
    });
  };

  render() {
    const self = this;
    const {state, props} = self;
    const {getValue, getLabel, multiple, placeholder, ...others} = props;

    const selectProps = {
      ...others,
      notFoundContent: state.fetching ? <Spin size="small"/> : null,
      value: self.getCurrentValue(),
      filterOption: false,
      onSearch: self.handleSearch,
      onChange: self.handleChange,
      placeholder,
    };
    if (multiple) {
      selectProps.mode = 'multiple';
    } else {
      selectProps.showSearch = true;
    }

    const extraOptions = this.getExtraOptions();
    const widgets = [];
    state.options.forEach((item) => {
      const v = getValue(item);
      const label = self.CACHED_LABELS[v] || getLabel(item);
      widgets.push(<Option key={v}>{label}</Option>);
    });
    extraOptions.forEach((item) => {
      widgets.push(<Option key={item.value}>{item.label}</Option>);
    });

    return <Select {...selectProps}>{widgets}</Select>;
  }
}
