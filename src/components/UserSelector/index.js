/**
 * @param {Boolean} multiple 是否多选
 * @param {String | Array}  value 值，多选时为 Array，单选时为字符串
 * @param {Function} onChange 值改变的回调
 * @param {Array} defaultOptions 默认的选项（用于初始化显示label）
 * @example
 <UserSelector
 multiple
 value={[]}
 defaultOptions={[]}
 onChange={(v) => {
        console.log(v);
      }}
 />
 */

import {getArr, getValidStr} from '@/utils/utils';
import RemoteSelector from '@/components/RemoteSelector';
import {searchUser} from '@/services/search';

class UserSelector extends RemoteSelector {
}

UserSelector.defaultProps = {
  getData(keyword) {
    return searchUser({keyword: keyword, pageNum: 1, pageSize: 8});
  },
  getResult(res) {
    return getArr(res.content);
  },
  getValue(item) {
    return getValidStr(item.empId) || null;
  },
  getLabel(item) {
    return `${getValidStr(item.displayName)}-${getValidStr(item.empId)}`;
  },
  style: {minWidth: '200px'},
};

export default UserSelector;
