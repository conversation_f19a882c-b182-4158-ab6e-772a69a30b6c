import React from 'react';
import CodeMirror from 'codemirror';
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/mode/properties/properties.js';
import 'codemirror/mode/htmlmixed/htmlmixed.js';
import 'codemirror/addon/fold/foldgutter.js';
import 'codemirror/addon/fold/foldcode.js';
import 'codemirror/addon/fold/comment-fold.js';
import 'codemirror/addon/merge/merge.js';
import 'codemirror/lib/codemirror.css';
import './index.less';

class CodeEditor extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    setTimeout(() => {
      const {mode, value, readOnly, onChange, codeMirror, placeholder} = this.props;
      let showMode;
      switch (mode) {
        case 'text':
          showMode = 'text/html';
          break;
        case 'properties':
          showMode = 'text/x-properties';
          break;
        case 'json':
          showMode = 'application/json';
          break;
        default:
          showMode = 'text/javascript';
      }
      this.codeMirror = CodeMirror.fromTextArea(this.codeWrap, {
        lineNumbers: true,
        autoRefresh: true,
        mode: showMode,
        readOnly: !!readOnly,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        value,
        ...codeMirror,
      });
      if (!readOnly && this.codeMirror) {
        this.codeMirror.on('change', () => {
          const newValue = this.codeMirror.getValue();
          onChange(newValue);
        });
      }
    }, 100);
  }

  componentWillReceiveProps(nextProps) {
    if (this.codeMirror) {
      // 监听mode变化
      if (
        nextProps.mode !== this.props.mode &&
        nextProps.mode !== this.codeMirror.getOption('mode')
      ) {
        const {mode} = nextProps;
        this.codeMirror.setOption('mode', mode);
      }
      // 监听readOnly变化
      if (
        nextProps.readOnly !== this.props.readOnly &&
        nextProps.readOnly !== this.codeMirror.getOption('readOnly')
      ) {
        const {readOnly} = nextProps;
        this.codeMirror.setOption('readOnly', readOnly);
      }
      // 监听value变化
     //console.log('render','thisProbs:'+this.props.value,'codeMirror:'+this.codeMirror.getValue(),'nextProps:'+nextProps.value)
      if (!this.codeMirror.getValue() && nextProps.value) {
        this.codeMirror.setValue(nextProps.value);
      } else if (nextProps.value !== this.props.value && nextProps.value !== this.codeMirror.getValue()) {
        const {value} = nextProps;
        this.codeMirror.setValue(value);
      }
    }
  }

  // 获取类名,重新组合
  getClassName = () => {
    if (this.props.className) {
      const classNameArray = this.props.className.split(/\s+/);
      if (!classNameArray.includes('emasd-codeeditor')) {
        classNameArray.push('emasd-codeeditor');
      }
      return classNameArray.join(' ');
    }
    return 'emasd-codeeditor';
  };

  render() {
    const {value = ''} = this.props;
    const props = {
      ...this.props,
      className: this.getClassName(),
    };
    // 删除默认props，可能引起react的安全提示warn
    const rmPropsArr = ['mode', 'value', 'onChange', 'readOnly', 'codeMirror'];
    rmPropsArr.forEach((ele) => {
      delete props[ele];
    });
    return (
      <textarea
        ref={(node) => {
          this.codeWrap = node;
        }}
        defaultValue={value}
        {...props}
      />
    );
  }
}

CodeEditor.defaultProps = {
  mode: 'properties',//['text', 'properties', 'json']
  value: '',
  onChange: () => {
  },
  readOnly: false,
  codeMirror: {},
};

export default CodeEditor;
