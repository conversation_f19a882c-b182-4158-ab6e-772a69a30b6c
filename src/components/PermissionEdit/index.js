import React, {Component, PropTypes} from 'react';
import {Select} from 'antd';
import './index.less';

const Option = Select.Option;

class PermissionEdit extends Component {
  static defaultProps = {
    authedPeople: {}, // 已经获得相应权限的人
    searchedCandidates: {} // 搜索获得的可选人员
  };

  constructor(props) {

    super(props);
    this.state = {candidates: []};
  }

  componentDidMount() {
    const {authedPeople} = this.props;
    this.setState({
      candidates: authedPeople.value
    });
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.hasOwnProperty('searchedCandidates')
      && (nextProps.searchedCandidates.suggestedTimeStamp !== this.props.searchedCandidates.suggestedTimeStamp)) {
      //const nextCandidates = this.props.authedPeople.value.concat(nextProps.searchedCandidates.value);
      const nextCandidates = [].concat(nextProps.searchedCandidates.value);

      this.setState({
        candidates: nextCandidates
      });
    }
    this.forceUpdate();
  }

  /**
   * util: get names from permission list
   * @param names
   * @returns {Array}
   * @private
   */
  _getPermissionNicknames(names) {
    const result = [];
    for (let i = 0; i < names.length; i++) {
      const preprocessedItem = names[i];
      result.push(preprocessedItem.nickName);
    }
    return result;
  }

  /**
   * 取候选人Id
   * @param people
   * @returns {Array}
   * @private
   */
  _getPermissionIds(people) {
    const result = [];
    for (let i = 0; i < people.length; i++) {
      const preprocessedItem = people[i];
      result.push(preprocessedItem.accountId);
    }
    return result;
  }

  _getAuthedPermissions(people) {
    const result = [];
    for (let i = 0; i < people.length; i++) {
      const preprocessedItem = people[i];
      const singlePerson = `${preprocessedItem.nickName}(${preprocessedItem.accountId})`;
      result.push(singlePerson);
    }
    return result;
  }

  /**
   * event: fired when the input value is changed
   * @param value
   * @private
   */
  _onPermissionSearch(value) {
    if (value !== '') {
      this.props.onUserNameSearch({keyword: value});
    }
  }

  /**
   * event: fired on select a candidate from the option list
   * @param value
   * @private
   */
  _onCandidateSelect(value) {
    const _self = this;
    /**
     * find user by name
     * @param name
     * @private
     */
    const _findUserByName = (val) => {
      const {searchedCandidates, permissionType} = this.props;
      const allCandidates = searchedCandidates.value;
      for (let idx = 0; idx < allCandidates.length; idx++) {
        const candidate = allCandidates[idx];
        const candidateOnDisplay = `${candidate.nickName}(${candidate.accountId})`;
        if (candidateOnDisplay === val) {
          candidate.mappRole = permissionType;
          return candidate;
        }
      }
      return null;
    };
    // call props of parent to add a user for this level permission
    const person = _findUserByName(value);
    if (person !== null) {
      _self.props.onUserPermissionSelect(person);
    }
  }

  /**
   * event: fired on unselect an option from the option list
   * @param value
   * @private
   */
  _onCandidateUnSelect(value) {
    const _self = this;
    const {authedPeople, permissionType} = _self.props;
    /**
     * find user by name
     * @param name
     * @private
     */
    const _findUserByVal = (val) => {
      const allAuthed = authedPeople.value;
      for (let idx = 0; idx < allAuthed.length; idx++) {
        const singleAuthed = allAuthed[idx];
        const candidateOnDisplay = `${singleAuthed.nickName}(${singleAuthed.accountId})`;
        if (candidateOnDisplay === val) {
          singleAuthed.mappRole = permissionType;
          return singleAuthed;
        }
      }
      return null;
    };
    const person = _findUserByVal(value);
    if (person !== null) {
      _self.props.onUserPermissionUnselect(person);
    }
  }

  /**
   * render: render select options
   * @param candidates
   * @returns {*}
   * @private
   */
  _renderSelectOptions(candidates) {
    const options = candidates.map((candidate, idx) => {
      const toDisplay = `${candidate.nickName}(${candidate.accountId})`;
      return (<Option key={idx + 1} value={toDisplay}>{toDisplay}</Option>);
    });
    return options;
  }


  render() {
    const _self = this;
    const {authedPeople, searchedCandidates} = this.props;
    const options = _self._renderSelectOptions(this.state.candidates);
    return (<Select mode="multiple" placeholder="请输入花名修改权限"
                    style={{width: '90%'}}
                    onSelect={_self._onCandidateSelect.bind(_self)}
                    onDeselect={_self._onCandidateUnSelect.bind(_self)}
                    value={_self._getAuthedPermissions(authedPeople.value)}
                    notFoundContent="找不到该用户"
                    onSearch={_self._onPermissionSearch.bind(_self)}>{options}</Select>);
  }
}

/*PermissionEdit.propTypes = {
  authedPeople: PropTypes.object,
  searchedCandidates: PropTypes.object,
  onUserNameSearch: PropTypes.func,
  onUserPermissionSelect: PropTypes.func,
  onUserPermissionUnselect: PropTypes.func,
  addUserPermissionResult: PropTypes.object,
  deleteUserPermissionResult: PropTypes.object,
  permissionType: PropTypes.number
};*/
export default PermissionEdit;
