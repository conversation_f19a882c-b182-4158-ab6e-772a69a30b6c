// @import (reference) '../../../styles/base.less';
@indent: 16px;
@descColor: rgba(0, 0, 0, 0.44);

:global {
  .up-color {
    color: green;
  }

  .down-color {
    color: red;
  }
}

.card {
  display: inline-block;
  width: 20%;
  padding: 0 4px;

  &:first-child {
    padding-left: 0;
  }

  &:nth-child(5n) {
    padding-right: 0;
  }

  .content {
    background: #fff;
    padding: @indent;
    width: 100%;
  }

  .header {
    color: @descColor;

    .title {
      float: left;
      font-size: 14px;
    }

    .tip {
      float: right;
      cursor: pointer;
    }
  }

  .value {
    height: 42px;
    font-size: 28px;
    color: rgba(0, 0, 0, 0.847);
  }

  .extra {
    // margin-bottom: @hfIndent;
    // width: 100px;
    // overflow: hidden;
  }

  .compare {
    // border-top: 1px solid @borderColor;
  }
}
