import React from 'react';
import {Icon, Tooltip} from 'antd';
import {getFixedNum} from '@/utils/LangUtils';

import styles from './index.less';

function getComparePercent(origin) {
  const num = getFixedNum(Number(origin) * 1.0);
  if (isNaN(num)) {
    return '-';
  }
  const dir = num > 0 ? 'up' : 'down';
  return (
    <span>
      <Icon className={`${dir}-color`} type={`caret-${dir}`}/> {num} %
    </span>
  );
}

export default class MetricCard extends React.Component {
  render() {
    const {
      tip,
      className,
      value,
      suffix,
      title,
      weekCompare,
      dayCompare,
      extra,
      ...others
    } = this.props;

    return (
      <div className={`${styles.card} ${className}`} {...others}>
        <div className={styles.content}>
          <div className={`${styles.header} clearfix`}>
            <div className={styles.title}>{title}</div>
            {tip ? (
              <div className={styles.tip}>
                <Tooltip title={tip} placement="bottom">
                  <Icon type="question-circle-o"/>
                </Tooltip>
              </div>
            ) : null}
          </div>
          <div className={styles.value}>
            <span className="mr5">{value}</span>
            <span className="num-suffix">{suffix}</span>
          </div>
          {extra ? <div className={styles.extra}>{extra}</div> : null}
          <div className={`${styles.compare} clearfix`}>
            <div className={`${styles.compareItem} pull-left`}>
              <div className={styles.itemTitle}>同7天前比</div>
              <div className={styles.itemRate}>{getComparePercent(weekCompare)}</div>
            </div>
            <div className={`${styles.compareItem} pull-right`}>
              <div className={styles.itemTitle}>同前天比</div>
              <div className={styles.itemRate}>{getComparePercent(dayCompare)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
