import request from '@/utils/request';
import api from '@/config/api';

export async function queryAppList(data) {
  return request({
    url: api.setting.queryAppList,
    method: 'get',
    data,
  });
}

export async function queryAppDetail(data) {
  return request({
    url:  api.setting.queryAppDetail,
    method: 'get',
    data,
  });
}


export async function appConfigSetting(data) {
  return request({
    url:  api.setting.appConfigSetting,
    method: 'get',
    data,
  });
}
