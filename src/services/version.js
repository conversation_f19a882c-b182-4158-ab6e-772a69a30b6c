import request from '@/utils/request';
import api from '@/config/api';

export async function getVersionList(data) {
  return request({
    url: api.version.getVersionList,
    method: 'get',
    data,
  });
}

export async function getVersionDetail(data) {
  return request({
    url: api.version.getVersionDetail,
    method: 'get',
    data,
  });
}


export async function queryKnockoutVersions(data) {
  return request({
    url: api.version.queryKnockoutVersions,
    method: 'get',
    data,
  });
}

export async function createVersion(data) {
  return request({
    url: api.version.createVersion,
    method: 'post',
    data,
  });
}


export async function getResourceDetail(data) {
  return request({
    url: api.version.getResourceDetail,
    method: 'get',
    data,
  });
}


export async function closeVersion(data) {
  return request({
    url: api.version.closeVersion,
    method: 'get',
    data,
  });
}


export async function debugVersion(data) {
  return request({
    url: api.version.debugVersion,
    method: 'get',
    data,
  });
}

export async function versionTools(data) {
  return request({
    url: api.tools.versionTools,
    method: 'get',
    data,
  });
}


export async function skipProcess(data) {
  return request({
    url: api.version.skipProcess,
    method: 'get',
    data,
  });
}

export async function getMassPushResult(data) {
  return request({
    url: api.version.getMassPushResult,
    method: 'get',
    data,
  });
}
export async function getMassDeviceList(data) {
  return request({
    url: api.version.getMassDeviceList,
    method: 'get',
    data,
  });
}


export async function reportVersionDetail(data) {
  return request({
    url: api.version.reportVersionDetail,
    method: 'get',
    data,
  });
}

export async function queryVersionDetailList(data) {
  return request({
    url: api.version.queryVersionDetailList,
    method: 'get',
    data,
  });
}

export async function getRollbackView(data) {
  return request({
    url: api.version.getRollbackView,
    method: 'get',
    data,
  });
}

export async function getRollbackList(data) {
  return request({
    url: api.version.getRollbackList,
    method: 'get',
    data,
  });
}

export async function rollbackVersion(data) {
  return request({
    url: api.version.rollbackVersion,
    method: 'get',
    data,
  });
}



export async function massPushOper(data) {
  return request({
    url: api.version.massPushOper,
    method: 'get',
    data,
  });
}

export async function getCheckHoldResult(data, handleError) {
  return request({
    url: api.version.getCheckHoldResult,
    method: 'get',
    data,
  }, handleError);
}

export async function versionStage(data) {
  return request({
    url: api.version.versionStage,
    method: 'get',
    data,
  });
}

export async function recordOperate(data) {
  return request({
    url: api.version.recordOperate,
    method: 'get',
    data,
  });
}

export async function getVersionDeviceCnt(data) {
  return request({
    url: api.version.getVersionDeviceCnt,
    method: 'get',
    data,
  }, (res) => {
    console.log('查询设备数量异常', res);
  });
}

export async function getLatestGrayRecord(data) {
  return request({
    url: api.version.getLatestGrayRecord,
    method: 'get',
    data,
  });
}

export async function getTigaTaskDetail(data) {
  return request({
    url: api.version.getTigaTaskDetail,
    method: 'get',
    data,
  });
}

export async function createTigaTask(data) {
  return request({
    url: api.version.createTigaTask,
    method: 'get',
    data
  });
}

export async function getTigaSuggestTemplateList(data) {
  return request({
    url: api.version.getTigaSuggestTemplateList,
    method: 'get',
    data
  });
}
