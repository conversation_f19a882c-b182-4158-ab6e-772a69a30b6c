import request from '@/utils/request';
import api from '@/config/api';

export async function configNotifyNumList(params) {
  return request({
    url: api.report.configNotifyNumList,
    method: 'get',
    data: params,
    transformResponse: [reportDataToJSON],
  });
}

export async function configUpdateNumList(params) {
  return request({
    url: api.report.configUpdateNumList,
    method: 'get',
    data: params,
    transformResponse: [reportDataToJSON],
  });
}

export async function configUpdateRateList(params) {
  return request({
    url: api.report.configUpdateRateList,
    method: 'get',
    data: params,
    transformResponse: [reportDataToJSON],
  });
}

export async function configUseNumList(params) {
  return request({
    url: api.report.configUseNumList,
    method: 'post',
    data: params,
    transformResponse: [reportDataToJSON],
  });
}

export async function queryDimensions(params) {
  return request({
    url: api.report.queryDimensions,
    method: 'get',
    data: params,
  });
}

/**
 * 修复":{"metricName":"count","needAccumulatorUv":false},"values":{1577620800000:"23080.0",1577624400000:"22910.0" ...
 * JSON key为数字无法解析为JSON，axios直接返回字符串问题
 * @param data
 * @returns {any}
 */
function reportDataToJSON(data) {
  if (typeof data == 'string') {
    return JSON.parse(data.replace(/(\d{13}):/g, '"$1":'));
  }
  return data;
}

export async function getAppDashboardOfApp(params) {
  return request({
    url: api.report.getAppDashboardOfApp,
    method: 'get',
    data: params,
  });
}

export async function getAppDashboardOfDc(params) {
  return request({
    url: api.report.getAppDashboardOfDc,
    method: 'get',
    data: params,
  });
}

export async function getAppDashboardOfConsole(params) {
  return request({
    url: api.report.getAppDashboardOfConsole,
    method: 'get',
    data: params,
  });
}


export async function getAppTrendOfApp(params) {
  return request({
    url: api.report.getAppTrendOfApp,
    method: 'get',
    data: params,
  });
}

export async function getAppTrendOfDc(params) {
  return request({
    url: api.report.getAppTrendOfDc,
    method: 'get',
    data: params,
  });
}

export async function getAppIntervalDetailsOfApp(params) {
  return request({
    url: api.report.getAppIntervalDetailsOfApp,
    method: 'get',
    data: params,
  });
}
