import request from '@/utils/request';
import api from '@/config/api';

export async function searchUser({ keyword = '', pageNum = 1, pageSize = 8 }) {
  return request({
    url: api.search.user,
    method: 'GET',
    data: {
      keyword,
      pageNum,
      pageSize,
    },
  });
}

export async function searchModule({ keyword = '', appKey, pageNum = 1, pageSize = 8 }) {
  return request(
    {
      url: api.search.module,
      method: 'GET',
      data: {
        appKey,
        keyword,
        pageNum,
        pageSize,
      },
    },
    function (res) {
      console.error('摩天轮模块接搜索报错: ', res);
    },
  );
}

export async function searchAoneApp({ key = '', pageNum = 1, pageSize = 8 }) {
  return request({
    url: api.search.aoneApp,
    method: 'GET',
    data: {
      key,
      pageNum,
      pageSize,
    },
  });
}

export async function queryAppList(data) {
  return request({
    url: api.search.queryAppList,
    method: 'GET',
    data: data,
  });
}

export async function queryUserByEmpIds(data) {
  return request({
    url: api.search.queryUserByEmpIds,
    method: 'GET',
    data: data,
  });
}

export async function queryModuleByModuleIds(data) {
  return request({
    url: api.search.queryModuleByModuleIds,
    method: 'GET',
    data: data,
  });
}
