import request from '@/utils/request';
import api from '@/config/api';

export async function getBusinessList(data) {
  return request({
    url: api.business.getBusinessList,
    method: 'get',
    data,
  });
}

export async function getBusinessDetail(data) {
  return request({
    url: api.business.getBusinessDetail,
    method: 'get',
    data,
  });
}


export async function createUpdateBusiness(data) {
  return request({
    url: api.business.createUpdateBusiness,
    method: 'get',
    data,
  });
}
