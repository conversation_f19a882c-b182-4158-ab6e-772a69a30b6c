import request from '@/utils/request';
import api from '@/config/api';

export async function getAppNamespaceList(data) {
    return request({
        url: api.namespace.appNamespaceList,
        method: 'get',
        data,
    });
}

export async function myNamespaceList(data) {
    return request({
        url: api.namespace.namespaceListForMe,
        method: 'get',
        data,
    });
}

export async function addNamespace(data) {
    return request({
        url: api.namespace.addNamespace,
        method: 'post',
        data,
    });
}

export async function offlineNamespace(data) {
    return request({
        url: api.namespace.offlineNamespace,
        method: 'post',
        data,
    });
}

export async function availableNamespace(data) {
    return request({
        url: api.namespace.availableNamespace,
        method: 'post',
        data,
    });
}

export async function updateNamespace(data) {
    return request({
        url: api.namespace.updateNamespace,
        method: 'post',
        data,
    });
}

export async function loadLevelSubmitAuditing(data) {
    return request({
        url: api.namespace.loadLevelSubmitAuditing,
        method: 'post',
        data,
    });
}

export async function queryKnockoutForOfflineNamespace(data) {
    return request({
        url: api.namespace.queryKnockoutForOfflineNamespace,
        method: 'get',
        data,
    });
}

export async function getNamespace(data) {
    return request({
        url: api.namespace.getNamespace,
        method: 'get',
        data,
    });
}

export async function getNamespaceDetail(data) {
  return request({
    url: api.namespace.getNamespaceDetail,
    method: 'get',
    data,
  });
}

export async function queryUnAvailableNamespace(data) {
  return request({
    url: api.namespace.queryUnAvailableNamespace,
    method: 'get',
    data,
  });
}

export async function namespaceTools(data) {
  return request({
    url: api.tools.namespaceTools,
    method: 'get',
    data,
  });
}

export async function auditingNamespace(data) {
  return request({
    url: api.namespace.auditingNamespace,
    method: 'get',
    data,
  });
}

export async function cancelAuditingNamespace(data) {
    return request({
        url: api.namespace.cancelAuditingNamespace,
        method: 'get',
        data,
    });
}


export async function permissionApply(data) {
    return request({
        url: api.namespace.permissionApply,
        method: 'get',
        data,
    });
}
