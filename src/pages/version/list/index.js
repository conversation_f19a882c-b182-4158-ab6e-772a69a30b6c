import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';
import {Breadcrumb} from '@alipay/bigfish/antd';
import {getFuncName} from '@/utils/utils';
import {LinkUtils} from '@/utils/LinkUtils';

import QueryForm from './components/QueryForm';

import List from './components/List';


import namespace from '@/config/namespace';

const NAME_SPACE = namespace.version.list;

class VersionList extends React.Component {
  componentWillMount() {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }

  render() {
    const {dispatch, pageData} = this.props;
    const {list, formData, pagination, loading, appList,userMap} = pageData;

    const onRefresh = (payload) => {
      dispatch({
        type: getFuncName(NAME_SPACE, 'getData'),
        payload,
      });
    };
    const changeFormData = (payload) => {
      dispatch({
        type: getFuncName(NAME_SPACE, 'changeFormData'),
        payload,
      });
    };
    if (!pagination.onChange) {
      pagination.onChange = (current) => {
        onRefresh({
          pageNo: current,
        });
      };
    }

    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>配置管理</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getVersionList()}>发布列表</Link>
          </Breadcrumb.Item>
        </Breadcrumb>
        <QueryForm formData={formData} appList={appList} onChange={changeFormData} onRefresh={onRefresh}/>

        <List data={list}  appList={appList} userMap={userMap} pagination={pagination} loading={loading}/>
      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(VersionList);
