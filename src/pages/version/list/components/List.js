import React from 'react';
import {Icon, Table} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from '@/utils/ConvertUtils';
import {VersionUtils} from '@/utils/VersionUtils';
import {UserUtils} from "@/utils/UserUtils";
import {VERSION_SOURCE_API} from '@/constants/version';

import styles from './List.less';

import {
    VERSION_STATUS_SUCCESS
} from "@/constants/version";

export default class List extends React.Component {
    getColumns = (userMap, appList) => [
        {
            dataIndex: 'id',
            title: 'Version',
            width: 250,
            render(text, record) {
                return (
                    <div className={styles.txtEllipsis}>
                        <p>
                            {record.version}{' '}
                            <a href={UrlUtils.getResourceUrl(record.resourceId)} target="_blank">
                                <Icon type="download"/>
                            </a>
                        </p>
                        <p>策略：{record.strategy || '-'}</p>
                    </div>
                );
            },
        },
        {
            title: 'Namespace',
            width: 200,
            render(text, record) {
                return (
                    <div className={styles.txtEllipsis}>
                        <p>name:{record.name}</p>
                        <div>app:{record.appKey}-{record.appVersion}({VersionUtils.getAppName(record.appKey, appList)})</div>
                    </div>
                );
            },
        },
        {
            title: '变更',
            render(text, record) {
                return (
                    <div>
                        <p>来源：{ConvertUtils.getVersionSourceName(record.source)}</p>
                        <p>加载：{ConvertUtils.getLoadLevelName(record.loadLevel)}</p>
                    </div>
                );
            },
        },
        {
            title: '相关人',
            width: 110,
            render(text, record) {
                return (
                    <div>
                        <p>创建: {record.creator == 'system' && record.source == VERSION_SOURCE_API ? VersionUtils.getVersionApiBusinessName(record.sourceData) : UserUtils.getUserDisplayName(record.creator, userMap)}</p>
                        <p>跟进: {record.creator == 'system' && record.source == VERSION_SOURCE_API ? VersionUtils.getVersionApiBusinessName(record.sourceData) : UserUtils.getUserDisplayName(record.reviewer, userMap)}</p>
                    </div>
                );
            },
        },
        {
            title: '相关时间',
            width: 210,
            render(text, record) {
                return (
                    <div>
                        <p>创建:{record.gmtCreateTime}</p>
                        <p>发布:{record.status == VERSION_STATUS_SUCCESS ? record.gmtPublishTime : '-'}</p>
                    </div>
                );
            },
        },
        {
            title: '状态',
            render(text, record) {
                return (<div>
                    <p>状态：{ConvertUtils.getVersionStatusName(record.status)}</p>
                    <p>生效：{ConvertUtils.getAvailableName(record.isAvailable)}</p>
                </div>);
            }
        },
        {
            title: '操作',
            render(text, record) {
                return (
                    <div>
                        <p>
                            <Link to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>
                                发布单详情
                            </Link>
                        </p>
                        <p>
                            <Link to={LinkUtils.getNamespaceDetail(record.namespaceId, record.version)}>
                                配置详情
                            </Link>
                        </p>
                    </div>
                );
            },
        },
    ];

    removeItem = () => {
    };

    render() {
        const {loading, userMap} = this.props;
        let {data, pagination, appList} = this.props;
        if (data && data[0]) {
        } else {
            data = [];
        }
        if (pagination && !pagination.showTotal) {
            pagination.showQuickJumper = true;
            pagination.showTotal = function(total) {return `总共： ${total}`;};
        }
        return (
            <div className={styles.holder}>
                <Table
                    loading={loading}
                    columns={this.getColumns(userMap, appList)}
                    dataSource={data}
                    rowKey={record => record.id}
                    pagination={pagination}
                />
            </div>
        );
    }
}
