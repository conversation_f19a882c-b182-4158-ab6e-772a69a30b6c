import React from 'react';
import {But<PERSON>, DatePicker, Form, Input, Select, Switch} from '@alipay/bigfish/antd';

import moment from 'moment';
import {VERSION_SOURCE_ARR, VERSION_STATUS_ARR} from '@/constants/version';


import styles from './QueryForm.less';

const {Option} = Select;
const FormItem = Form.Item;


const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class QueryForm extends React.Component {
  render() {
    const {formData, onChange, onRefresh} = this.props;
    const appList = this.props.appList || [];
    const toString = function (key) {
      return '' + key;
    }
    return (
      <div className={styles.holder}>
        <Form layout="inline" className="clearfix">
          <FormItem label="状态">
            <Select
              value={formData.status}
              className={styles.typeSelector}

              onChange={(v) => {
                onChange({
                  params: {
                    status: v,
                  },
                });
              }}
            >
              <Option value="">所有</Option>
              {VERSION_STATUS_ARR.map((item) => {
                return <Option value={toString(item.value)} key={item.value}>{item.label}</Option>;
              })}

            </Select>
          </FormItem>
          <FormItem label="来源">
            <Select
              value={formData.source}
              className={styles.typeSelector}

              onChange={(v) => {
                onChange({
                  params: {
                    source: v,
                  },
                });
              }}
            >
              {VERSION_SOURCE_ARR.map((item) => {
                return <Option value={toString(item.value)} key={item.value}>{item.label}</Option>;
              })}
              <Option value="-1">所有</Option>
            </Select>
          </FormItem>
          <FormItem label="客户端">
            <Select
              showSearch
              value={formData.appKey}
              className={styles.typeSelectormax}

              onChange={(v) => {
                onChange({
                  params: {
                    appKey: v,
                  },
                });
              }}
            >
              <Option value="">所有</Option>
              {appList.map((item) => {
                return <Option value={item.appKey} key={item.appKey}>{item.appKey}-{item.appName}</Option>;
              })}

            </Select>
          </FormItem>
          <FormItem label="name">
            <Input
              value={formData.name}
              className={styles.search}
              placeholder="根据name精确搜索"
              onChange={(e) => {
                onChange({
                  params: {
                    name: e.target.value,
                  },
                });
              }}
            />
          </FormItem>
          <FormItem label="namespaceId">
            <Input
              value={formData.namespaceId}
              className={styles.search}
              placeholder="根据namespaceId精确搜索"
              onChange={(e) => {
                onChange({
                  params: {
                    namespaceId: e.target.value,
                  },
                });
              }}
            />
          </FormItem>
          <FormItem label="创建人">
            <Input
              value={formData.creator}
              className={styles.search}
              placeholder="输入工号(历史花名)精确搜索"
              onChange={(e) => {
                onChange({
                  params: {
                    creator: e.target.value,
                  },
                });
              }}
            />
          </FormItem>
          <FormItem label="发布时间">
            <DatePicker
              showTime={true}
              format={dateFormat}
              placeholder="Start"
              value={formData.gps ? moment(formData.gps, dateFormat) : null}
              onChange={function (value, dateString) {
                onChange({
                  params: {
                    gps: dateString
                  },
                });

              }}
            />
            ~
            <DatePicker
              showTime={true}
              format={dateFormat}
              placeholder="End"
              value={formData.gpe ? moment(formData.gpe, dateFormat) : null}
              onChange={function (value, dateString) {
                onChange({
                  params: {
                    gpe: dateString
                  },
                });

              }}
            />
          </FormItem>
          <FormItem label="只查有效">
            <Switch checked={formData.online == '1' ? true : false} onChange={(v) => {
              let ret = (v == true ? "1" : "");
              onChange({
                params: {
                  online: ret,
                },
              });
            }}
            />

          </FormItem>
          <FormItem className="pull-right">
            <Button className="st-mr" type="primary" onClick={onRefresh}>
              查询
            </Button>
          </FormItem>
        </Form>
      </div>
    );
  }
}
