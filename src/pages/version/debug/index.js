import React from 'react';
import {connect} from 'dva';
import {<PERSON>} from 'dva/router';

import {<PERSON><PERSON>c<PERSON>b, Button, Checkbox, Col, Form, Icon, Input, message, Row} from '@alipay/bigfish/antd';
import QRCode from 'qrcode.react';
import {getFuncName} from '@/utils/utils';


import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";

import {isSystemAdmin, isTestEnv} from "@/utils/EnvUtils";
import styles from './index.less';

import namespace from '@/config/namespace';

const CheckboxGroup = Checkbox.Group;


const FormItem = Form.Item;
import VersionList from './components/VersionList';


const NAME_SPACE = namespace.version.debug;

class TypeDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {selectVersionBO: undefined, namespaceId: undefined};
  }

  _initPageData(namespaceId) {
    const {dispatch, location, match} = this.props;

    this.setState({
      namespaceId: namespaceId,
      version: undefined
    });
    let payload = {params: {'namespaceId': namespaceId}};

    dispatch({
      type: getFuncName(NAME_SPACE, 'getDetail'),
      payload,
    });
  }

  componentWillMount() {
    const {dispatch, location, match} = this.props;
    const versions = (this.props.match && this.props.match.params && this.props.match.params.version) ? this.props.match.params.version : '';
    const params = versions.split("-");
    const namespaceId = params[0];
    this._initPageData(namespaceId);

  }

  componentWillReceiveProps(nextProps) {
    if (this.props != null && nextProps != null && this.props.location != nextProps.location) {

      const versions = (nextProps.match && nextProps.match.params && nextProps.match.params.version) ? nextProps.match.params.version : '';
      const params = versions.split("-");
      const namespaceId = params[0];
      if (namespaceId != this.state.namespaceId) {
        this._initPageData(namespaceId);

      }
    }
  }


  _renderDesc(namespaceBO, appBO) {
    const {dispatch} = this.props;
    return (
      <div>
        <h2>
          配置描述
        </h2>
        <Form className={styles.form}>
          <Row gutter={24}></Row>
          <Row>
            <Row>
              <Col span={8}>
                <FormItem label="namespace">{namespaceBO.name}
                  {namespaceBO.namespaceId ? (
                    <Link to={LinkUtils.getNamespaceDetail(namespaceBO.namespaceId, null)}>去详情</Link>) : null}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="客户端">{appBO.appName}( {namespaceBO.appKeyOrGroup})</FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="加载级别">{ConvertUtils.getLoadLevelName(namespaceBO.loadLevel)} </FormItem>
              </Col>
            </Row>
          </Row>
        </Form>
      </div>);

  }

  _renderTips(selectVersionBO, versionBO, indexBO, wmccView) {
    const {dispatch} = this.props;
    const published = indexBO && selectVersionBO && indexBO.gmtCreateTime >= selectVersionBO.gmtPublishTime;
    const showWmcc = selectVersionBO && selectVersionBO.version && selectVersionBO.version && versionBO.version;
    return (
      <div>
        <h2>
          步骤说明：
        </h2>
        <Form className={styles.form}>

          <FormItem label="第一步">云上检查</FormItem>
          <FormItem label="* 1.1">下表中选择命中的版本，判断索引是否更新过：{published ? '是' :
            <span> <span style={{"color": "red"}}>'否'</span>, 请等待云上发布完成</span>}</FormItem>
          {showWmcc ? (<FormItem label="* 1.2">探针是否生效：{this._renderWmccView(wmccView)} </FormItem>) : null}
          <FormItem label="第二步">端上检查 </FormItem>
          <FormItem label="* 2.1">客户端扫码，比较云上index与端上index，判断索引是否已更新 </FormItem>
          {selectVersionBO.loadLevel != 10 ? (
            <FormItem label="* 2.2">若配置内容未更新，请确保该配置被GetConfig使用过（该配置加载级别不是HighInit ）</FormItem>) : null}
          <FormItem label="第三步">寻求帮助 </FormItem>
          <FormItem label="* 3.1">请根据<a href="https://yuque.antfin-inc.com/wireless-orange/wiki/bazf52#dWABu"
                                        target="_blank"> 帮助文档</a>自助排查，仍不能确认，请将扫码后的对比截图 和 当前页面地址给管理员 </FormItem>

        </Form>
      </div>);
  }

  _renderWmccView(wmccView) {
    return (<span> {wmccView.message || '-'} {wmccView.id ?
      <a target="_blank" href={UrlUtils.getWmccUrl(wmccView.id)}>详情</a> : null}</span>)
  }

  _renderIndex(indexBO, selectVersionBO) {
    if (!indexBO.version) {
      return null;
    }
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    return (
      <div>


        <Form className={styles.form}>
          <FormItem label="appIndexVersion">{indexBO.version}<a href={UrlUtils.getResourceUrl(indexBO.resourceId)}
                                                                target="_blank"><Icon
            type="download"/></a> </FormItem>
          <FormItem label="versionVersion">{selectVersionBO.version}</FormItem>
          <Row>
            <Col>
              <FormItem label="索引发布">{indexBO.gmtCreateTime} </FormItem>
            </Col>
          </Row>
        </Form>
      </div>);

  }

  _renderContent(selectVersionBO, versionBO, indexBO, namespaceBO, wmccView, loading) {
    if (selectVersionBO && selectVersionBO.version) {
      const {namespaceId} = this.state;
      const qrUrl = UrlUtils.getQrUrl(namespaceBO.name, namespaceBO.appKeyOrGroup, "*", selectVersionBO.version, indexBO.appIndexVersion);
      console.log(qrUrl);
      return (<div>
        <div style={{"display": " -webkit-box"}}>
          {this._renderTips(selectVersionBO, versionBO, indexBO, wmccView)}
          <div style={{"width": "50%", "height": '200px'}}>
            <div style={{'marginTop': '80px', 'textAlign': 'center'}}>
              <div style={{'marginRight': '50px'}}>
                <h4>扫码验证</h4>
                <QRCode value={qrUrl}/>
                <div style={{'marginTop': '20px', 'lineHeight': '25px', 'fontSize': '16px'}}>
                  <div><span>versionVersion</span>:<span>{selectVersionBO.version}</span></div>
                  <div><span>appIndexVersion</span>:<span>{indexBO.appIndexVersion}</span><a
                    href={UrlUtils.getResourceUrl(indexBO.resourceId)}
                    target="_blank"><Icon type="download"/></a></div>
                  <div><span>索引生成时间</span>:<span>{indexBO.gmtCreateTime}</span></div>
                </div>

              </div>

            </div>
          </div>
        </div>
      </div>);
    }
    return (<div>
      <div style={{"fontSize": "20px", margin: "100px 300px"}}>
        该配置尚未发布过!!
      </div>
    </div>);
  }

  _renderVersionList(userMap, list, selectVersionBO, indexBO, loading) {
    if (list.length <= 0) {
      return null;
    }
    const that = this;

    const _onSelectVersion = function (value) {
      if (value) {
        that.setState({
          selectVersionBO: value
        });
      }
    }
    return (
      <div>
        <h2>生效版本</h2>
        <div>
          <VersionList data={list} loading={loading} selectVersionBO={selectVersionBO} userMap={userMap}
                       onSelectVersion={_onSelectVersion}/>
        </div>
      </div>);
  }

  render() {
    let that = this;
    const {dispatch, pageData} = this.props;
    let {selectVersionBO, namespaceId} = this.state;
    const {detail, loading} = pageData;
    const versionBO = detail && detail.versionBO || {};
    const appBO = detail && detail.appBO || {};
    const wmccView = detail && detail.wmccView || {};
    const userMap = detail && detail.userMap || {};


    const namespaceBO = detail && detail.namespaceBO || {};
    const indexBO = detail && detail.indexBO || {};
    const list = detail && detail.list || [];
    if (!selectVersionBO && versionBO.version) {
      selectVersionBO = versionBO;
    }

    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>配置管理</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getNamespaceDetail(namespaceId, null)}>配置详情</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>Debug详情</Breadcrumb.Item>
        </Breadcrumb>
        <div style={{"maxWidth": "1200px"}}>
          {that._renderDesc(namespaceBO, appBO)}
          {that._renderContent(selectVersionBO, versionBO, indexBO, namespaceBO, wmccView, loading)}
          {that._renderVersionList(userMap, list, selectVersionBO, indexBO, loading)}

        </div>


      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(TypeDetail);
