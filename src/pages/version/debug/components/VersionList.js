import React from 'react';
import {Button, Table, Icon} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';

import styles from './List.less';
import {LinkUtils} from '@/utils/LinkUtils';
import {UserUtils} from "@/utils/UserUtils";
import {ConvertUtils} from "@/utils/ConvertUtils";

export default class VersionList extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      resourceId2: null,
      version2: null,
      resourceBO2: {},
    };
  }

  componentWillReceiveProps(nextProps) {
    /*if (JSON.stringify(nextProps.resourceBO2) != JSON.stringify(this.props.resourceBO2)) {
      this.setState({
        resourceBO2: nextProps.resourceBO2 || ' ',
      });
    }*/

  }


  getColumns = (userMap, indexBO, onSelectVersion, selectVersionBO) => {


    const self = this;
    const columns = [
      {
        dataIndex: 'version',
        title: 'Version',
        render(text, record) {
          return (
            <div>
              {record.version} {selectVersionBO && selectVersionBO.version && selectVersionBO.version == text ? (
              <span style={{color: "red"}}>
                  <Icon type="check"/>
                </span>
            ) : null}
            </div>
          );
        },
      }, {
        dataIndex: 'strategy',
        title: 'strategy'
      }, {
        dataIndex: 'loadLevel',
        title: '加载级别',
        render(text, record) {
          return (
            <div>
              {ConvertUtils.getLoadLevelName(text)}
            </div>
          );
        },
      },
      {
        dataIndex: 'creator',
        title: '创建',
        render(text) {
          return <span>{UserUtils.getUserDisplayName(text, userMap)}</span>;
        },
      },
      {
        dataIndex: 'gmtPublishTime',
        title: '发布时间',
      },
      {
        title: '索引已更新',
        render(text, record) {
          const indexTime = indexBO && indexBO.gmtCreateTime || '';
          const versionTime = indexBO && indexBO.gmtPublishTime || '';
          if (indexTime < versionTime) {
            return (<div><span style={{color: "red"}}>N</span> <Link
              to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>详情</Link></div>);

          } else {
            return (<div>Y</div>);
          }

        },
      },
      {
        title: '操作',
        render(text, record) {
          if (record.code == 'current') {
            return null;
          }
          return (<div><Button type="dashed" size="small"
                               onClick={function () {
                                 onSelectVersion && onSelectVersion(record);
                               }}
          >
            查看
          </Button> <Link to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>详情</Link>
          </div>);
        },
      }

    ];
    return columns;
  };

  removeItem = () => {
  };

  render() {
    const self = this;
    const {loading} = this.props;

    let {data, indexBO, onSelectVersion, selectVersionBO, userMap} = this.props;
    if (data && data[0]) {
    } else {
      data = [];
    }


    return (
      <div className={styles.holder}>
        <Table
          loading={loading}
          columns={this.getColumns(userMap, indexBO, onSelectVersion, selectVersionBO)}
          dataSource={data}
          rowKey={record => record.id}
          pagination={false}
        />
      </div>
    );
  }
}
