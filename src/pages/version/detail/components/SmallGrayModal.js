import React from 'react';
import {
  Modal,
  Button,
  message,
  Spin,
  Select,
  Icon,
  Popconfirm,
} from '@alipay/bigfish/antd';
import { createTigaTask, getTigaSuggestTemplateList } from '@/services/version';
import { UrlUtils } from '@/utils/LinkUtils';

const smallGrayDocUrl = 'https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8aZxe5myC0nggMRDWgN7R35y';

export default class SmallGrayModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible: false,
      taskId: '',
      iframeHeight: this.getIframeHeight(),
      loading: false,
      modalWidth: this.getModalWidth(),
      templateSelectVisible: false,
      templateList: [],
      selectedTemplate: undefined,
      loadingTemplates: false,
    };
    this.iframeRef = React.createRef();
  }

  componentDidMount() {
    // 添加窗口大小变化监听器
    window.addEventListener('resize', this.handleResize);
  }

  componentWillUnmount() {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.handleResize);
  }

  getIframeHeight = () => {
    // 屏幕高度减去上下的间隙（约 200px，包括 modal 标题栏、padding 等）
    return window.innerHeight - 200;
  };

  getModalWidth = () => {
    return window.innerWidth > 1512 ? 1500 : 1300;
  };

  handleResize = () => {
    const newWidth = this.getModalWidth();
    const newHeight = this.getIframeHeight();
    if (newWidth !== this.state.modalWidth || newHeight !== this.state.iframeHeight) {
      this.setState({
        modalWidth: newWidth,
        iframeHeight: newHeight,
      });
    }
  };

  show = () => {
    const { versionBO } = this.props;

    // 检查是否已有小流量灰度任务
    if (versionBO.tigaMetadata) {
      try {
        const metadata = JSON.parse(versionBO.tigaMetadata);
        this.setState({
          modalVisible: true,
          taskId: metadata.taskId,
          loading: false,
        });
      } catch (error) {
        console.warn('解析 tigaMetadata 失败:', error);
        // 如果解析失败，当作没有任务处理
        this.showTemplateSelectModal();
      }
    } else {
      // 如果没有 tigaMetadata，先显示模板选择模态框
      this.showTemplateSelectModal();
    }
  };

  hide = () => {
    this.setState({
      modalVisible: false,
      taskId: '',
      iframeHeight: this.getIframeHeight(),
      loading: false,
      templateSelectVisible: false,
      selectedTemplate: undefined,
      templateList: [],
      loadingTemplates: false,
    });
  };

  showTemplateSelectModal = () => {
    this.setState({
      templateSelectVisible: true,
    });
    this.loadTemplateList();
  };

  handleSkip = () => {
    const { onHandleSkip } = this.props;
    if (onHandleSkip) {
      onHandleSkip();
    }
    this.hide();
  };

  loadTemplateList = async (keywords) => {
    try {
      this.setState({ loadingTemplates: true });
      const { versionBO } = this.props;
      const result = await getTigaSuggestTemplateList({
        namespaceId: versionBO.namespaceId,
        version: versionBO.version,
        ...(keywords && { keywords })
      });

      this.setState({
        templateList: result|| [],
        loadingTemplates: false,
      });

      // 默认选中第一个模板
      if (result.length > 0 && !this.state.selectedTemplate) {
        this.setState({
          selectedTemplate: result[0].id,
        });
      }
    } catch (error) {
      console.error('获取模板列表失败:', error);
      message.error(`获取模板列表失败: ${error.message}`);
      this.setState({ loadingTemplates: false });
    }
  };

  handleTemplateSearch = (value) => {
    this.loadTemplateList(value);
  };

  handleTemplateConfirm = async () => {
    const { selectedTemplate } = this.state;
    if (!selectedTemplate) {
      message.error('请选择一个模板');
      return;
    }

    try {
      const { versionBO } = this.props;
      const taskId = await createTigaTask({
        namespaceId: versionBO.namespaceId,
        version: versionBO.version,
        templateId: selectedTemplate
      });

      this.setState({
        taskId: taskId,
        templateSelectVisible: false,
        modalVisible: true,
        selectedTemplate: undefined,
      });

      // 通知父组件刷新版本数据
      if (this.props.onTaskCreated) {
        this.props.onTaskCreated();
      }
    } catch (error) {
      console.error('创建小流量灰度任务失败:', error);
      message.error(`创建小流量灰度任务失败: ${error.message}`);
    }
  };

  getTaskDetailUrl = (taskId) => {
    if (!taskId) return '';

    return UrlUtils.getTigaTaskDetailUrl(taskId);
  };

  render() {
    const {
      modalVisible,
      taskId,
      iframeHeight,
      loading,
      modalWidth,
      templateSelectVisible,
      templateList,
      selectedTemplate,
      loadingTemplates
    } = this.state;

    return (
      <>
        {/* 模板选择模态框 */}
        <Modal
          title={
            <span>
              选择小流量灰度模板
              <a
                href={smallGrayDocUrl}
                target="_blank"
                rel="noreferrer"
                style={{ marginLeft: 10 }}
              >
                操作文档
              </a>
            </span>
          }
          visible={templateSelectVisible}
          onCancel={() => {
            this.setState({
              templateSelectVisible: false,
              selectedTemplate: undefined,
            });
          }}
          onOk={this.handleTemplateConfirm}
          confirmLoading={loadingTemplates}
          width={600}
          footer={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ color: 'rgba(0,0,0,.45)', display: 'flex', alignItems: 'center' }}>
                <Icon type="info-circle" style={{ color: '#1890ff', marginRight: 6 }} />
                可前往&nbsp;
                <a href={UrlUtils.getTigaTemplateCenterUrl()} target="_blank" rel="noreferrer" style={{ color: '#1677ff' }}>
                  Tiga 灰度模板中心
                </a>
                &nbsp;自定义更多模板
              </div>
              <div>
                <Popconfirm title="当前跳过不符合安全生产规范，申请跳过需要审批。审批通过后此审批单将不做灰度管控，确认要跳过吗？" onConfirm={this.handleSkip}>
                  <Button>申请跳过</Button>
                </Popconfirm>
                <Button
                  onClick={() => {
                    this.setState({ templateSelectVisible: false, selectedTemplate: undefined });
                  }}
                >
                  取消
                </Button>
                <Button type="primary" onClick={this.handleTemplateConfirm} loading={loadingTemplates} style={{ marginLeft: 8 }}>
                  确定
                </Button>
              </div>
            </div>
          }
        >
          <Select
            placeholder="搜索并选择模板"
            style={{ width: '100%' }}
            value={selectedTemplate}
            onChange={(value) => this.setState({ selectedTemplate: value })}
            loading={loadingTemplates}
            showSearch
            filterOption={false}
            onSearch={this.handleTemplateSearch}
            notFoundContent={loadingTemplates ? '加载中...' : '未找到模板'}
          >
            {templateList.map((template) => (
              <Select.Option key={template.id} value={template.id}>
                {template.name}
              </Select.Option>
            ))}
          </Select>
        </Modal>

        {/* 小流量灰度详情模态框 */}
        <Modal
          title={
            <span>
              小流量灰度
              <a
                href={smallGrayDocUrl}
                target="_blank"
                rel="noreferrer"
                style={{ marginLeft: 10 }}
              >
                操作文档
              </a>
            </span>
          }
          visible={modalVisible}
          onCancel={this.hide}
          footer={
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Popconfirm title="当前跳过不符合安全生产规范，申请跳过需要审批。审批通过后此审批单将不做灰度管控，确认要跳过吗？" onConfirm={this.handleSkip}>
                <Button>申请跳过</Button>
              </Popconfirm>
              <Button onClick={this.hide} style={{ marginLeft: 8 }}>关闭</Button>
            </div>
          }
          width={modalWidth}
          centered
        >
          <Spin spinning={loading}>
            {taskId && (
              <iframe
                ref={this.iframeRef}
                src={this.getTaskDetailUrl(taskId)}
                width="100%"
                height={`${iframeHeight}px`}
                style={{
                  border: 0,
                  flex: 1,
                }}
              />
            )}
            {!loading && !taskId && (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <p>加载任务详情失败</p>
                <Button onClick={this.hide}>关闭</Button>
              </div>
            )}
          </Spin>
        </Modal>
      </>
    );
  }
}
