import React from 'react';
import { Button, Table, message } from '@alipay/bigfish/antd';

import styles from './List.less';
import { UrlUtils } from '@/utils/LinkUtils';
import { ConvertUtils } from '@/utils/ConvertUtils';
import { UserUtils } from '@/utils/UserUtils';

import { getMassPushResult } from '@/services/version';
import { AVAILABLEL_YES } from '@/constants/version';
import {
  RECORD_STATUS_SUCCESS,
  RECORD_TYPE_APPLY,
  RECORD_TYPE_BETA,
  RECORD_TYPE_GRAY,
  RECORD_TYPE_SKIP,
  RECORD_PARAMS_SHOWS,
  RECORD_RESULT_SHOWS,
  RECORD_TYPE_RATIO_GRAY,
} from '@/constants/record';

import ShowGrayResult from './ShowGrayResult';
import ShowBetaResult from './ShowBetaResult';
import { GRAY_RATIO_UNIT } from './GrayCircleModal';

export default class RecordList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      resourceId2: null,
      version2: null,
      refreshTime: null,
    };
  }

  getColumns = (
    userMap,
    refreshSubmit,
    getBetaResult,
    getMassCircleResult,
    oneStepSkip,
    checkHoldShow,
  ) => {
    const self = this;
    const columns = [
      {
        dataIndex: 'id',
        title: 'id',
      },
      {
        dataIndex: 'type',
        title: '操作名称',
        render(text) {
          return <span>{ConvertUtils.getRecordTypeName(text)}</span>;
        },
      },
      {
        dataIndex: 'creator',
        title: '操作人',
        render(text) {
          return <span>{UserUtils.getUserDisplayName(text, userMap)}</span>;
        },
      },
      {
        dataIndex: 'gmtCreateTime',
        title: '相关时间',
        width: 220,
        render(text, record) {
          return (
            <div>
              <div>创建：{record.gmtCreateTime}</div>
              <div>更新：{record.gmtModifiedTime}</div>
            </div>
          );
        },
      },
      {
        title: '生效中',
        dataIndex: 'isAvailable',
        render(text) {
          return <span>{ConvertUtils.getAvailableName(text)}</span>;
        },
      },
      {
        dataIndex: 'status',
        title: '状态',
        render(text) {
          return <span>{ConvertUtils.getRecordStatusName(text)}</span>;
        },
      },
      {
        title: '描述',
        width: 300,
        render(text, record) {
          if (!record.ver || record.ver == 1) {
            return null;
          }
          const paramDO = record.paramDO || {};
          const resultDO = record.resultDO || {};

          const changefreeUrl =
            record.type == RECORD_TYPE_APPLY && resultDO.changefreeOrderId
              ? UrlUtils.getChangeFreeUrl(resultDO.changefreeOrderId)
              : null;
          const bpmsUrl =
            record.type == RECORD_TYPE_SKIP && record.arg0
              ? UrlUtils.getBpmsUrl(record.arg0)
              : null;
          const grayRatio =
            record.type === RECORD_TYPE_RATIO_GRAY
              ? record.paramDO && record.paramDO.grayRatio
              : null;

          return (
            <div>
              {RECORD_PARAMS_SHOWS.map((item) => {
                const key = item.value;
                if (paramDO[key]) {
                  return (
                    <div key={key}>
                      {item.label}: {paramDO[key]}
                    </div>
                  );
                } else {
                  return null;
                }
              })}
              {RECORD_RESULT_SHOWS.map((item) => {
                const key = item.value;
                if (resultDO[key]) {
                  return (
                    <div key={key}>
                      {item.label}: {resultDO[key]}
                    </div>
                  );
                } else {
                  return null;
                }
              })}
              {changefreeUrl ? (
                <a href={changefreeUrl} target="_blank">
                  {' '}
                  CF地址
                </a>
              ) : null}
              {bpmsUrl ? (
                <a href={bpmsUrl} target="_blank">
                  {' '}
                  BPMS地址
                </a>
              ) : null}
                {grayRatio ? `灰度比例: ${grayRatio * 100 / GRAY_RATIO_UNIT}%` : null}
            </div>
          );
        },
      },
      {
        title: '检测',
        dataIndex: 'checkResult',
        width: 50,
        render(text, record) {
          if (!record.ver || record.ver == 1) {
            return null;
          }
          const checkResultDO = record.checkResultDO || {};
          if (!checkResultDO.checkStatus) {
            return null;
          }
          const hasList = checkResultDO.details ? true : false;
          return (
            <div>
              <p>{ConvertUtils.getCheckStatusShow(checkResultDO.checkStatus)}</p>
              <p>
                {!hasList ? null : (
                  <Button
                    type="dashed"
                    size="small"
                    onClick={function () {
                      checkHoldShow && checkHoldShow(checkResultDO, record.id);
                    }}
                  >
                    查看
                  </Button>
                )}
              </p>
            </div>
          );
        },
      },
      {
        title: '操作',
        render(text, record) {
          if (!record.ver || record.ver == 1) {
            return null;
          }
          const paramDO = record.paramDO || {};
          const resultDO = record.resultDO || {};
          const massId = resultDO.massTaskId || '';

          const showApply =
            record.isAvailable == AVAILABLEL_YES &&
            record.type == RECORD_TYPE_APPLY &&
            record.status != RECORD_STATUS_SUCCESS;
          const showBeta =
            record.isAvailable == AVAILABLEL_YES &&
            record.status == RECORD_STATUS_SUCCESS &&
            record.type == RECORD_TYPE_BETA;
          const showGray = record.isAvailable == AVAILABLEL_YES && record.type == RECORD_TYPE_GRAY;
          const showSkip = record.isAvailable == AVAILABLEL_YES && record.type == RECORD_TYPE_SKIP;
          if (showApply && record.arg0) {
            return (
              <div>
                <Button
                  type="dashed"
                  size="small"
                  onClick={function () {
                    refreshSubmit && refreshSubmit(record.id, RECORD_TYPE_APPLY);
                  }}
                >
                  刷新CF
                </Button>
              </div>
            );
          } else if (showBeta) {
            const massResult = (
              <Button
                type="dashed"
                size="small"
                onClick={function () {
                  getBetaResult && getBetaResult(record, massId);
                }}
              >
                详细
              </Button>
            );
            return <div>{paramDO.utdids || massId ? massResult : null}</div>;
          } else if (showGray) {
            const massResult = (
              <Button
                type="dashed"
                size="small"
                onClick={function () {
                  getMassCircleResult && getMassCircleResult(record, massId);
                }}
              >
                任务查看
              </Button>
            );
            if (massId) {
              return <div>{massId ? massResult : ''}</div>;
            }
          } else if (showSkip) {
            if (record.status != RECORD_STATUS_SUCCESS && record.arg0) {
              return (
                <div>
                  <Button
                    type="dashed"
                    size="small"
                    onClick={function () {
                      checkSubmit && checkSubmit(record.arg0, RECORD_TYPE_SKIP);
                    }}
                  >
                    刷新BPMS
                  </Button>
                </div>
              );
            }
          }
          return <div></div>;
        },
      },
    ];
    return columns;
  };

  removeItem = () => {};

  render() {
    const self = this;
    const { loading, refreshSubmit, oneStepSkip, checkHoldShow, userMap } = this.props;
    const { refreshTime } = this.state;

    let { data } = this.props;
    let pagination = false;
    if (data && data[0]) {
      if (pagination > 10) {
        pagination = { pageSize: 10 };
      }
    } else {
      data = [];
    }
    const _getBetaResult = function (record, massTaskId) {
      if (!massTaskId || ('' + massTaskId).indexOf('mass') < 0) {
        self.refs.betaResultModal.show(null, record);
        return;
      }
      getMassPushResult({
        recordId: record.id,
        massTaskId: massTaskId,
        version: record.version,
        namespaceId: record.namespaceId,
      }).then((res) => {
        self.refs.betaResultModal.show(res, record);
      });
    };

    const _getMassCircleResult = function (record, massTaskId) {
      getMassPushResult({
        recordId: record.id,
        massTaskId: massTaskId,
        version: record.version,
        namespaceId: record.namespaceId,
      }).then((res) => {
        self.refs.grayResultModal.show(res, record);
      });
    };

    const _refreshSubmit = function (arg0, refreshType) {
      const currentRefreshTime = new Date().getTime();
      //60s点击1次
      if (refreshTime != null && currentRefreshTime - refreshTime < 60 * 1000) {
        message.error(
          '刷新按钮，用于bpms或者changefree 回调异常时做手动触发，1分钟最多允许点击1次~~',
        );
        return;
      }
      self.setState({ refreshTime: currentRefreshTime });
      refreshSubmit && refreshSubmit(arg0, refreshType);
    };

    return (
      <div className={styles.holder}>
        <Table
          loading={loading}
          columns={this.getColumns(
            userMap,
            _refreshSubmit,
            _getBetaResult,
            _getMassCircleResult,
            oneStepSkip,
            checkHoldShow,
          )}
          dataSource={data}
          rowKey={(record) => record.id}
          pagination={pagination}
        />
        <ShowGrayResult ref="grayResultModal" />
        <ShowBetaResult ref="betaResultModal" />
      </div>
    );
  }
}
