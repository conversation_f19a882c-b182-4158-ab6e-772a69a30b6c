import React from 'react';
import {<PERSON>, Modal, <PERSON>, But<PERSON>, Popconfirm, message, Table} from '@alipay/bigfish/antd';
import {CHECK_STATUS_PASS, CHECK_STATUS_HOLD, SKIP_SUPPORT_NONE, SKIP_SUPPORT_BPMS} from "@/constants/record";

import {ConvertUtils} from "@/utils/ConvertUtils";
import {formatTime} from '@/utils/TimeUtils';
import {isSystemAdmin} from "@/utils/EnvUtils";
import {RECORD_STATUS_PROCESSING, RECORD_STATUS_SUCCESS} from "@/constants/record";
import {UrlUtils} from "@/utils/LinkUtils";


const FormItem = Form.Item;


const formLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 17,
    },
};

export default class CheckHoldResultModal extends React.Component {

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }

    getInitState = () => {
        return {
            checkResult: undefined,
            nextProcess: undefined,
            oneStepSkip: false,
            lastOrSuccessCheckSkipRecord: null,
            recordId: null,
            visible: false,
            checking: false,
            loading: false,
        };

    };

    show = (checkResult, oneStepSkip, nextProcess, recordId, lastOrSuccessCheckSkipRecord) => {
        this.setState({
            visible: true,
            oneStepSkip: oneStepSkip,
            nextProcess: nextProcess,
            checkResult: checkResult,
            recordId: recordId,
            lastOrSuccessCheckSkipRecord: lastOrSuccessCheckSkipRecord

        });
    };

    handleOk = () => {
        let that = this;
        let {nextProcess, recordId} = this.state;
        if (!recordId) {
            nextProcess && nextProcess();
        }
        this.state = this.getInitState();
        this.setState({
            visible: false,
            checkResult: undefined,
            nextProcess: undefined,
            oneStepSkip: false,
            lastOrSuccessCheckSkipRecord: null,
            recordId: null,

        });
    };

    handleCancel = () => {
        this.state = this.getInitState();

        this.setState({
            visible: false,
            content: undefined
        });
    };


    componentWillReceiveProps(nextProps) {
        /*if (JSON.stringify(nextProps.differProps) != JSON.stringify(this.props.differProps)) {
          this.setState({
            differProps: nextProps.differProps,
          });
        }*/
    }


    getColumns = () => {


        const self = this;
        const columns = [
            {
                dataIndex: 'provider',
                title: '来源',
                render(text) {
                    return ConvertUtils.getCheckProviderName(text);
                }
            },
            {
                dataIndex: 'status',
                title: '状态',
                render(text) {
                    return ConvertUtils.getCheckStatusShow(text);
                }
            },
            {
                title: '后续',
                dataIndex: 'skipSupport',
                render(text, record) {
                    if (record.status == CHECK_STATUS_PASS) {
                        return '完成';
                    }
                    return ConvertUtils.getCheckSkipSupportName(text);

                }
            },
            {
                dataIndex: 'detailUrl',
                title: '详情',
                render(text, record) {
                    if (record.approvalUrl) {
                        return <a href={record.approvalUrl} target="_blank">审批地址</a>
                    }
                    return text ? <a href={text} target="_blank">详情查看</a> : null;
                },
            },
            {
                dataIndex: 'message',
                title: '描述',
                width: 500,
            },

        ];
        return columns;
    };

    render() {
        const {onHandleSkip} = this.props;
        const {visible, loading, checkResult, oneStepSkip, recordId, lastOrSuccessCheckSkipRecord} = this.state;
        if (!checkResult) {
            return null;
        }
        const hasCheckSkip = lastOrSuccessCheckSkipRecord && lastOrSuccessCheckSkipRecord.status == RECORD_STATUS_SUCCESS;
        const isCheckSkipProcessing = lastOrSuccessCheckSkipRecord && lastOrSuccessCheckSkipRecord.status == RECORD_STATUS_PROCESSING;
        const bpmsUrl = isCheckSkipProcessing ? UrlUtils.getBpmsUrl(lastOrSuccessCheckSkipRecord.arg0) : null;

        const that = this;
        const details = checkResult.details || [];
        const checkStatus = checkResult.checkStatus || '';
        const isSecAdmin = oneStepSkip || isSystemAdmin();
        const bpmsList = details.filter(item => item.skipSupport == SKIP_SUPPORT_BPMS);
        const hasSupportBpms = bpmsList.length > 0;
        const noneList = details.filter(item => item.skipSupport == SKIP_SUPPORT_NONE);
        const hasSupportNone = noneList.length > 0;
        const showBpmsButton = !recordId && !hasSupportNone && hasSupportBpms && !hasCheckSkip;
        const bpmsProblem = bpmsList.map(item => item.message).join(";");
        //是否可以显示继续
        let canNext = true;
        if (!recordId && !isSecAdmin) {
            if (hasSupportNone) {
                canNext = false;
            } else if (hasSupportBpms && !hasCheckSkip) {
                canNext = false;
            }
        }
//卡口跳过
        const _applySkipCheckHold = function () {
            onHandleSkip && onHandleSkip(bpmsProblem);
            that.setState({
                visible: false,
            });
        };
        const _showBpmsButton = function (primary) {
            if (!showBpmsButton) {
                return null;
            }
            return <Popconfirm
                title="卡口跳过申请主管审批，若审批通过，后续所有卡口都将失效，需要业务方自行判断是否继续，确认申请跳过吗？"
                onConfirm={() => {
                    _applySkipCheckHold();
                }}
            >
                <Button type={primary}>跳过卡口</Button>
            </Popconfirm>
        }
        const _renderBpmsTips = function () {
            if (hasCheckSkip) {
                return <div><span style={{"color": "red"}}>卡口检测遇到了阻断，已经申请过卡口跳过，操作者若判断没有问题可点击强制继续执行后续逻辑~~</span>
                    <span> {_showBpmsButton()} </span></div>
            } else if (isCheckSkipProcessing) {
                return <div>
                    <span
                        style={{"color": "red"}}>卡口检测遇到了阻断，申请的卡口跳过处于审批过程中，<a href={bpmsUrl}
                                                                                 target="_blank">查看审批详情</a> ,若bpms流程异常可重新申请 </span>
                    <span>
                       {_showBpmsButton("default")}
                    (不要重复申请)</span></div>
            } else {
                return <div>
                    <span
                        style={{"color": "red"}}>卡口检测遇到了阻断，操作者若判断没有问题，点击 <span>
                       {_showBpmsButton("primary")}
                    </span> 申请并审批成功后才可执行后续逻辑~~ </span>
                    </div>
            }
        }

        const _renderTips = function () {
            if (recordId) {

            } else if (hasSupportNone) {
                return <div><span style={{"color": "red"}}>卡口检测遇到了封网阻断，请在点击 <a href={noneList[0].approvalUrl}
                                                                               target="_blank">审批地址</a> 执行后续逻辑，或者等待封网结束~~   </span>
                </div>
            } else if (hasSupportBpms) {
                return _renderBpmsTips();
            } else if (checkStatus != CHECK_STATUS_PASS) {
                return <div><span style={{"color": "red"}}>卡口检测遇到了阻断或异常，支持 强制继续 继续放量，请根据情况判断是否继续~~   </span></div>
            }
            return null;
        }

        const okButtonProps = !canNext ? {disabled: true} : {};
        const okText = (recordId || checkResult.checkStatus == CHECK_STATUS_PASS) ? "确认" : "强制继续";

        return (<Modal title="卡口检测结果" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}
                       okText={okText} cancelText="关闭"
                       okButtonProps={okButtonProps}
                       width={900}
                       destroyOnClose>
            <div>
                <div>检测结果：{ConvertUtils.getCheckStatusShow(checkResult.checkStatus)}</div>
                <p></p>
                <div>检测时间：{formatTime(checkResult.lastCheckTime)}</div>
                <p></p>
                <div>
                    每次放量(定量灰度或正式发布)前会执行卡口检测，若检测均通过或允许跳过方可执行后续逻辑，否则需要等待或者执行相应的审批流程.<a
                    href="https://yuque.antfin-inc.com/wireless-orange/wiki/ggvqdb#BK4al" target="_blank">文档</a>。
                </div>
                <p></p>

            </div>
            <Spin spinning={loading}>
                <Table
                    loading={loading}
                    columns={this.getColumns()}
                    dataSource={checkResult.details}
                    rowKey={record => record.provider}
                    pagination={false}
                />
            </Spin>
            <div style={{"marginLeft": "20px", "marginTop": "20px"}}>
                {_renderTips()}
            </div>
        </Modal>)
            ;

    }

}
