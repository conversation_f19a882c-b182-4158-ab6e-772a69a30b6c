import React from 'react';
import {Form, Input, Modal, Radio, Spin} from '@alipay/bigfish/antd';

import {checkForm} from '@/utils/utils';

const FormItem = Form.Item;
const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};

export default class ApplyModal extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      formData: {
        isEmergent: 'n',
      },
      visible: false,
      checking: false,
      loading: false,
    };
  };

  show = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        const {formData} = this.state;
        let {onHandleOk} = this.props;
        onHandleOk && onHandleOk(formData);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    this.setState(this.getInitState());
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);

    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'isEmergent': {
          if (!value) {
            help = '必选';
          }
          break;
        }
        case 'reason': {
          if (!value) {
            help = '必选';
          }
          if (value.length > 64) {
            help = '64个字符以内';
          }
          break;
        }
      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  render() {
    let {getFormProps, changeForm} = this;
    const {visible, formData, loading} = this.state;
    return (
      <Modal title="申请发布" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}>
        <Spin spinning={loading}>
          <Form ref="form" layout="horizontal">
            <FormItem label="生效类型" {...getFormProps('isEmergent')} required help="仅回滚、故障类允许申请立即生效，需BU负责人审批，影响全局稳定性。">
              <Radio.Group value={formData.isEmergent}
                           onChange={(v) => {
                             changeForm({isEmergent: v.target.value});
                           }}>
                <Radio value="n">普通生效(约10分钟,推荐)</Radio>
                <Radio value="y">立即生效(谨慎,约2分钟,影响全局)</Radio>
              </Radio.Group>
            </FormItem>
            <FormItem label="申请原因" {...getFormProps('reason')} required help="一句话说明发布原因，64个字符以内">
              <Input.TextArea
                value={formData.reason}
                rows={5}
                onChange={(e) => {
                  changeForm({reason: e.target.value});
                }}
              />
            </FormItem>

          </Form>
        </Spin>
      </Modal>
    );
  }
}
