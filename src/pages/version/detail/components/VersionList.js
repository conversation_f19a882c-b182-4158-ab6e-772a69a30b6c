import React from 'react';
import { But<PERSON>, Table } from '@alipay/bigfish/antd';
import { Link } from 'dva/router';

import styles from './List.less';
import { LinkUtils } from '@/utils/LinkUtils';

import { UserUtils } from '@/utils/UserUtils';
import StatusSpan from '@/components/StatusSpan';
import CodeDiffDetail from '../../../namespace/detail/components/CodeDiffDetail';

import { getResourceDetail } from '@/services/version';
import { NamespaceUtils } from '@/utils/NamespaceUtils';

export default class VersionList extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      resourceId2: null,
      version2: null,
      resourceBO2: {}
    };
  }

  componentDidMount() {
    const { data } = this.props;
    if (this.props.showDiff) {
      // 本次发布新增的版本
      const newVersion = data?.find(i => i.valid === 'info' && i.isAvailable === 'n') || {};
      // 本次发布被删除的版本列表
      const deleteVersions = data?.filter(i => i.valid === 'fail' && i.isAvailable === 'y') || [];
      const replaceVersion = deleteVersions?.find(i => newVersion.strategy == i.strategy);
      // 替换原有策略
      if (replaceVersion) {
        const title = newVersion.strategy ? `策略 ${newVersion.strategy} 配置变更` : '兜底策略配置变更';
        this.doCompareResource(replaceVersion.version, replaceVersion.resourceId, title);
      } else {
        const previousVersion = data?.filter(i => i.appVersion === '*')?.[1] || {};
        // 新增策略
        const title = newVersion.strategy ? `新增策略 ${newVersion.strategy}` : '新增兜底策略';
        this.doCompareResource(previousVersion.version, previousVersion.resourceId, title);
      }
    }
  }

  doCompareResource = (version2, resourceId2, title) => {
    const { versionBO, resourceBO, namespaceBO } = this.props;

    const showDiffDetail = (resourceBO2) => {
      this.setState({
        version2: version2,
        resourceId2: resourceId2,
        resourceBO2: resourceBO2
      });

      const differProps = {
        title: title,
        mode: 'json',
        dataformat: NamespaceUtils.getContentFormat(namespaceBO),
        dataTitle: version2 || ' ',
        compareTitle: versionBO.version + '(当前)' || '当前',
        data: resourceBO2 && resourceBO2.srcContent || ' ',
        compareData: resourceBO && resourceBO.srcContent || ' ',
        codeMirror: {
          readOnly: true
        }
      };

      this.refs.codeDiffDetail.show(differProps);
    }

    if (!version2 || !resourceId2) {
      showDiffDetail({});
    } else {
      getResourceDetail({ 'resourceId': resourceId2 }).then((resourceBO2) => {
        showDiffDetail(resourceBO2);
      });
    }
  };

  getColumns = (userMap, doCompareResource) => {
    const columns = [
      {
        dataIndex: 'id',
        title: 'Version',
        render(text, record) {
          return (
            <div>
              {record.version} <Link to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>详情</Link>
            </div>
          );
        }
      }, {
        dataIndex: 'appVersion',
        title: 'appVersion'
      }, {
        dataIndex: 'strategy',
        title: 'strategy'
      },
      {
        dataIndex: 'creator',
        title: '创建',
        render(text) {
          return <span>{UserUtils.getUserDisplayName(text, userMap)}</span>;
        }
      },
      {
        dataIndex: 'gmtPublishTime',
        title: '发布时间'
      },
      {
        title: '生效',
        render(text, record) {


          return <StatusSpan title={record.validTitle} type={record.valid} />;
        }
      },
      {
        title: '描述',
        width: 200,
        render(text, record) {
          return <span>{record.message}</span>;
        }

      },
      {
        title: '操作',
        render(text, record) {
          if (record.code == 'current') {
            return null;
          }
          return (<div><Button type="dashed" size="small"
                               onClick={function() {
                                 doCompareResource(record.version, record.resourceId);
                               }}
          >
            数据对比
          </Button>
          </div>);
        }
      }

    ];
    return columns;
  };

  render() {
    const self = this;
    const { loading } = this.props;

    let { data, userMap } = this.props;
    if (data && data[0]) {
    } else {
      data = [];
    }

    return (
      <div className={styles.holder}>
        <Table
          loading={loading}
          columns={this.getColumns(userMap, self.doCompareResource)}
          dataSource={data}
          rowKey={record => record.id}
          pagination={false}
        />
        <CodeDiffDetail ref="codeDiffDetail" />
      </div>
    );
  }
}
