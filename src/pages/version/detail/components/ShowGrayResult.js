import React from 'react';
import {<PERSON>, Modal, Spin, <PERSON><PERSON>, Popconfirm, message} from '@alipay/bigfish/antd';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {formatTime} from '@/utils/TimeUtils';

import {recordOperate,getMassDeviceList} from '@/services/version';

import {RECORD_OPER_CANCEL} from "@/constants/record";
import ShowDeviceList from "./ShowDeviceList";


const FormItem = Form.Item;


const formLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 17,
    },
};

export default class ShowGrayResult extends React.Component {

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }

    getInitState = () => {
        return {
            content: undefined,
            record: undefined,
            visible: false,
            checking: false,
            loading: false,
        };

    };

    show = (content, record) => {
        this.setState({
            visible: true,
            content: content,
            record: record

        });
    };

    handleOk = () => {
        this.state = this.getInitState();
        this.setState({
            visible: false,
            record: undefined,
            content: undefined

        });
    };

    handleCancel = () => {
        this.state = this.getInitState();

        this.setState({
            visible: false,
            content: undefined
        });
    };


    componentWillReceiveProps(nextProps) {
        /*if (JSON.stringify(nextProps.differProps) != JSON.stringify(this.props.differProps)) {
          this.setState({
            differProps: nextProps.differProps,
          });
        }*/
    }


    div;

    render() {
        let {visible, loading, content, record} = this.state;
        if (!record) {
            return null;
        }
        const paramDO = record.paramDO || {};
        const resultDO = record.resultDO || {};
        const that = this;
        const highLightCancel = content && content.taskCreateTime && (new Date().getTime() - content.taskCreateTime > 10 * 60 * 1000);
        const showStop = content && ['INIT_STATUS', 'SPLIT_STATUS', 'PROCESSING_STATUS', 'BIZ_PAUSE_STATUS'].includes('' + content.status);

        const showFinishTime = content && content.taskFinishTime;
            const _stopTask = function () {

            recordOperate({'recordId': record.id, oper: RECORD_OPER_CANCEL}).then((res) => {
                message.info('成功了~~')
                that.setState({
                    visible: false
                });
            });
        }

        const _renderDeviceList = function () {
            //是否展示设备列表，7天内
            const showDeviceList = content && content.ackNum && content.taskCreateTime && (new Date().getTime() - content.taskCreateTime < 7 * 24 * 60 * 60 * 1000);
            if(!showDeviceList){
                return null;
            }
            return <span>  <Button  type="primary"   onClick={function () {
                getMassDeviceList({
                    recordId: record.id,
                    massTaskId: resultDO.massTaskId,
                    version: record.version,
                    namespaceId: record.namespaceId,
                }).then((res) => {
                    that.refs.deviceListModal.show(res, record);
                });
            }}>明细</Button></span>
        };

        this.div =
            <div><Modal title="灰度结果" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel} width={850}
                        destroyOnClose>
                <Spin spinning={loading}>
                    <Form ref="form" layout="horizontal">
                        <FormItem  {...formLayout} label="提示信息">此为ACCS 根据在线设备推送, 收到的ACK成功数；不是配置更新设备数，此数据仅用于参考。 </FormItem>
                        <FormItem  {...formLayout} label="相关设备数">{paramDO && paramDO.deviceCnt || '-'}(目标)
                            && {content && content.scanCount || '-'} (扫描到) && {content && content.sendNum || '-'}(发送)</FormItem>
                        <FormItem  {...formLayout} label="收到ACK数">{content && content.ackNum || '-'} {_renderDeviceList()} </FormItem>
                        <FormItem  {...formLayout}
                                   label="开始时间">{content && content.taskCreateTime ? formatTime(content.taskCreateTime) : '-'}  </FormItem>
                        {showFinishTime ?
                            <FormItem  {...formLayout} label="结束时间">{formatTime(content.taskFinishTime)}  </FormItem> :
                            <FormItem  {...formLayout} label="当前时间">{formatTime(new Date().getTime())}  </FormItem>}
                        <FormItem {...formLayout}
                                  label="状态">{content && ConvertUtils.getMassResultStatus(content.status) || '-'} </FormItem>

                        <FormItem>
                            {showStop ? <div style={{"marginLeft": "120px"}}>
                                {highLightCancel ?
                                    <span style={{color: "red"}}>  当前圈选任务执行时间已经超过10分钟，您可以判断是否手工取消结束该任务>>  </span> : ''}
                                <Popconfirm
                                    title="确认要取消吗？"
                                    onConfirm={() => {
                                        _stopTask();
                                    }}
                                >
                                    <Button type={highLightCancel ? "primary" : ""}>取消</Button>
                                </Popconfirm></div> : null}
                        </FormItem>

                    </Form>
                </Spin>
            </Modal>
                <ShowDeviceList ref="deviceListModal"/>
            </div>;
        return this.div;

    }

}
