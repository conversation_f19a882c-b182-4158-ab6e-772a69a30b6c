import React from 'react';
import {Form, Modal, <PERSON>, <PERSON><PERSON>, Popconfirm, message, Table, Input} from '@alipay/bigfish/antd';
import {formatTime} from '@/utils/TimeUtils';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';


const FormItem = Form.Item;


const formLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 17,
    },
};

export default class ShowDeviceList extends React.Component {

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }

    getInitState = () => {
        return {
            content: undefined,
            record: undefined,
            keyword: '',
            visible: false,
            checking: false,
            loading: false,
        };

    };

    show = (content, record) => {
        this.setState({
            visible: true,
            content: content,
            record: record,
            keyword: ''

        });
    };

    handleOk = () => {
        this.state = this.getInitState();
        this.setState({
            visible: false,
            record: undefined,
            content: undefined,
            keyword: ''

        });
    };

    handleCancel = () => {
        this.state = this.getInitState();

        this.setState({
            visible: false,
            content: undefined,
            keyword: ''
        });
    };


    componentWillReceiveProps(nextProps) {
        /*if (JSON.stringify(nextProps.differProps) != JSON.stringify(this.props.differProps)) {
          this.setState({
            differProps: nextProps.differProps,
          });
        }*/
    }

    getColumns = () => {
        const self = this;
        const columns = [{
            dataIndex: 'utdid',
            title: 'utdid'
        }, /*{
            dataIndex: 'slsTime',
            title: 'ACK时间',
            render(text) {
                return <span>{text ? formatTime(text * 1000) : null}</span>;
            },
        },*/ {
            dataIndex: 'logTime',
            title: 'ACK时间'
        }
        ];
        return columns;
    };

    render() {
        const that = this;
        let {visible, loading, content, record, keyword} = this.state;
        if (!record) {
            return null;
        }
        const deviceList = content && content.ackDeviceList || [];
        const retList = deviceList.filter(v => !keyword ? true : JSON.stringify(v).indexOf(keyword) > -1);
        let pagination = false;

        if (retList && retList[0]) {
            if (retList.length > 10) {
                pagination = {pageSize: 10}
            }
        }
        return (
            <Modal title="ACCS ACK列表" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel} width={800}
                   destroyOnClose>
                <Spin spinning={loading}>
                    <Form layout="inline" className="clearfix">
                        <FormItem label="关键字">
                            <Input style={{width: "300px"}}
                                   value={keyword}
                                   placeholder="可输入utdid，列表内过滤"
                                   onChange={(e) => {
                                       that.setState({"keyword": e.target.value || ''})
                                   }}
                            />
                        </FormItem>
                        <FormItem label="排查工具">
                        <span> <a href="https://aliyuque.antfin.com/wireless-orange/wiki/bazf52#aMizh"
                                  target="_blank">相关文档</a>
                        </span> | <span style={{"marginLeft": "2px"}}> <a
                            href={UrlUtils.getAcccsUrl()} target="_blank">ACCS</a>
                    </span> | <span style={{"marginLeft": "2px"}}> <a
                            href={UrlUtils.getTlogUrl()} target="_blank">TLOG</a>
                    </span> | <span style={{"marginLeft": "2px"}}> <a
                            href={UrlUtils.getDp2Url()} target="_blank">魔兔DP</a>
                         </span> | <span style={{"marginLeft": "2px"}}> <a
                            href={UrlUtils.getCrashUrl()} target="_blank">CRASH</a>
                </span>
                        </FormItem>
                    </Form>
                    <div style={{marginTop: "5px"}}>
                    <Table
                        loading={loading}
                        columns={this.getColumns()}
                        dataSource={retList}
                        rowKey={record => record.utdid}
                        pagination={pagination}
                    />
                    </div>
                    <div style={{marginTop: "5px"}}>
                        <span style={{"fontSize": "13px", "color": "rgba(0, 0, 0, 0.3)"}}>备注：此处是随机获取的一部分ACCS ACK 设备（最多100条）。ACCS ACK 后，不代表Orange完成更新，请人工后续分析。</span>
                    </div>
                </Spin>
            </Modal>);

    }

}
