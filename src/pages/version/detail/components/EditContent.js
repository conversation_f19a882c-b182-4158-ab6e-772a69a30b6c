import React from 'react';
import {Button, Popconfirm} from '@alipay/bigfish/antd';
import AceEditor from 'react-ace';

import 'brace/mode/python';
import 'brace/theme/terminal';

export default class EditContent extends React.Component {

  constructor(props) {
    super(props);
    this.state = {content: this.props.content};
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.content != this.props.content) {
      this.setState({
        content: nextProps.content || ' ',
      });
    }

  }

  render() {
    const that = this;
    const content = this.state.content || {};
    const {onContentSumbit} = this.props;

//不能为空
    function onChange(newValue) {
      that.setState({
        content: newValue || ' ',
      });
    }

    function onSubmit() {
      const content = that.state.content;
      onContentSumbit && onContentSumbit(content)
    }

    function renderSubmit() {

      if (!onContentSumbit) {
        return null;
      }
      return (<div style={{"textAlign": "right", "marginRight": "50px"}}>
        <div>
         <span style={{"marginLeft": "5px"}}>
            <Popconfirm
              title="确认要更新吗"
              onConfirm={() => {
                onSubmit();
              }}
            >
              <Button type="primary"> 更新脚本</Button>
            </Popconfirm>
          </span>

        </div>
      </div>);
    }

    return (
      <div>
        <div style={{"margin": "20px"}}>
          <AceEditor
            width="800px"
            height="300px"
            mode="python"
            theme="terminal"
            readOnly={true}
            value={content}
            editorProps={{$blockScrolling: true}}

          />
        </div>
        {renderSubmit()}

      </div>)
  }
}
