import React from 'react';
import {Form, Input, Modal, Spin} from '@alipay/bigfish/antd';


import {checkForm} from '@/utils/utils';

const FormItem = Form.Item;
const {TextArea} = Input;

const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};

export default class BetaModal extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      formData: {},
      visible: false,
      checking: false,
      loading: false,
    };
  };

  show = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        const {formData} = this.state;
        let {onHandleOk} = this.props;
        onHandleOk && onHandleOk(formData);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {

        let {onHandleCancel} = this.props;
        onHandleCancel && onHandleCancel();
        this.setState({
          visible: false,
        });
      },
    );
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);

    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'utdids': {
          if (!value) {
            help = '必选';
          }
          break;
        }
      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  render() {
    let {getFormProps, changeForm} = this;
    const {visible, formData, loading} = this.state;
    const {betaTips} = this.props;



    return (
      <Modal title="Beta灰度" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel} cancelText="跳过Beta">
        <Spin spinning={loading}>
          <Form ref="form" layout="horizontal">

            <FormItem label="utdid列表"   {...getFormProps('utdids')} required help="每行一个utdid, 推送时忽略策略与版本">
              <TextArea
                rows={8}
                value={formData.utdids}
                onChange={(e) => {
                  changeForm({utdids: e.target.value});
                }}
              />
            </FormItem>
            {betaTips?(<div style={{color:"red","marginLeft":"70px"}}>{betaTips}</div>):null}

          </Form>
        </Spin>
      </Modal>
    );
  }
}
