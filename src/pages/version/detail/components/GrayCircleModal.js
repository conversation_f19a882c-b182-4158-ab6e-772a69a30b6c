import React from 'react';
import {
  Icon,
  Tabs,
  Steps,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Spin,
  Radio,
  Button,
  Popconfirm,
  message,
  Tooltip,
  Layout,
} from '@alipay/bigfish/antd';

import { checkForm, getApproximateNumberStr } from '@/utils/utils';
import { isOnlineEnv, isSystemAdmin } from '@/utils/EnvUtils';
import { GRAY_TYPE_MASS_CIRCLE, GRAY_CIRCLE_DIMENSIONS } from '@/constants/record';
import { getLatestGrayRecord, getTigaTaskDetail } from '@/services/version';

const GRAY_RATIO_LIST = [0, 1, 10, 50, 100, 500, 1000, 5000, 10000, 20000, 50000];
export const GRAY_RATIO_UNIT = 100000;
// 最小支持百分比灰度的设备数
export const MIN_DEVICE_CNT = GRAY_RATIO_UNIT;
// 第一批百分比灰度最小观察分钟数
const MIN_GRAY_MINUTES = 30;
const FormItem = Form.Item;
const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};

export default class GrayCircleModal extends React.Component {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = this.getInitState(props);
    this.intervalId = null;
    this.timeoutId = null;
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    if (nextProps.latestGrayRecord.id !== prevState.grayRecord.id) {
      return {
        grayRecord: nextProps.latestGrayRecord,
        loading: false,
      };
    }
    return null;
  }

  getInitState = (props) => {
    return {
      formData: { batchQuantity: 4 },
      grayRecord: props.latestGrayRecord,
      visible: false,
      checking: false,
      loading: false,
      grayRatio: null,
      tigaTaskDetail: null,
      tigaTaskDetailLoading: false,
      activeTab: null,
    };
  };

  componentWillUnmount() {
    this.stopPolling();
    this.clearTimeout();
  }

  stopPolling = () => {
    if (this.intervalId) {
      console.log('停止轮询查询灰度单状态');
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  };

  clearTimeout = () => {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId); // 清除定时器
      this.timeoutId = null; // 重置定时器 ID
    }
  };

  refreshLastGrayRecord = () => {
    const { versionBO } = this.props;
    if (versionBO) {
      getLatestGrayRecord({
        namespaceId: versionBO.namespaceId,
        version: versionBO.version,
      }).then((res) => {
        if (res.status !== 'NEW') {
          this.stopPolling();
          this.setState({ grayRecord: res });
        }
      });
    }
  };

  show = () => {
    const { viewConfig, ratioGrayConfig, versionDeviceCnt } = this.props;
    const isRatioGrayEnabled =
      !!viewConfig.ratioGray &&
      ratioGrayConfig &&
      ratioGrayConfig.support &&
      // 如果查询设备量失败，也允许用户进行百分比灰度发布
      (versionDeviceCnt === undefined || versionDeviceCnt >= MIN_DEVICE_CNT);

    const defaultActiveKey = isRatioGrayEnabled ? 'ratio_gray' : 'default_gray';

    this.setState({
      visible: true,
      activeTab: defaultActiveKey,
    }, () => {
      // 如果默认显示百分比灰度tab，主动获取数据
      if (this.state.activeTab === 'ratio_gray' && !this.state.tigaTaskDetailLoading) {
        this.fetchTigaTaskDetail();
      }
    });
  };

  hide = () => {
    this.setState({
      visible: false,
      tigaTaskDetail: null,
      activeTab: null,
    });
  };

  setGrayRatio = (key) => {
    let { viewConfig, submitGrayRatio, versionBO } = this.props;
    if (viewConfig.ratioGray && submitGrayRatio) {
      this.setState({grayRatio: GRAY_RATIO_LIST[key]});
    }
  };

  getCanSkipFirstStep = () => {
    const { versionDeviceCnt } = this.props;
    const { tigaTaskDetail } = this.state;
    const firstRatioDeviceCnt = versionDeviceCnt
      ? Math.floor((GRAY_RATIO_LIST[1] * versionDeviceCnt) / GRAY_RATIO_UNIT)
      : 0;
    return tigaTaskDetail?.deviceStatistics?.activeCnt > firstRatioDeviceCnt;
  };

  fetchTigaTaskDetail = async () => {
    const { versionBO } = this.props;
    if (!versionBO || !versionBO.tigaMetadata || this.state.tigaTaskDetailLoading) {
      return;
    }

    this.setState({ tigaTaskDetailLoading: true });
    try {
      const result = await getTigaTaskDetail({
        namespaceId: versionBO.namespaceId,
        version: versionBO.version,
      });
      this.setState({
        tigaTaskDetail: result,
        tigaTaskDetailLoading: false,
      });
    } catch (error) {
      console.log('获取 tigaTaskDetail 失败', error);
      this.setState({
        tigaTaskDetail: null,
        tigaTaskDetailLoading: false,
      });
    }
  };

  /**
   * 百分比灰度切流
   */
  submitGrayRatio = () => {
    this.setState({ loading: true });

    let { submitGrayRatio, versionBO } = this.props;
    const { grayRatio } = this.state;
    const canSkipFirstStep = this.getCanSkipFirstStep();
    submitGrayRatio({ grayRatio });
    // 如果是第一个节点灰度，限制最少观测时间，在最小观测时间后放开限制
    // 当小流量灰度总生效设备数大于第一个百分比节点的设备量时，可免去观测时间限制
    if (grayRatio === 1 && !this.timeoutId && versionBO && !canSkipFirstStep && !isSystemAdmin()) {
      this.timeoutId = setTimeout(
        () => {
          getLatestGrayRecord({
            namespaceId: versionBO.namespaceId,
            version: versionBO.version,
          }).then((res) => {
            if (res) {
              this.setState({ grayRecord: res });
            }
          });
        },
        // 需要加上生成探针的定时任务周期时间
        (MIN_GRAY_MINUTES + 1) * 60 * 1000,
      );
    }
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }
        const { formData } = this.state;
        let { onHandleOk } = this.props;
        const params = [];
        params.deviceCnt = formData.deviceCnt;
        params.grayStrategy = formData.grayStrategy || '';

        onHandleOk && onHandleOk(params);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        this.setState({
          visible: false,
        });
      },
    );
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);

    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const { checking, formData } = this.state;

    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'deviceCnt': {
          if (!value) {
            help = '必选';
          }
          break;
        }
        case 'grayStrategy': {
          break;
        }
      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  render() {
    const that = this;

    let { getFormProps, changeForm } = this;
    const { visible, formData, loading } = this.state;
    const {
      versionBO,
      onHandleSkip,
      ratioGrayConfig,
      grayConfig,
      viewConfig,
      onSpecifyGrayModal,
      versionDeviceCnt,
    } = this.props;
    const supportConfigs = (grayConfig && grayConfig.supportConfigs) || {};
    const expression = (grayConfig && grayConfig.expression) || '';
    const massSupportConfig = (supportConfigs && supportConfigs[GRAY_TYPE_MASS_CIRCLE]) || {};
    const maxGrayCnt = massSupportConfig.maxGrayCnt || 10000;
    const supportDimensions = massSupportConfig.supportDimensions || GRAY_CIRCLE_DIMENSIONS;
    const supportDimensionsTips =
      '您可新增圈选条件(同orange策略表达式规则)，建议仅使用如下维度: ' +
      ((supportDimensions && supportDimensions.join(',')) || '');
    const isComplexExpressions = !(versionBO && versionBO.strategy) && expression.length > 60;
    const showSpecifyGrayModel = isSystemAdmin() && isComplexExpressions;
    const _applySkipCancel = function () {
      onHandleSkip && onHandleSkip();
      that.setState({
        visible: false,
      });
    };
    const grayRecord = this.state.grayRecord;
    const deviceCntEnoughForRatioGray = versionDeviceCnt >= MIN_DEVICE_CNT;
    const isRatioGrayEnabled =
      !!viewConfig.ratioGray &&
      ratioGrayConfig &&
      ratioGrayConfig.support &&
      // 如果查询设备量失败，也允许用户进行百分比灰度发布
      (versionDeviceCnt === undefined || deviceCntEnoughForRatioGray);
    let unsupportedMsg =
      viewConfig.ratioGray && ratioGrayConfig && ratioGrayConfig.unsupportedMsg
        ? ratioGrayConfig.unsupportedMsg
        : '';
    if (!deviceCntEnoughForRatioGray) {
      unsupportedMsg = `符合当前策略规则的设备量小于 ${MIN_DEVICE_CNT}, 无需百分比灰度`;
    }
    // 如果不支持 Tiga 小流量灰度或不支持百分比灰度，仍然透出定量灰度入口
    const showDefaultGrayTab = !viewConfig.smallGray || !isRatioGrayEnabled;

    if (
      isRatioGrayEnabled &&
      this.state.visible &&
      grayRecord?.status === 'NEW' &&
      !this.intervalId
    ) {
      // 当最新的百分比是进行中状态，则轮训查询 latestGrayRecord 来更新 grayRecord
      this.intervalId = setInterval(this.refreshLastGrayRecord, 10000);
    }
    // 如果状态不是灰度，则停止轮询，防止灰度过程中，用户直接关单或发布操作导致轮询一直无法结束
    if (versionBO.status != 50 && this.intervalId) {
      this.stopPolling();
      this.clearTimeout();
    }
    const _renderNotSupport = function (tips) {
      return (
        <Modal
          title="定量灰度"
          visible={visible}
          onOk={that.handleOk}
          onCancel={that.handleCancel}
          okButtonProps={{ disabled: true }}
          width={600}
        >
          <Spin spinning={loading}>
            <Form ref="form" layout="horizontal">
              <div style={{ color: 'red', margin: '100px' }}>{tips}</div>
              <div style={{ margin: '10px 200px' }}>
                <Button type="primary" onClick={_applySkipCancel}>
                  申请跳过
                </Button>
              </div>
            </Form>
          </Spin>
        </Modal>
      );
    };
    if (maxGrayCnt && maxGrayCnt <= 0) {
      return _renderNotSupport('非生产环境或其它原因，各灰度能力均被关闭，请直接申请跳过~~');
    }

    const _renderGrayTips = function () {
      return (
        <div style={{ marginLeft: '50px' }}>
          <div style={{ fontWeight: 'bold', lineHeight: '30px' }}>注意事项：</div>
          <div>
            {isComplexExpressions
              ? '* 当前灰度圈选表达式需取所有策略的反，表达式过于复杂存在超时的可能。若长期未结束您可以在【任务查看】里自行取消，或者等待超时取消，数据以ACK为准。'
              : ''}
          </div>
          <div>
            {!isOnlineEnv() ? '* 仅线上环境设备在线，推送方可成功，其它环境请直接跳过~~' : ''}
          </div>
          <div style={{ color: 'red' }}>* 设备圈选时会忽略did_hash维度，务必务必确认~~~~~~~~~</div>
          <div style={{ color: 'red' }}>
            *
            安全生产要求：Orange在正式上线前需要至少1轮灰度观察5分钟。灰度放量节奏依次不超过2000,20000,50000。
          </div>
          <div>
            * 若问题紧急可申请跳过，由主管做审批处理；如果是回滚(走回滚按钮创建的版本),
            可直接申请灰度跳过，不做审核; 有问题自行承担~~
          </div>
        </div>
      );
    };
    const help = `请输入0-${maxGrayCnt}之间的数字，仅推送在线设备，最多推送当前在线设备数！！！`;
    return (
      <Modal
        visible={visible}
        onCancel={this.handleCancel}
        footer={[<Button onClick={this.handleCancel}>关闭</Button>]}
        width={1250}
        closable={false}
      >
        <Spin spinning={loading}>
          <Tabs
            activeKey={this.state.activeTab}
            tabBarExtraContent={unsupportedMsg}
            onChange={(activeKey) => {
              this.setState({ activeTab: activeKey });
              if (activeKey === 'ratio_gray') {
                this.fetchTigaTaskDetail();
              }
            }}
          >
          {showDefaultGrayTab && <Tabs.TabPane
              tab="定量灰度"
              key="default_gray"
              disabled={grayRecord?.grayRatio && isRatioGrayEnabled}
            >
              <Form ref="form" layout="horizontal">
                <FormItem
                  label="推送在线设备数(上限)"
                  {...getFormProps('deviceCnt')}
                  help={help}
                  required
                >
                  <InputNumber
                    value={formData.deviceCnt}
                    min={1}
                    max={maxGrayCnt}
                    onChange={(v) => {
                      changeForm({ deviceCnt: v });
                    }}
                  />
                </FormItem>
                {!expression ? (
                  <FormItem
                    label="追加圈选策略"
                    {...getFormProps('grayStrategy')}
                    help={supportDimensionsTips}
                  >
                    <Input
                      style={{ width: 300 }}
                      onChange={(e) => {
                        changeForm({ grayStrategy: e.target.value });
                      }}
                    />
                  </FormItem>
                ) : null}

                {_renderGrayTips()}
                <FormItem>
                  <div style={{ marginLeft: '60%', marginTop: '10px' }}>
                    <Button type="primary" onClick={that.handleOk} style={{ marginRight: '10px' }}>
                      确认
                    </Button>
                    <span>
                      <Popconfirm
                        title="当前跳过不符合安全生产规范，申请跳过需要审批。审批通过后此审批单将不做灰度管控，确认要跳过吗？"
                        onConfirm={() => {
                          _applySkipCancel();
                        }}
                      >
                        <Button type="default">申请跳过</Button>
                      </Popconfirm>
                    </span>
                    {showSpecifyGrayModel ? (
                      <span>
                        {' '}
                        <Popconfirm
                          title="当前兜底版本圈选表达式过于复杂存在超时的可能，平台提供了直接指定圈选表达式的能力（绕过系统生成），表达式正确性业务方自行保障（满足所有策略的反），若填写错误责任请自担，是否继续？"
                          onConfirm={() => {
                            onSpecifyGrayModal && onSpecifyGrayModal();
                          }}
                        >
                          <Button>自定义圈选表达式</Button>
                        </Popconfirm>
                      </span>
                    ) : null}
                  </div>
                </FormItem>
              </Form>
            </Tabs.TabPane>}
            {viewConfig.ratioGray ? (
              <Tabs.TabPane
                tab="百分比灰度"
                key="ratio_gray"
                disabled={!isRatioGrayEnabled}
              >
                <Steps
                  size="small"
                  current={GRAY_RATIO_LIST.indexOf((grayRecord?.grayRatio) || 0)}
                  onChange={that.setGrayRatio}
                  style={{ padding: '25px 0' }}
                >
                  {GRAY_RATIO_LIST.map((ratio) => {
                    const ratioCompare = ratio - ((grayRecord?.grayRatio) || 0);
                    const currentInProcess = ratioCompare === 0 && grayRecord?.status === 'NEW';
                    const canSkipFirstStep = this.getCanSkipFirstStep();
                    let disable = ratio === 0 || ratioCompare === 0;
                    let disableReason = '';
                    if (grayRecord && ratio < grayRecord.grayRatio) {
                      disable = true;
                      disableReason = '不支持往小调灰度比例';
                    } else if (!grayRecord?.grayRatio && ratio !== 1 && !canSkipFirstStep) {
                      disable = true;
                      disableReason = '请先完成十万分之一的灰度放量';
                    } else if (
                      grayRecord?.grayRatio === 1 &&
                      new Date() - grayRecord.gmtCreate < 1000 * 60 * MIN_GRAY_MINUTES &&
                      !canSkipFirstStep
                    ) {
                      disable = true;
                      disableReason = `十万分之一灰度放量后至少观测 ${MIN_GRAY_MINUTES} 分钟`;
                    }

                    const deviceCntStr = versionDeviceCnt === undefined ?
                      '未知' :
                      getApproximateNumberStr(Math.floor((ratio * versionDeviceCnt) / GRAY_RATIO_UNIT));

                    return (
                      <Steps.Step
                        size="small"
                        key={ratio}
                        icon={
                          currentInProcess ? (
                            <Tooltip
                              placement="top"
                              title="等待探针文件生成中，预计需要 5-6 分钟"
                              defaultVisible={true}
                            >
                              <Icon type="loading" />
                            </Tooltip>
                          ) : (
                            ratioCompare <= 0 ? (
                              <Tooltip placement="top" title={disableReason ? disableReason : null}>
                                <Icon type="check-circle" />
                              </Tooltip>
                            ) : (
                              disable ?
                              <Tooltip placement="top" title={disableReason ? disableReason : null}>
                                <Icon type="play-circle" />
                              </Tooltip> :
                              <Popconfirm title={`灰度放量 ${ratio * 100 / GRAY_RATIO_UNIT}%`} onConfirm={this.submitGrayRatio}>
                                <Icon type="play-circle" />
                              </Popconfirm>
                            )
                          )}
                        disabled={disable}
                        title={
                          ratio === 0 ? (
                            '初始'
                          ) : (!disable ? (
                            <Popconfirm title={`灰度放量 ${ratio * 100 / GRAY_RATIO_UNIT}%`} onConfirm={this.submitGrayRatio}>
                              {`${ratio * 100 / GRAY_RATIO_UNIT}%`}
                            </Popconfirm>) : (
                            <Tooltip placement="top" title={disableReason ? disableReason : null}>
                              {`${ratio * 100 / GRAY_RATIO_UNIT}%`}
                            </Tooltip>
                            )
                          )
                        }
                        description={
                          ratio === 0
                            ? '预估设备量'
                            : deviceCntStr
                        }
                      />
                    );
                  })}
                </Steps>
                {this.getCanSkipFirstStep() && (
                  <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '4px' }}>
                    <div style={{ color: '#52c41a', fontWeight: 'bold', marginBottom: '8px' }}>
                      跳过限制提示
                    </div>
                    <div style={{ color: '#52c41a' }}>
                      您已完成 {this.state.tigaTaskDetail?.deviceStatistics?.activeCnt} 台设备的小流量灰度放量，
                      已为您跳过百分比强制第一批观测 30 分钟卡点限制，您可以继续选择后续任意比例进行放量观测。
                    </div>
                  </div>
                )}
              </Tabs.TabPane>
            ) : null}
          </Tabs>
        </Spin>
      </Modal>
    );
  }
}
