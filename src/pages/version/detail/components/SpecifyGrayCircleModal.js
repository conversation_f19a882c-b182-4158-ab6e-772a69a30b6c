import React from 'react';
import {Form, Input, InputNumber, Modal, Spin, Radio, Button, Popconfirm, message} from '@alipay/bigfish/antd';

import {checkForm} from '@/utils/utils';
import {GRAY_TYPE_MASS_CIRCLE, GRAY_CIRCLE_DIMENSIONS} from "@/constants/record";

const FormItem = Form.Item;
const formLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 17,
    },
};

export default class SpecifyGrayCircleModal extends React.Component {
    static propTypes = {};

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }

    getInitState = () => {
        return {
            formData: {batchQuantity: 4},
            visible: false,
            checking: false,
            loading: false,
        };
    };

    show = () => {
        this.setState({
            visible: true,
        });
    };

    handleOk = () => {
        let that = this;

        this.setState(
            {
                checking: true,
            },
            () => {
                if (!checkForm(this.refs.form)) {
                    return;
                }
                const {formData} = this.state;
                let {onHandleOk} = this.props;
                const params = [];
                params.deviceCnt = formData.deviceCnt;
                params.specifiedGrayStrategy = formData.specifiedGrayStrategy || '';

                onHandleOk && onHandleOk(params);
                this.setState({
                    visible: false,
                });
            },
        );
    };

    handleCancel = () => {
        let that = this;

        this.setState(
            {
                checking: true,
            },
            () => {
                this.setState({
                    visible: false,
                });
            },
        );
    };


    changeForm = (params) => {
        const newData = Object.assign({}, this.state.formData, params);

        this.setState({
            formData: newData,
            checking: false,
        });
    };

    getFormProps = (name) => {
        const {checking, formData} = this.state;

        let help = '';
        const value = formData[name];
        if (checking) {
            switch (name) {
                case 'deviceCnt': {
                    if (!value) {
                        help = '必选';
                    }
                    break;
                }
                case 'specifiedGrayStrategy': {
                    if (!value) {
                        help = '必填';
                    }
                    break;
                }
            }
        }
        return {
            ...formLayout,
            help,
            validateStatus: help ? 'error' : '',
        };
    };

    render() {
        const that = this;

        let {getFormProps, changeForm} = this;
        const {visible, formData, loading} = this.state;
        const {changeBO, grayConfig} = this.props;
        const supportConfigs = grayConfig && grayConfig.supportConfigs || {};
        const massSupportConfig = (supportConfigs && supportConfigs[GRAY_TYPE_MASS_CIRCLE]) || {};
        const maxGrayCnt = massSupportConfig.maxGrayCnt || 10000;
        const supportDimensions = massSupportConfig.supportDimensions || GRAY_CIRCLE_DIMENSIONS;
        const supportDimensionsTips = "您可自定义指定最终圈选条件(同orange策略表达式规则，绕过系统生成)，务必满足正确性(符合所有策略的反)，建议仅使用如下维度: " + (supportDimensions && supportDimensions.join(",") || "");

        const strategyMetaList = changeBO && changeBO.metas ? JSON.parse(changeBO.metas).candidates : [];
        const listStrategies = strategyMetaList && strategyMetaList[0] ? strategyMetaList.map((d) => <div
            key={d.version}>{d.match}</div>) : null;

        const _renderGrayTips = function () {
            return <div style={{"marginLeft": "50px"}}>
                <div style={{fontWeight: "bold"}}>所有策略：</div>
                <div style={{margin: "10px", lineHeight: "20px"}}>{listStrategies}</div>
                <div style={{color: "red"}}>请务必务必确认目标表达式正确，若配置错误责任请自担~~~</div>
            </div>;

        }
        const help = `请输入0-${maxGrayCnt}之间的数字，仅推送在线设备，最多推送当前在线设备数！！！`;
        return (
            <Modal title="定量灰度（自定义圈选表达式）" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}
                   width={800}>
                <Spin spinning={loading}>
                    <Form ref="form" layout="horizontal">
                        <FormItem label="推送在线设备数(上限)" {...getFormProps('deviceCnt')} help={help} required>
                            <InputNumber
                                value={formData.deviceCnt}
                                min={1}
                                max={maxGrayCnt}
                                onChange={(v) => {
                                    changeForm({deviceCnt: v});
                                }}
                            />
                        </FormItem>
                        {
                            <FormItem label="自定义圈选策略" {...getFormProps('specifiedGrayStrategy')}
                                      help={supportDimensionsTips} required>
                                <Input
                                    style={{width: 300}}
                                    onChange={(e) => {
                                        changeForm({specifiedGrayStrategy: e.target.value});
                                    }}
                                />
                            </FormItem>
                        }
                        {_renderGrayTips()}
                    </Form>
                </Spin>
            </Modal>
        );
    }
}
