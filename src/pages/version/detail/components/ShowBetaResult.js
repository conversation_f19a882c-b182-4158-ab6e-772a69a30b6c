import React from 'react';
import {<PERSON>, Modal, <PERSON>, <PERSON><PERSON>, Popconfirm, message} from '@alipay/bigfish/antd';

import {getMassDeviceList} from '@/services/version';

import ShowDeviceList from "./ShowDeviceList";

const FormItem = Form.Item;


const formLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 17,
    },
};

export default class ShowBetaResult extends React.Component {

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }

    getInitState = () => {
        return {
            content: undefined,
            record: undefined,
            visible: false,
            checking: false,
            loading: false,
        };

    };

    show = (content, record) => {
        this.setState({
            visible: true,
            content: content,
            record: record

        });
    };

    handleOk = () => {
        this.state = this.getInitState();
        this.setState({
            visible: false,
            record: undefined,
            content: undefined

        });
    };

    handleCancel = () => {
        this.state = this.getInitState();

        this.setState({
            visible: false,
            content: undefined
        });
    };


    componentWillReceiveProps(nextProps) {
        /*if (JSON.stringify(nextProps.differProps) != JSON.stringify(this.props.differProps)) {
          this.setState({
            differProps: nextProps.differProps,
          });
        }*/
    }


    render() {
        const {visible, loading, content, record} = this.state;
        if (!record) {
            return null;
        }
        const that = this;
        const paramDO = record.paramDO || {};
        const resultDO = record.resultDO || {};
        const deviceIds = paramDO.utdids || [];
        const taskId = resultDO.massTaskId || '';
        const _renderDeviceList = function () {
            //const allReceived = content && content.ackNum && paramDO.utdids && paramDO.utdids[0] && content.ackNum == paramDO.utdids.length;
            //是否展示设备列表，7天内
            const showDeviceList = content && content.ackNum && content.taskCreateTime && (new Date().getTime() - content.taskCreateTime < 7 * 24 * 60 * 60 * 1000);
            if(!showDeviceList){
                return null;
            }
            return <span>  <Button  type="primary"   onClick={function () {
                getMassDeviceList({
                    recordId: record.id,
                    massTaskId: taskId,
                    version: record.version,
                    namespaceId: record.namespaceId,
                }).then((res) => {
                    that.refs.deviceListModal2.show(res, record);
                });
            }}>明细</Button></span>
        };

        return (<div><Modal title="BETA灰度" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}  width={600}
                       destroyOnClose>
            <Spin spinning={loading}>
                {content ? <Form ref="form" layout="horizontal">
                    <FormItem  {...formLayout} label="推送utdid">{deviceIds.join(',') || '-'}</FormItem>
                    <FormItem {...formLayout} label="任务ID">{taskId || '-'} </FormItem>
                    <FormItem  {...formLayout} label="任务状态">{content ? content.status : '-'}</FormItem>
                    <FormItem  {...formLayout}
                               label="收到ACK数"><span>{content ? content.ackNum : '-'} {_renderDeviceList()}</span><span  style={{"fontSize": "13px", "color": "rgba(0, 0, 0, 0.3)"}}>(此为ACCS的ACK，不代表Orange配置更新)</span></FormItem>
                </Form> : <Form ref="form" layout="horizontal">
                    <FormItem  {...formLayout} label="推送utdid">{deviceIds || '-'}</FormItem>
                </Form>}

            </Spin>
        </Modal> <ShowDeviceList ref="deviceListModal2"/></div>);

    }

}
