import React from 'react';
import {Modal, Select, Form, Input, Spin, Radio, Checkbox} from '@alipay/bigfish/antd';

const {TextArea} = Input;


const FormItem = Form.Item;
import indexStyles from '../index.less';
import RollbackVersionList from "./RollbackVersionList";
import {checkForm} from '@/utils/utils';
import {
    SKIP_PROCESS_ARR, SKIP_PROCESS_BETA, SKIP_PROCESS_APPLY, SKIP_PROCESS_GRAY, SKIP_PROCESS_CHECK, SKIP_PROCESS_ALL
} from "@/constants/record";
import {EMERGENT_ARR} from "@/constants/version";

const formLayout = {
    labelCol: {
        span: 5,
    },
    wrapperCol: {
        span: 17,
    },
};
export default class SkipProcessModal extends React.Component {

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }

    getInitState = () => {
        return {
            views: undefined,
            formData: {},
            visible: false,
            checking: false,
            loading: false,
        };

    };

    show = (views) => {
        this.setState({
            visible: true,
            views: views,
            formData: {},
        });
    };

    handleOk = () => {
        let that = this;

        this.setState(
            {
                checking: true,
            },
            () => {
                if (!checkForm(this.refs.form)) {
                    return;
                }

                let {formData} = this.state;
                let {onHandleOk} = this.props;
                let extParams = {
                    problem: formData.problem || '',
                    reason: formData.reason || '',
                };
                const skipStage = formData.skipStage;
                if (skipStage == SKIP_PROCESS_APPLY) {
                    extParams.emergent = formData.emergent || '';
                }
                if (skipStage == SKIP_PROCESS_GRAY || skipStage == SKIP_PROCESS_CHECK) {
                    extParams.noApproval = formData.noApproval || '';
                }
                onHandleOk && onHandleOk(skipStage, extParams);
                this.setState({
                    visible: false,
                    views: undefined,
                    formData: {}
                });
            },
        );
    };

    handleCancel = () => {
        let that = this;

        this.setState(
            {
                checking: true,
            },
            () => {

                let {onHandleCancel} = this.props;
                onHandleCancel && onHandleCancel();
                this.setState({
                    visible: false,
                    views: undefined,
                    formData: {},
                });
            },
        );
    };

    changeForm = (params) => {
        const newData = Object.assign({}, this.state.formData, params);

        this.setState({
            formData: newData,
            checking: false,
        });
    };

    getFormProps = (name) => {
        const {checking, formData} = this.state;
        let help = '';
        const value = formData[name];
        if (checking) {
            switch (name) {
                case 'skipStage': {
                    if (!value) {
                        help = '请确认';
                    }
                    break;
                }
            }
        }
        return {
            ...formLayout,
            help,
            validateStatus: help ? 'error' : '',
        };
    };

    componentWillReceiveProps(nextProps) {
    }

    render() {
        let {visible, loading, views, formData} = this.state;
        const {onHandleOk} = this.props;
        const {getFormProps, changeForm} = this;

        views = views || {};

        return (<Modal title="跳过流程" visible={visible} okText="确认"
                       onOk={this.handleOk} onCancel={this.handleCancel} width={800}
                       destroyOnClose>
            <div>
                <Form ref="form" layout="horizontal">
                    <FormItem label="跳过流程"   {...getFormProps('skipStage')} required>
                        <Select
                            value={formData.skipStage}
                            onChange={(v) => {
                                changeForm({skipStage: v});
                            }}
                        >
                            {SKIP_PROCESS_ARR.map((item) => {
                                return <Select.Option value={item.value} key={item.value}>{item.label}</Select.Option>;
                            })}

                        </Select>
                    </FormItem>
                    {formData.skipStage == SKIP_PROCESS_GRAY || formData.skipStage == SKIP_PROCESS_CHECK ?
                        <FormItem label="是否免批"   {...getFormProps('noApproval')} >
                            <Checkbox value={formData.noApproval} onChange={(e) => {
                                changeForm({noApproval: e.target.checked});
                            }}>免批</Checkbox>
                        </FormItem>
                        : null}
                    {formData.skipStage == SKIP_PROCESS_APPLY ?
                        <FormItem label="是否立即生效"   {...getFormProps('emergent')} required>
                            <Select
                                value={formData.emergent}
                                onChange={(v) => {
                                    changeForm({emergent: v});
                                }}
                            >
                                {EMERGENT_ARR.map((item) => {
                                    return <Select.Option value={item.value}
                                                          key={item.value}>{item.label}</Select.Option>;
                                })}

                            </Select>
                        </FormItem> : null}

                    <FormItem label="申请原因" {...getFormProps('reason')} required>
                        <TextArea rows={2}
                                  onChange={(e) => {
                                      changeForm({reason: e.target.value});
                                  }}
                        />
                    </FormItem>
                    <FormItem label="遇到的问题" {...getFormProps('problem')}
                    >
                        <TextArea rows={2}
                                  onChange={(e) => {
                                      changeForm({problem: e.target.value});
                                  }}
                        />
                    </FormItem>

                </Form>

            </div>

        </Modal>);
    }

}
