import React from 'react';
import {Modal, Select, Form, Input, Spin, Radio, Checkbox} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';
import styles from './List.less';

const FormItem = Form.Item;
import indexStyles from '../index.less';
import RollbackVersionList from "./RollbackVersionList";
import {checkForm} from '@/utils/utils';


const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};
export default class RollbackModal extends React.Component {

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      views: undefined,
      formData: {},
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = (views) => {
    this.setState({
      visible: true,
      views: views,
      formData: {},
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        let {views} = this.state;
        let {onHandleOk} = this.props;
        const fromVersion = views && views.fromVersionBO ? views.fromVersionBO.version : '';
        const toVersion = views && views.toVersionBO ? views.toVersionBO.version : '';
        onHandleOk && onHandleOk(fromVersion, toVersion);
        this.setState({
          visible: false,
          views: undefined,
          formData: {}
        });
      },
    );
  };

  handleCancel = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {

        let {onHandleCancel} = this.props;
        onHandleCancel && onHandleCancel();
        this.setState({
          visible: false,
          views: undefined,
          formData: {},
        });
      },
    );
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);

    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'lossyCheck': {
          if (!value) {
            help = '请确认';
          }
          break;
        }
        case 'starCheck': {
          if (!value) {
            help = '请确认';
          }
          break;
        }
        case 'offlineCheck': {
          if (!value) {
            help = '请确认';
          }
          break;
        }
      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  componentWillReceiveProps(nextProps) {
    /*if (JSON.stringify(nextProps.differProps) != JSON.stringify(this.props.differProps)) {
      this.setState({
        differProps: nextProps.differProps,
      });
    }*/
  }

  render() {
    let {visible, loading, views, formData} = this.state;
    let {getFormProps, changeForm} = this;

    views = views || {};
    const {fromChangeBO, toChangeBO, fromVersionBO, toVersionBO, lossy} = views;

    return (<Modal title="回滚确认" visible={visible} okText="回滚"
                   onOk={this.handleOk} onCancel={this.handleCancel} width={1200}
                   destroyOnClose>
      <div>
        <Form ref="form" layout="horizontal">
          <FormItem label="1"   {...getFormProps('lossyCheck')} required>
            <Checkbox value={formData.lossyCheck} onChange={(e) => {
              changeForm({lossyCheck: e.target.checked});
            }}>当前回滚{lossy ? <span style={{"color": "red"}}> 可能有损 </span> : '无损'}, 请确认</Checkbox>
          </FormItem>
          <FormItem label="2"   {...getFormProps('starCheck')} required>
            <Checkbox value={formData.starCheck} onChange={(e) => {
              changeForm({starCheck: e.target.checked});
            }}>回滚功能不会重新上线非*的常规版本，这类常规发布都不做支持, 请确认</Checkbox>
          </FormItem>
          <FormItem label="3"   {...getFormProps('offlineCheck')} required>
            <Checkbox value={formData.offlineCheck} onChange={(e) => {
              changeForm({offlineCheck: e.target.checked});
            }}>下面两个表格分别给出了回滚前和回滚后的生效列表和各版本的待上下线状态, 请确认</Checkbox>
          </FormItem>
        </Form>
        <hr/>
        <div className={indexStyles.hBox}>
          <div style={{"width": "50%"}}>
            <h4>From:{fromVersionBO && fromVersionBO.version}</h4>
            <div>
              <RollbackVersionList versionBO={fromVersionBO} changeBO={fromChangeBO} {...views} />
            </div>

          </div>
          <div style={{"width": "50%"}}>
            <h4>To:{toVersionBO && toVersionBO.version}</h4>

            <div>
              <RollbackVersionList versionBO={toVersionBO} changeBO={toChangeBO} {...views} />
            </div>
          </div>
        </div>


      </div>

    </Modal>);
  }

}
