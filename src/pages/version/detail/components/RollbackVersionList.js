import React from 'react';
import {But<PERSON>, Table} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';

import styles from './List.less';
import {LinkUtils} from '@/utils/LinkUtils';

import {VersionUtils} from "@/utils/VersionUtils";
import {getResourceDetail} from '@/services/version';

export default class RollbackVersionList extends React.Component {

  constructor(props) {
    super(props);
  }

  componentWillReceiveProps(nextProps) {
    /*if (JSON.stringify(nextProps.resourceBO2) != JSON.stringify(this.props.resourceBO2)) {
      this.setState({
        resourceBO2: nextProps.resourceBO2 || ' ',
      });
    }*/

  }


  getColumns = (versionBO, toOfflineVersions, toOnlineVersions, fromVersion, toVersion) => {


    const self = this;
    const columns = [
      {
        dataIndex: 'id',
        title: 'Version',
        width: '140px',
        render(text, record) {
          //{record.version == versionBO.version ? "(当前)" : ""}
          return (
            <div>
              <span
                className={styles.txtEllipsis}> {record.version} </span><Link
              to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>详情</Link>
            </div>
          );
        },
      }, {
        dataIndex: 'strategy',
        title: 'strategy'
      },
      {
        dataIndex: 'creator',
        title: '创建',
      },
      {
        title: '生效',
        render(text, record) {


          return <span>{record.isAvailable}</span>;
        },
      },
      {
        title: '描述',
        render(text, record) {
          const version = record.version;
          if (record.version == versionBO.version) {
            let desc='CUR';
            if (fromVersion == version) {
              desc='下线(FROM)';
            }
            if (toVersion == version) {
              desc='重发';
            }
            return <span  style={{color: "#3599ff"}}>{desc}</span>;

          }

          if (toOfflineVersions.indexOf(version) > -1) {
            return <span style={{color: "red"}}>下线</span>;
          }
          if (toOnlineVersions.indexOf(version) > -1) {
            return <span style={{color: "green"}}>上线</span>;

          }
          return <span>保持</span>;

        }

      }

    ];
    return columns;
  };

  removeItem = () => {
  };

  render() {
    const self = this;
    const {loading} = this.props;
    let data = [];

    let {changeBO, versionBO, allVersionList, toOfflineVersions, toOnlineVersions, fromVersionBO, toVersionBO} = this.props;
    if (changeBO && changeBO.versions) {
      const versionIds = (changeBO && changeBO.versions) ? changeBO.versions.split(",") : null;
      //filter不能保证顺序
      for (let i = 0; i < versionIds.length; i++) {
        const versionId = versionIds[i];
        for (let j = 0; j < allVersionList.length; j++) {
          const each = allVersionList[j];
          if (versionId == each.version) {
            data.push(each);
            break;
          }
        }
      }
    }
    const fromVersion = fromVersionBO ? fromVersionBO.version : '';
    const toVersion = toVersionBO ? toVersionBO.version : '';


    return (
      <div className={styles.holder}>
        <Table
          loading={loading}
          columns={this.getColumns(versionBO, toOfflineVersions, toOnlineVersions, fromVersion, toVersion)}
          dataSource={data}
          rowKey={record => record.id}
          pagination={false}
        />
      </div>
    );
  }
}
