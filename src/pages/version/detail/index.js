import React from 'react';
import queryString from 'query-string';
import {connect} from 'dva';
import {Link} from 'dva/router';

import {
    Breadcrumb,
    Button,
    Popover,
    Checkbox,
    Col,
    Form,
    Icon,
    message,
    Popconfirm,
    Progress,
    Row,
    Tooltip,
    Steps
} from '@alipay/bigfish/antd';
import QRCode from 'qrcode.react';
import {getFuncName} from '@/utils/utils';
import CodeEditor from '@/components/CodeEditor';


import BetaModal from './components/BetaModal';
import ApplyModal from './components/ApplyModal';
import GrayCircleModal, { MIN_DEVICE_CNT } from './components/GrayCircleModal';
import SpecifyGrayCircleModal from './components/SpecifyGrayCircleModal';

import VersionList from './components/VersionList';
import RecordList from './components/RecordList';
import RollbackModal from "./components/RollbackModal";
import CheckHoldResultModal from "./components/CheckHoldResultModal";
import SkipProcessModal from "./components/SkipProcessModal";


import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {UserUtils} from "@/utils/UserUtils";

import {getRollbackView, getCheckHoldResult, getTigaTaskDetail} from '@/services/version';


import {getEnvName, isAliGroup, isOnlineEnv, isSystemAdmin} from "@/utils/EnvUtils";

import {
    VERSION_STAGE_APPLY,
    VERSION_STAGE_BETA,
    VERSION_STAGE_GRAY,
    VERSION_STAGE_RATIO_GRAY,
    VERSION_STAGE_REVIEW,
    VERSION_STAGE_SKIP,
    VERSION_STAGE_CLOSE,
    VERSION_STAGE_PUBLISH,
    VERSION_STATUS_CREATED,
    VERSION_STATUS_CANCEL,
    VERSION_STATUS_DELETE,
    VERSION_STATUS_SUCCESS
} from "@/constants/version";

import {
    RECORD_STATUS_SUCCESS,
    RECORD_STATUS_FAIL,
    RECORD_TYPE_APPLY,
    RECORD_TYPE_BETA,
    RECORD_TYPE_GRAY,
    RECORD_TYPE_RATIO_GRAY,
    RECORD_TYPE_SKIP,
    RECORD_TYPE_REVIEW,
    RECORD_OPER_REFRESH
} from "@/constants/record";
import styles from './index.less';

import namespace from '@/config/namespace';
import {
    CHECK_STATUS_PASS,
    SKIP_PROCESS_BETA,
    SKIP_PROCESS_GRAY,
    SKIP_PROCESS_APPLY,
    SKIP_PROCESS_CHECK,
    SKIP_PROCESS_ALL
} from "@/constants/record";
import SmallGrayModal from '@/pages/version/detail/components/SmallGrayModal';

const CheckboxGroup = Checkbox.Group;
const {Step} = Steps;

const FormItem = Form.Item;


const NAME_SPACE = namespace.version.detail;

class VersionDetail extends React.Component {
    constructor(props) {
        super(props);
        this.state = { isZoomed: false, tigaTaskFinished: null };
        this.handleQRCodeClick = this.handleQRCodeClick.bind(this);
    }

    componentWillMount() {
        this._initPageData(this.props);
    }

    _initPageData(nextProps) {
        const {dispatch, location, match} = nextProps;
        const versions = (match && match.params && match.params.version) ? match.params.version : '';
        const params = versions.split("-");
        let payload = {params: {'namespaceId': params[0], 'version': params[1]}};

        dispatch({
            type: getFuncName(NAME_SPACE, 'getDetail'),
            payload,
        });
    }

    componentWillReceiveProps(nextProps) {
        if (this.props != null && nextProps != null && this.props.location !== nextProps.location) {
            this._initPageData(nextProps)
        }
        this._updateTigaTaskStatus(nextProps);
    }

    componentDidMount() {
        this._updateTigaTaskStatus(this.props);
    }

    _updateTigaTaskStatus = (props) => {
        const detail = props && props.pageData && props.pageData.detail;
        const versionBO = detail && detail.versionBO || {};
        const viewConfig = detail && detail.viewConfig || {};
        const supportSmallGray = !!viewConfig && !!viewConfig.smallGray;
        const hasCreateTigaTask = !!versionBO && !!versionBO.tigaMetadata;

        if (!supportSmallGray) {
            this.setState({ tigaTaskFinished: null });
            return;
        }

        if (hasCreateTigaTask) {
            getTigaTaskDetail({ namespaceId: versionBO.namespaceId, version: versionBO.version })
                .then((res) => {
                    const finished = ['SKIPPED', 'CANCELLED', 'SUCCESS'].includes(res.status);
                    this.setState({ tigaTaskFinished: !!finished });
                })
                .catch(() => {
                    // 查询失败时，默认按未完成处理，防止越权放量
                    this.setState({ tigaTaskFinished: false });
                });
        } else {
            this.setState({ tigaTaskFinished: false });
        }
    };

    _renderTips(tip) {
        return (<Tooltip title={tip} className="help">
            <span><Icon type="question-circle"/></span>
        </Tooltip>);

    }


    _renderVersion(userMap, versionBO, appBO, wmccView, namespaceBO, viewConfig) {
        const {dispatch} = this.props;
        const that = this;
        const isAdmin = isSystemAdmin();
        const tigaTaskId = versionBO.tigaMetadata && JSON.parse(versionBO.tigaMetadata).taskId;
        const _showSteps = function () {
            const _getStep = function () {
                let step = 1;
                if (wmccView && (wmccView.message == '成功' || wmccView.message == '已生效')) {
                    step = 3;
                } else if (wmccView && wmccView.id) {
                    step = 2;
                } else if (versionBO) {
                    if (versionBO.status == VERSION_STATUS_CREATED) {
                        step = 0;
                    } else if (versionBO.status == VERSION_STATUS_SUCCESS) {
                        step = 2;
                    } else {
                        step = 1;
                    }
                } else {
                    step = 1;
                }
                return step;
            }
            const step = _getStep();
            const _showStepStart = function () {
                return (<div style={{"whiteSpace": "nowrap"}}>
                        <div>{versionBO && versionBO.gmtCreateTime || ''}</div>
                        <div> {versionBO && versionBO.creator ? (
                            <span>创建人： {UserUtils.getUserDisplayName(versionBO.creator, userMap)}</span>) : ''}</div>
                    </div>
                );
            };
            const _showStepVersion = function () {
                return (<div style={{"whiteSpace": "nowrap"}}>
                        <div>{versionBO && versionBO.status == RECORD_STATUS_SUCCESS ? versionBO.gmtPublishTime : ''}</div>
                        <div>
                            <span>跟进人： {UserUtils.getUserDisplayName(versionBO.reviewer, userMap)}</span>
                            <span
                                className={styles.versionStatus}>{ConvertUtils.getVersionStatusName(versionBO.status)}</span>
                        </div>
                    </div>
                );
            };
            const _showStepProbe = function () {
                return (
                    <div>
                        <div>{wmccView && wmccView.message ?
                            <span>{wmccView && wmccView.message} {step == 2 && isAliGroup() ? that._renderTips('探针按任务排队发布，阿里集群生产环境每次任务约6.5分钟') : null}</span> : ''}</div>
                        <div>{wmccView.id ?
                            <a target="_blank" href={UrlUtils.getWmccUrl(wmccView.id)}>wmcc任务</a> : null}
                        </div>
                    </div>
                );
            };
            if (versionBO && (versionBO.status == VERSION_STATUS_DELETE || versionBO.status == VERSION_STATUS_CANCEL)) {
                return (<Steps size="small" current={2}>
                    <Step title="开始"
                          description={_showStepStart()}
                    />
                    <Step title="版本发布" description={_showStepVersion()}/>
                    <Step title="完成"/>
                </Steps>);
            } else {
                return (<Steps size="small" current={step}>
                    <Step title="开始"
                          description={_showStepStart()}
                    />
                    <Step title="版本发布"
                          description={_showStepVersion()}/>
                    <Step title="探针发布"
                          description={_showStepProbe()}/>
                    <Step title="完成"/>
                </Steps>);
            }


        }
        const _renderTips = function (tip) {
            return (<Tooltip title={tip} className="help">
                <span style={{"color": "red"}}><Icon type="exclamation-circle"/></span>
            </Tooltip>);

        }
        return (
            <div style={{"width": "1200px", "padding": "10px"}}>
                <h2>
                    {versionBO.version} <a href={UrlUtils.getResourceUrl(versionBO.resourceId)} target="_blank"><Icon
                    type="download"/></a>
                </h2>
                <div className={styles.form}>
                    {_showSteps()}
                </div>
                <Form className={styles.form}>
                    <Row gutter={24}></Row>

                    <Row>
                        <Col span={8}>
                            <FormItem label="namespace">{namespaceBO.name} <span><Link
                                to={LinkUtils.getNamespaceDetail(versionBO.namespaceId, versionBO.version)}>配置详情</Link></span>
                            </FormItem>
                        </Col>
                        <Col span={8}>
                            <FormItem
                                label="appKey">{appBO.appName}( {versionBO.appKey} - {versionBO.appVersion} )</FormItem>
                        </Col>

                        <Col span={8}>
                            <FormItem label="策略">{versionBO.strategy}  </FormItem>
                        </Col>

                    </Row>
                    <Row>
                        <Col span={6}>
                            <FormItem label="加载级别">{ConvertUtils.getLoadLevelName(versionBO.loadLevel)} </FormItem>
                        </Col>
                        <Col span={6}>
                            <FormItem label="类型">{ConvertUtils.getNsTypeName(versionBO.type)}</FormItem>
                        </Col>
                        <Col span={6}>
                            <FormItem
                                label="生效方式">{ConvertUtils.getEmergentName(versionBO.isEmergent)} {this._renderTips("立即生效约2分钟，普通生效约7-15分钟")}</FormItem>

                        </Col>
                        <Col span={6}>
                            <FormItem
                                label="来源">{ConvertUtils.getVersionSourceName(versionBO.source)}{ConvertUtils.getVersionSourceDesc(versionBO.source, versionBO.sourceData)} </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <FormItem label="负责人">{UserUtils.getUsersDisplayName(namespaceBO.owners, userMap)}
                            </FormItem>
                        </Col>
                        <Col span={12}>
                            <FormItem
                                label="测试负责人"><span
                                className={styles.txtEllipsis}>{UserUtils.getUsersDisplayName(namespaceBO.testers, userMap)}</span> {!namespaceBO.testers ? _renderTips("测试负责人不能为空，发布审核与验证时使用(安全生产要求)。请去配置详情，点击标题旁的编辑ICON设置测试负责人。") : null}
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                         <Col span={tigaTaskId ? 6 : 8}>
                            <FormItem label="发布列表"><Link
                                to={LinkUtils.getVersionListOfNs(versionBO.name, versionBO.appKey)}>
                                <Icon type="flag"/> 全部版本</Link></FormItem>
                         </Col>
                         <Col span={tigaTaskId ? 6 : 8}>
                            {viewConfig.showReport ?
                                <FormItem label="数据报表"><Link
                                    to={LinkUtils.getPublishTrace(versionBO.namespaceId, versionBO.version)}>
                                    <Icon type="line-chart"/>发布跟踪</Link></FormItem>
                                :
                                <FormItem label="数据报表"><a
                                    href={UrlUtils.getHelpUrl("report")} target="_blank">&nbsp;报表接入</a>
                                    <Icon type="alert"/></FormItem>
                            }
                         </Col>
                         <Col span={tigaTaskId ? 6 : 8}>
                            <FormItem label="文档"><a href={UrlUtils.getHelpUrl("version")} target="_blank">发布管理文档</a>
                            </FormItem>
                         </Col>
                         {tigaTaskId && <Col span={6}>
                            <FormItem><a onClick={() => that.refs?.smallGrayModal?.show()} target="_blank">小流量灰度单</a></FormItem>
                        </Col>}
                    </Row>

                </Form>
            </div>);

    }

    handleQRCodeClick(){
        this.setState(prevState => ({
            isZoomed: !prevState.isZoomed // 切换缩放状态
        }));
    };

    _renderContent(resourceBO, versionBO, indexBO, changeBO) {
        const { isZoomed } = this.state;
        if (resourceBO && resourceBO.srcContent) {
            // 只有未发布的版本才能进行快速 beta
            const indexInfo = versionBO.status < 200 ? {
                appVersion: versionBO.appVersion,
                changeVersion: changeBO?.changeVersion,
                highLazy: versionBO.loadLevel === 10 ? 0 : 1,
                loadLevel: { 0: 'DEFAULT', 5: 'HIGH_LAZY', 10: 'HIGH_INIT' }[versionBO.loadLevel],
                md5: resourceBO.md5,
                name: versionBO.name,
                resourceId: resourceBO.resourceId,
                type: { 1: 'STANDARD', 2: 'SWITCH', 3: 'CUSTOM', 4: 'MERGED' }[versionBO.type],
                version: versionBO.version
            } : null;
            const qrUrl = UrlUtils.getQrUrl(versionBO.name, versionBO.appKey, versionBO.appVersion, versionBO.version, indexBO && indexBO.appIndexVersion || '', indexInfo)
            console.log(qrUrl);
            const editMode = ConvertUtils.getNsTypeMode(versionBO.type);
            const onContentChange = function (val) {
            }
            return (<div>
                <h2>变更内容</h2>
                <div style={{ 'display': ' -webkit-box' }}>
                    <div style={{ 'width': '70%', 'padding': '10px' }}>
                        <CodeEditor
                          mode={'properties' || editMode}
                          value={resourceBO.srcContent}
                          onChange={onContentChange}
                          readOnly={true}
                        />
                    </div>
                    <div style={{ width: '20%', textAlign: 'center', height: '15px' }}>
                        <div style={{ marginTop: '20px' }}>
                            <h4>扫码灰度验证</h4>
                            <p>{`点击二维码可以进行${isZoomed ? '缩小' : '放大'}`}</p>
                            <div
                              onClick={this.handleQRCodeClick} // 点击时切换缩放状态
                              style={{ display: 'inline-block', cursor: 'pointer' }} // 添加指针光标
                            >
                                <QRCode
                                  value={qrUrl + ''}
                                  size={isZoomed ? 300 : 200} // 根据状态切换尺寸
                                />
                            </div>
                            <div><a href={UrlUtils.getHelpUrl('beta')} target="_blank"
                                    rel="noopener noreferrer">查看文档</a></div>
                            <div><Link to={LinkUtils.getDebugDetail(versionBO.namespaceId)}>全量验证点此</Link></div>
                        </div>
                    </div>
                </div>
            </div>);
        }
        return null;
    }


    _renderNewVersionList(userMap, namespaceBO, versionBO, isFinished, resourceBO, lastVersionBO, noStrategyList, onlineList, offlineList, loading) {

        if (!versionBO.version) {
            return null;
        }
        let title = '生效版本';

        const retList = [];
        const currentVersionId = versionBO.version;
        const lastVersionId = lastVersionBO && lastVersionBO.version || '';
        const onlineVersionIds = versionBO.versions && versionBO.versions.split(',') || [];
        const offlineMap = versionBO.offlines ? JSON.parse(versionBO.offlines) : {};
        //console.log("offlineMap",offlineMap);

        const fillCommonVersionDesc = function(item) {
            if (item.isAvailable == 'y') {
                item.valid = 'success';
                item.validTitle = '生效中';
            } else {
                item.valid = 'fail';
                item.validTitle = '已失效';
            }
            return item;

        }
        const fillVersionDesc = function (item) {
            const itemVersion = item.version;
            item = fillCommonVersionDesc(item);
            if (itemVersion == currentVersionId) {
                item.code = 'current';
                item.message = '当前版本';
                item.valid = 'info';
                item.validTitle = '生效';
                return item;
            }
            if (lastVersionId && itemVersion == lastVersionId) {
                item.code = 'last';
            }
            if (onlineVersionIds.indexOf(itemVersion) > -1) {
                item.valid = 'success';
                item.validTitle = '生效';
            }
            for (var key in offlineMap) {
                if (key == itemVersion) {
                    item.valid = 'fail';
                    item.validTitle = '失效';
                    var value = offlineMap[key];
                    item.message = ConvertUtils.getVersionOfflineReason(value);
                }
            }
            return item;

        }

        if (versionBO.versions) {
            //当前策略发布


            if (noStrategyList && noStrategyList.length > 1) {

                for (let i = 0; i < noStrategyList.length; i++) {
                    let each = noStrategyList[i];
                    if (each.appVersion != '*') {
                        if (offlineMap && offlineMap[each.version]) {

                        } else {
                            retList.push(fillVersionDesc(each));
                        }

                    }
                }
            }
            if (onlineList && onlineList[0]) {

                for (let i = 0; i < onlineList.length; i++) {
                    let each = onlineList[i];
                    retList.push(fillVersionDesc(each));
                }
            }
            if (offlineList && offlineList[0]) {

                for (let i = 0; i < offlineList.length; i++) {
                    let each = offlineList[i];
                    retList.push(fillVersionDesc(each));
                }
            }


        } else {
            //历史发布 或者常规发布
            title = "关联版本";

            if (!isFinished) {
                versionBO.valid = 'info';
                versionBO.validTitle = '待生效';
            } else {
                versionBO = fillCommonVersionDesc(versionBO);
            }
            versionBO.code = 'current';
            versionBO.message = '当前版本';
            retList.push(versionBO);
            if (offlineList && offlineList[0]) {
                for (let i = 0; i < offlineList.length; i++) {
                    let each = offlineList[i];
                    retList.push(fillVersionDesc(each));
                }
            } else if (lastVersionBO.version) {
                lastVersionBO = fillCommonVersionDesc(lastVersionBO);
                lastVersionBO.code = 'last';
                lastVersionBO.message = '上一版本';
                retList.push(lastVersionBO);

            }
        }

        const query = queryString.parse(this.props.location.search);
        const showDiff = query.showDiff === 'true';

        return (
            <div>
                <h2>{title}</h2>
                <div>
                    {versionBO.versions ? (
                        <div style={{"marginTop": "-20px", "marginBottom": "10px", "textAlign": "center"}}><span
                            style={{"color": "red"}}>端上命中顺序自上而下，请检查待生效与待失效版本是否符合预期！！！</span></div>) : null
                    }
                    <VersionList data={retList} loading={loading} versionBO={versionBO} resourceBO={resourceBO}
                                 userMap={userMap} showDiff={showDiff}
                                 namespaceBO={namespaceBO}/>

                </div>
            </div>
        );

    }


    _renderVersionList(namespaceBO, versionBO, availableList, knockoutMap, resourceBO, loading) {

        if (!availableList[0]) {
            return null;
        }
        let data = [];
        versionBO.code = 'current'
        versionBO.valid = 'info';
        versionBO.validTitle = '待生效';
        versionBO.message = '当前变更';
        data.push(versionBO);
        for (let i = 0; i < availableList.length; i++) {
            let each = availableList[i];
            const version = each.version;
            if (knockoutMap && knockoutMap[version]) {
                each.code = knockoutMap[version].reasonIds;
                each.message = knockoutMap[version].reasons;
                each.valid = 'fail';
                each.validTitle = '待失效';
            } else {
                each.valid = 'success';
                each.validTitle = '生效中';
            }
            data.push(each);

        }
        return (
            <div>
                <h2>生效版本</h2>
                <div>
                    <div style={{"marginTop": "-20px", "marginBottom": "10px", "textAlign": "center"}}><span
                        style={{"color": "red"}}>端上命中顺序自上而下，请检查待生效与待失效版本是否符合预期！！！</span></div>
                    <VersionList data={data} loading={loading} versionBO={versionBO} resourceBO={resourceBO}
                                 namespaceBO={namespaceBO}/>

                </div>
            </div>
        );
    }


    _renderOverwriteList(versionBO, lastVersionBO, overwriteList, resourceBO, loading) {
        if (!overwriteList[0] && !lastVersionBO.version) {
            return null;
        }

        const fillValid = function (item) {
            item.valid = (item.isAvailable == 'y' ? 'success' : 'fail');
            item.validTitle = (item.isAvailable == 'y' ? '生效中' : '失效中');
        }
        let data = [];
        fillValid(versionBO);
        versionBO.message = '当前变更';
        versionBO.code = 'current'
        data.push(versionBO);
        if (lastVersionBO.version) {
            fillValid(lastVersionBO);
            lastVersionBO.message = '上一变更';
            data.push(lastVersionBO);
        }
        for (let i = 0; i < overwriteList.length; i++) {
            let each = overwriteList[i];
            fillValid(each);
            each.message = "用户主动覆盖";
            data.push(each);
        }
        return (
            <div>
                <h2>关联版本</h2>
                <div>
                    <VersionList data={data} loading={loading} versionBO={versionBO} resourceBO={resourceBO}/>

                </div>
            </div>
        );
    }

    _renderMetrix(detail, appConfigSetting) {
        appConfigSetting = appConfigSetting || {};
        let grayConfig = appConfigSetting.grayConfig || {};
        let {configValueDO} = grayConfig || {};
        const {grayEffectUrlTemplate} = configValueDO || {};
        let grayEffectUrl = '';
        if (grayEffectUrlTemplate) {
            const {appBO, versionBO} = detail;
            const context = {...appBO, ...versionBO};
            grayEffectUrl = grayEffectUrlTemplate.replace(/\$\{(.+?)\}/g, (str, group1) => {
                return context[group1];
            });
            //grayEffectUrl='https://wop.alibaba-inc.com/#/share?namespace=orange&version=2120220726155115067&appKey=21646297';
            return (
                <div className={styles.holder} style={{width: '100%', paddingRight: "20px"}}>
                    <iframe src={grayEffectUrl} style={{width: '100%', height: "500px", border: 0}}/>
                </div>);
        } else {
            return null;
        }
    }

    _renderProcess(percent, successPercent, progessName) {
        return (
            <div className={styles.process}>
                <div className={styles.processtip}> 当前进度：{progessName}{isOnlineEnv() ? null : "（非线上环境可跳过流程直接发布）"}
                </div>
                <div className={styles.processdetail}>
                    <Progress percent={percent} successPercent={successPercent}/>
                    <Row gutter={24}></Row>
                    <Row>
                        <Col span={4}>
                            BETA灰度
                        </Col>
                        <Col span={5}>
                            申请发布
                        </Col>
                        <Col span={6}>
                            灰度发布
                        </Col>
                        <Col span={5}>
                            验证
                        </Col>
                        <Col span={2}>
                            发布
                        </Col>
                    </Row>
                </div>
            </div>)
    }

    _renderRecordList(userMap, versionBO, recordList, detail, loading) {
        if (!recordList[0]) {
            return null;
        }
        const that = this;
        const {dispatch} = this.props;
        const version = versionBO.version;
        const namespaceId = versionBO.namespaceId;
        const reparams = {namespaceId: namespaceId, "version": version}
        const viewConfig = detail && detail.viewConfig || {};

        const _onRefreshSubmit = (recordId, type) => {
            const params = {
                type: type,
                version: version,
                namespaceId: namespaceId,
                recordId: recordId,
                oper: RECORD_OPER_REFRESH
            };
            let payload = {
                params: params,
                reparams: reparams
            };

            dispatch({
                type: getFuncName(NAME_SPACE, 'recordOperate'),
                payload,
            });
            message.info('刷新完成')

        };
        const _onShowCheckHold = (checkResultDO, recordId) => {
            that.refs.checkHoldResultModal2.show(checkResultDO, null, null, recordId, null);
        };

        return (
            <div>
                <h2>操作记录</h2>
                <div>
                    <RecordList data={recordList} loading={loading} versionBO={versionBO} userMap={userMap}
                                refreshSubmit={_onRefreshSubmit}
                                oneStepSkip={viewConfig.oneStepSkip}
                                checkHoldShow={_onShowCheckHold}
                    />
                    < CheckHoldResultModal ref="checkHoldResultModal2"/>
                </div>
            </div>
        );
    }

    _renderButton(changeBO, detail, versionBO, isFinished, recordList, appConfigSetting, hasEditPermission, hasTestPermission, versionDeviceCnt) {
        const that = this;
        const {dispatch} = this.props;
        const version = versionBO.version;
        const namespaceId = versionBO.namespaceId;
        const reparams = {namespaceId: namespaceId, "version": version};
        const viewConfig = detail && detail.viewConfig || {};
        const latestGrayRecord = detail && detail.latestGrayRecord || {};
        const grayConfig = detail && detail.grayConfig || {};
        const ratioGrayConfig = detail && detail.ratioGrayConfig || {};

        const isRollback = versionBO && versionBO.sourceDataMeta && versionBO.sourceDataMeta.rollback;

      const supportSmallGray = !!viewConfig?.smallGray;
      const supportRatioGray =
        !!viewConfig.ratioGray && ratioGrayConfig?.support && versionDeviceCnt >= MIN_DEVICE_CNT;

        if (isFinished) {
            const {lastVersionBO} = detail;
            const rollback = viewConfig.rollback || false;
            const rollbackVersion = (rollback && rollback == true && lastVersionBO && lastVersionBO.version ? lastVersionBO.version : null);
            const _onRollback = function () {
                getRollbackView({
                    'namespaceId': namespaceId,
                    fromVersion: version,
                    toVersion: rollbackVersion
                }).then((res) => {

                    const views = res;

                    that.refs.rollbackModal.show(res);
                });
            }
            const _onRollbackSubmit = function () {
                const params = {'namespaceId': namespaceId, fromVersion: version, toVersion: rollbackVersion};
                let payload = {
                    params: params,
                    reparams: reparams
                };

                dispatch({
                    type: getFuncName(NAME_SPACE, 'rollbackVersion'),
                    payload,
                });

            }
            return (
                <Row style={{"margin": "10px", "width": "100%"}}>
                    <Col span={12}>
                        <h4>生效数据 </h4>
                        {this._renderMetrix(detail, appConfigSetting)}
                    </Col>
                    <Col span={12} style={{"textAlign": "right"}}>
                        {hasEditPermission ?
                            <div style={{"margin": "20px 10px 40px 10px"}}>
                                {
                                    rollbackVersion ? (
                                        <Button onClick={() => {
                                            _onRollback();
                                        }}>回滚到上一个版本</Button>) : null
                                }
                            </div> : null
                        }
                    </Col>
                    <RollbackModal ref="rollbackModal" onHandleOk={_onRollbackSubmit}/>
                    {versionBO.tigaMetadata && <SmallGrayModal ref="smallGrayModal" versionBO={versionBO} onHandleSkip={_onGraySkip}/>}
                </Row>
            );
        }

        let hasSkipAll=false;
        let hasBeta = false;
        let hasBetaSkip = false
        let hasApply = false;
        let hasApplySkip = false
        let applyReady = false;
        // 如果 tiga 任务已经完成，则代表有灰度记录
        let hasGray = that.state.tigaTaskFinished === true;
        let hasGraySkip = false;
        let hasReview = false;
        let lastOrSuccessCheckSkipRecord = null;
        let lastApplyRecord = null;//最后申请的record
        let lastReadyApplyRecord = null;//最后一个成功的申请记录
        for (let i = 0; i < recordList.length; i++) {
            const each = recordList[i];
            const type = each.type;
            if (type == RECORD_TYPE_APPLY) {
                hasApply = true;
                if(!lastApplyRecord){
                    lastApplyRecord = each;
                }
                if (each.status == RECORD_STATUS_SUCCESS) {
                    applyReady = true;
                    if (!lastReadyApplyRecord) {
                        lastReadyApplyRecord = each;
                    }
                }

            } else if (type == RECORD_TYPE_GRAY || type == RECORD_TYPE_RATIO_GRAY) {
                hasGray = true;
            } else if (type == RECORD_TYPE_SKIP) {
                const paramDO = each.paramDO || {};
                if (lastOrSuccessCheckSkipRecord == null && paramDO.skipStage == SKIP_PROCESS_CHECK) {
                    lastOrSuccessCheckSkipRecord = each;
                }
                if (each.status == RECORD_STATUS_SUCCESS) {
                    if (paramDO.skipStage == SKIP_PROCESS_BETA) {
                        hasBetaSkip = true;
                    } else if (paramDO.skipStage == SKIP_PROCESS_APPLY) {
                        hasApplySkip = true;
                    } else if (paramDO.skipStage == SKIP_PROCESS_GRAY) {
                        hasGraySkip = true;
                    } else if (paramDO.skipStage == SKIP_PROCESS_CHECK) {
                        lastOrSuccessCheckSkipRecord = each;
                    } else if (paramDO.skipStage == SKIP_PROCESS_ALL) {
                        hasSkipAll = true;
                    }
                } else {
                    // fixme: 不懂为什么要将 hasGraySkip 置为 true，会导致只要提交了跳过就默认灰度跳过成功，先注释掉
                    // hasGraySkip = true;
                }

            } else if (type == RECORD_TYPE_BETA) {
                hasBeta = true;
            } else if (type == RECORD_TYPE_REVIEW) {
                hasReview = true;
            }
        }
        const _getBetaTips = function () {
            if (!isOnlineEnv()) {
                return '仅线上环境设备在线，推送方可成功，其它环境请直接跳过~~';

            }
            return '打开目标设备APP，确保在线后提交';

        }
        const betaTips = _getBetaTips();

        const needWhole = viewConfig.wholeProcess || false;
        const renderBeta = true;
        const renderApply = (!needWhole || hasBeta || hasBetaSkip || hasSkipAll);

        const renderApplyTips = hasApply ? '重新申请' : '申请';
        const renderGray = (!needWhole || applyReady || hasApplySkip || hasSkipAll);
        const renderReview = !hasReview && (!needWhole || hasSkipAll || ((applyReady || hasApplySkip) && (hasGraySkip || hasGray || isRollback)));
        const renderFull = (!needWhole || hasReview);

        let renderClose = true;

        const _onVersionStage = (publishStage, stageParams) => {
            let params = {
                ...stageParams,
                version: version,
                namespaceId: versionBO.namespaceId,
                stageName:publishStage
            };
            let payload = {
                params: params,
                reparams: reparams
            };

            dispatch({
                type: getFuncName(NAME_SPACE, 'doVersionStage'),
                payload,
            });
        };

        const _onBetaSubmit = (formData) => {
            const utdidArr = formData.utdids.split("\n");
            return _onVersionStage(VERSION_STAGE_BETA, {"utdids": utdidArr.join(",")});
        };
        const _checkHold = (formData, nextProcess) => {
            getCheckHoldResult({
                    'namespaceId': namespaceId,
                    version: version,
                    ...formData
                }, (failRet) => {
                    console.log('前置检测异常了，您仍可继续操作', failRet);
                    nextProcess && nextProcess();
                }
            ).then((res) => {
                    const checkStatus = res.checkStatus;
                    if (checkStatus != CHECK_STATUS_PASS) {
                        that.refs.checkHoldResultModal.show(res, viewConfig.oneStepSkip, nextProcess, null, lastOrSuccessCheckSkipRecord);
                    } else {
                        nextProcess && nextProcess();
                    }
                }
            )
        };
        const _onGraySubmit = (formData) => {
            return _onVersionStage(VERSION_STAGE_GRAY, {
                deviceCnt: formData.deviceCnt,
                attachedGrayStrategy: formData.grayStrategy,
                specifiedGrayStrategy: formData.specifiedGrayStrategy
            });

        };
        const _onGrayRatioSubmit = (formData) => {
            return _onVersionStage(VERSION_STAGE_RATIO_GRAY, {
                grayRatio: formData.grayRatio,
            });
        };
        const _onApplySubmit = (formData) => {
            return _onVersionStage(VERSION_STAGE_APPLY, {emergent: formData.isEmergent, reason: formData.reason});
        };
        const _onPublishSubmit = () => {
            return _onVersionStage(VERSION_STAGE_PUBLISH, {});

        };
        const _onReviewSubmit = () => {
            return _onVersionStage(VERSION_STAGE_REVIEW, {});

        };
        const _onCloseSubmit = () => {
            return _onVersionStage(VERSION_STAGE_CLOSE, {});

        };
        const _onSkipSubmit = (process, extParams) => {
            return _onVersionStage(VERSION_STAGE_SKIP, {skipStage: process, ...extParams});
        };
        const _onBetaSkip = () => {
            _onSkipSubmit(SKIP_PROCESS_BETA, {});
        }
        const _onGraySkip = () => {
            _onSkipSubmit(SKIP_PROCESS_GRAY, {});
        }
        const _onCheckHoldSkip = (problem) => {
            _onSkipSubmit(SKIP_PROCESS_CHECK, {problem: problem || ''});
        }
        const _onSpecifyGrayModal = () => {
            //console.log('_onDegradeOldModal11')
            that.refs.specifyGrayCircleModal.show();
            that.refs.grayCircleModal.hide();
        }

        let percent = 0;
        let successPercent = 0;
        let progessName = "";
        let butName = 'apply';
        if (hasReview) {
            successPercent = 90;
            percent = 95;
            progessName = "待发布"
            butName = 'publish';
        } else if (hasGray || hasGraySkip || hasSkipAll) {
            successPercent = 70;
            percent = 80;
            progessName = "待验证"
            butName = 'review';

        } else if (hasApply || hasApplySkip) {
            if (applyReady || hasApplySkip) {
                successPercent = 50;
                percent = 60;
                progessName = "待灰度发布"
                butName = supportSmallGray ? 'smallGray' : 'gray';

            } else {
                if (lastApplyRecord && (lastApplyRecord.status == RECORD_STATUS_FAIL)) {
                    butName = 'apply';
                } else {
                    butName = '';
                }
                successPercent = 30;
                percent = 40;
                progessName = "申请中"
            }

        } else if (hasBeta || hasBetaSkip) {
            successPercent = 10;
            percent = 20;
            progessName = "待申请发布"
            butName = 'apply';
        }
        if (!needWhole) {
            butName = 'publish';
        }

        const showSkip = isSystemAdmin() || viewConfig && viewConfig.oneStepSkip;
        return (
          <div>
            <div style={{ marginTop: '20px', width: '100%' }}>
              <div className={styles.hBox}>
                <div style={{ width: '40%' }}>
                  <h4>生效数据</h4>
                  {this._renderMetrix(detail, appConfigSetting)}
                </div>
                <div style={{ width: '60%' }}>
                  <div>
                    <h4>版本发布流程 {this._renderTips('请按照蓝色按钮提示开始下一步')}</h4>
                    {this._renderProcess(percent, successPercent, progessName)}
                    <div>
                      <div style={{ textAlign: 'right', margin: '20px 10px 50px 10px' }}>
                        {showSkip ? (
                          <Button
                            onClick={() => {
                              this.refs.skipProcessModal.show();
                            }}
                          >
                            跳过流程
                          </Button>
                        ) : null}

                        {renderBeta ? (
                          <Button
                            type={
                              needWhole && !hasBeta && !hasBetaSkip && !hasSkipAll ? 'primary' : ''
                            }
                            disabled={!hasEditPermission}
                            onClick={() => {
                              this.refs.betaModal.show();
                            }}
                          >
                            Beta灰度
                          </Button>
                        ) : null}
                        {renderApply ? (
                          <Button
                            type={butName == 'apply' ? 'primary' : ''}
                            disabled={!hasEditPermission}
                            onClick={() => {
                              this.refs.applyModal.show();
                            }}
                          >
                            {renderApplyTips}发布
                          </Button>
                        ) : null}
                        {renderGray && supportSmallGray ? (
                          <Button
                            type={butName == 'smallGray' ? 'primary' : ''}
                            disabled={!hasEditPermission}
                            onClick={() => {
                              _checkHold({ publishStage: VERSION_STAGE_GRAY }, function () {
                                that.refs.smallGrayModal.show();
                              });
                            }}
                          >
                            小流量灰度
                          </Button>
                        ) : null}
                        {renderGray ? (
                          <Popover
                            content={
                              // 跳过灰度卡口或者小流量灰度完成都展示灰度发布按钮
                              (!hasGraySkip && (supportSmallGray && this.state.tigaTaskFinished === false)) ? '安全生产要求：必须先完成小流量灰度后，才可以继续进行后续灰度&正式放量' : null
                            }
                          >
                            <Button
                              type={butName == 'gray' ? 'primary' : ''}
                              disabled={
                                !hasEditPermission || (!hasGraySkip && (supportSmallGray && this.state.tigaTaskFinished === false))
                              }
                              onClick={() => {
                                _checkHold({ publishStage: VERSION_STAGE_GRAY }, function () {
                                  that.refs.grayCircleModal.show();
                                });
                              }}
                            >灰度发布</Button>
                          </Popover>
                        ) : null}
                        {renderReview ? (
                          <Popconfirm
                            title="确定验证通过吗？"
                            onConfirm={() => {
                              _onReviewSubmit();
                            }}
                          >
                            <Button
                              type={butName == 'review' ? 'primary' : ''}
                              disabled={!hasEditPermission && !hasTestPermission}
                            >
                              验证通过
                            </Button>
                          </Popconfirm>
                        ) : null}
                        {renderFull ? (
                          <Popconfirm
                            title="确定要全量发布吗？"
                            onConfirm={() => {
                              _checkHold({ publishStage: VERSION_STAGE_PUBLISH }, function () {
                                _onPublishSubmit && _onPublishSubmit();
                              });
                            }}
                          >
                            <Button type="primary" disabled={!hasEditPermission}>
                              {getEnvName()}发布
                            </Button>
                          </Popconfirm>
                        ) : null}
                        {renderClose ? (
                          <Popconfirm
                            title="确定要取消发布吗？"
                            onConfirm={() => {
                              _onCloseSubmit();
                            }}
                          >
                            <Button disabled={!hasEditPermission}>取消发布</Button>
                          </Popconfirm>
                        ) : null}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <BetaModal
              ref="betaModal"
              onHandleOk={_onBetaSubmit}
              onHandleCancel={_onBetaSkip}
              betaTips={betaTips}
            />
            <GrayCircleModal
              ref="grayCircleModal"
              versionBO={versionBO}
              onHandleOk={_onGraySubmit}
              submitGrayRatio={_onGrayRatioSubmit}
              onHandleSkip={_onGraySkip}
              ratioGrayConfig={ratioGrayConfig}
              grayConfig={grayConfig}
              viewConfig={viewConfig}
              versionDeviceCnt={versionDeviceCnt}
              latestGrayRecord={latestGrayRecord}
              onSpecifyGrayModal={_onSpecifyGrayModal}
            />
            <SmallGrayModal
              ref="smallGrayModal"
              versionBO={versionBO}
              onHandleSkip={_onGraySkip}
              onTaskCreated={() => this._initPageData(this.props)}
            />
            <SpecifyGrayCircleModal
              ref="specifyGrayCircleModal"
              versionBO={versionBO}
              changeBO={changeBO}
              onHandleOk={_onGraySubmit}
              grayConfig={grayConfig}
            />
            <CheckHoldResultModal ref="checkHoldResultModal" onHandleSkip={_onCheckHoldSkip} />
            <ApplyModal ref="applyModal" onHandleOk={_onApplySubmit} />
            <SkipProcessModal ref="skipProcessModal" onHandleOk={_onSkipSubmit} />
          </div>
        );

    }

    render() {
        let that = this;
        const {dispatch, pageData} = this.props;
        const {loading, detail, appConfigSetting, versionDeviceCnt} = pageData;
        //console.log(pageData, detail);
        const versionBO = detail && detail.versionBO || {};
        const appBO = detail && detail.appBO || {};
        const wmccView = detail && detail.wmccView || {};

        const namespaceBO = detail && detail.namespaceBO || {};
        const resourceBO = detail && detail.resourceBO || {};
        const indexBO = detail && detail.indexBO || {};
        const changeBO = detail && detail.changeBO || {};

        const recordList = detail && detail.recordList || [];

        const lastVersionBO = detail && detail.lastVersionBO || {};

        const noStrategyList = detail && detail.noStrategyList || [];
        const onlineList = detail && detail.onlineList || [];
        const offlineList = detail && detail.offlineList || [];
        const viewConfig = detail && detail.viewConfig || {};
        const userMap = detail && detail.userMap || {};


        const VERSION_STATUS_FINISHED = [VERSION_STATUS_SUCCESS, VERSION_STATUS_CANCEL, VERSION_STATUS_DELETE];

        const hasEditPermission = UserUtils.hasNsEditPermission(namespaceBO.owners);
        const hasTestPermission = UserUtils.hasNsTestPermission(namespaceBO.testers);

        const isFinished = (versionBO.status && VERSION_STATUS_FINISHED.indexOf(versionBO.status) >= 0 ? true : false);

        return (
            <div>
                <Breadcrumb className="bread-nav">
                    <Breadcrumb.Item>配置管理</Breadcrumb.Item>
                    <Breadcrumb.Item>
                        <Link to={LinkUtils.getVersionList()}>发布列表</Link>
                    </Breadcrumb.Item>
                    <Breadcrumb.Item>发布单详情</Breadcrumb.Item>
                </Breadcrumb>
                <div style={{"maxWidth": "1200px"}}>
                    {that._renderVersion(userMap, versionBO, appBO, wmccView, namespaceBO, viewConfig)}
                    {that._renderContent(resourceBO, versionBO, indexBO, changeBO)}
                    {that._renderNewVersionList(userMap, namespaceBO, versionBO, isFinished, resourceBO, lastVersionBO, noStrategyList, onlineList, offlineList, loading)}
                    {that._renderRecordList(userMap, versionBO, recordList, detail, loading)}
                    {that._renderButton(changeBO, detail, versionBO, isFinished, recordList, appConfigSetting, hasEditPermission, hasTestPermission, versionDeviceCnt)}
                </div>


            </div>
        );
    }
}

function

mapStateToProps(global) {
    return {
        pageData: global[NAME_SPACE],
    };
}

export default connect(mapStateToProps)

(
    VersionDetail
)
;
