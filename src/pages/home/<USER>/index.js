import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';
import {Breadcrumb, Icon} from '@alipay/bigfish/antd';
import Logo from '@/assets/logo.jpg';
import {LinkUtils} from '@/utils/LinkUtils';
import styles from './index.less';


import namespace from '@/config/namespace';

const NAME_SPACE = namespace.home.index;

class Index extends React.Component {

  render() {
    const {dispatch} = this.props;
    const {tips} = window._DATA_;

    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>Orange</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getHome()}>首页</Link>
          </Breadcrumb.Item>
        </Breadcrumb>
        <div className={styles.holder}>

          <div style={{"marginLeft": "200px","paddingTop":"30px"}}>
            <h1>Orange 无线端配置推送平台</h1>
          </div>
          {tips?<div className={styles.notice}><div className={styles["notice-content"]}><Icon type="exclamation-circle" /> 公告：{tips}</div></div>:null}

          <div>

            <div style={{"marginLeft": "20%","marginTop":"40px"}}>
              <a className={styles["btn-info"]}
                 target="_blank"
                 href="https://lark.alipay.com/wireless-orange/wiki">文档中心</a>

            </div>
            <div style={{"textAlign": "center"}}>
              <img src={Logo} width="600px"/>
            </div>
          </div>


        </div>
      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(Index);
