import React from 'react';
import {connect} from 'dva';
import {<PERSON>} from 'dva/router';
import {Breadcrumb, Button} from '@alipay/bigfish/antd';
import {getFuncName} from '@/utils/utils';
import {LinkUtils} from '@/utils/LinkUtils';

import List from './components/List';
import {EditIndex} from '../edit/EditIndex';


import namespace from '@/config/namespace';
import EditModal from "../edit/components/EditModal";

const NAME_SPACE = namespace.namespace.me;

class MyNamespaceList extends EditIndex {
  namespace = namespace.namespace.me;

  componentWillMount() {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }

  render() {
    const {dispatch, data} = this.props;
    const {list, appList, userMap, moduleMap, knockoutNamespaceVersion, namespace, loading} = data;
    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>配置管理</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getMyNamespaceList()}>我的配置</Link>
          </Breadcrumb.Item>
        </Breadcrumb>
        <div>
          <div style={{"margin": "10px 20px", "height": "20px"}}>
            <div className="pull-right">

              <EditModal namespace={{}} appList={appList}
                         onHandleOnline={this.availableNamespace}
                         onHandleOk={this.createOrUpdateHandler}>
                <Button id="applyButton" type="primary">新建配置</Button>
              </EditModal>
            </div>
          </div>
          <div>
            <List data={list}
                  offlineShow={this.offlineShow}
                  knockoutNamespaceVersion={knockoutNamespaceVersion}
                  namespaceShow={this.namespaceShow}
                  namespace={namespace}
                  userMap={userMap}
                  moduleMap={moduleMap}
                  loading={loading}
                  onHandleOnline={this.availableNamespace}
                  onOfflineOk={this.offlineNamespace}
                  editHandler={this.createOrUpdateHandler}/>

          </div>
        </div>
      </div>
    );
  }
}

function mapStateToProps(state) {
  return {
    data: state[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(MyNamespaceList);
