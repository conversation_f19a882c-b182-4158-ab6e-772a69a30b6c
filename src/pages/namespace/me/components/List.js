import React from 'react';
import {Table, Icon} from '@alipay/bigfish/antd';
import {ConvertUtils} from "@/utils/ConvertUtils";

import styles from './List.less';
import EditModal from "../../edit/components/EditModal";
import Offline from "../../edit/components/Offline";
import Edit from "../../edit/components/Edit";
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {UserUtils} from "@/utils/UserUtils";

import {Link} from 'dva/router';


export default class List extends Edit {
  constructor(props) {
    super(props);
  }

  getColumns = (appList, userMap, moduleMap) => {
    const self = this;
    return [
      {
        dataIndex: 'name',
        title: '名称',
        render(text, record) {
          return (
            <div>
              <span>{text}</span>
              <span><Link to={LinkUtils.getNamespaceDetail(record.namespaceId, null)}>详情</Link></span>
            </div>
          );
        },
      }, {
        title: '类型',
        dataIndex: 'type',
        render(text, record) {
          return (
            <div>
              <p>{ConvertUtils.getNsTypeName(text)}</p>
            </div>
          );
        },
      }, {
        title: '加载级别',
        dataIndex: 'loadLevel',
        render(text, record) {
          return (
            <div>
              <p>{ConvertUtils.getLoadLevelName(record.loadLevel)}</p>
            </div>
          );
        },
      },
      {
          title: '摩天轮模块',
          dataIndex: 'modules',
          render(text) {
              return <div>{ConvertUtils.getModulesName(text, moduleMap)}</div>;
          },
      },
      {
        title: '负责人',
        dataIndex: 'owners',
        width: 250,
        render(text) {
          return <div>{UserUtils.getUsersDisplayName(text, userMap)}</div>;
        },
      },
      {
        title: '测试',
        dataIndex: 'testers',
        width: 80,
        render(text) {
          return <div>{UserUtils.getUsersDisplayName(text, userMap)}</div>;
        },
      },
      {
        title: '描述',
        dataIndex: 'detail',
        width: 200
      },
      {
        title: '操作',
        render(text, record) {
          let {knockoutNamespaceVersion, namespace} = self.props;
          knockoutNamespaceVersion = knockoutNamespaceVersion || [];
          namespace = namespace || [];
          return (
            <div>
              <EditModal
                onShow={() => self.getNamespaceHandler(record.namespaceId)}
                namespace={namespace} appList={appList}
                onHandleOk={self.editNamespaceHandler}>
                <a>编辑</a>
              </EditModal>
              <Offline record={record}
                       onShow={() => self.offlineShowHandler(record.namespaceId)}
                       knockoutNamespaceVersion={knockoutNamespaceVersion}
                       onHandleOk={self.offlineNamespaceHandler}>
                <a>下线</a>
              </Offline>
              <p><Link to={LinkUtils.getVersionListOfNs(record.name, record.appKeyOrGroup)}>发布列表</Link></p>
            </div>
          );
        },
      }
    ];
  };
  showNamespaceList = (loading, appList, userMap, moduleMap, appNamespaceList) => {
    const {app, namespaceList} = appNamespaceList;
    return (
      <div style={{"margin": "20px 20px"}} key={app.appKey}>
        <div key={app.appKey} className={`${styles.holder} ${styles.tableBreak}`}>
          <div className="hBox">
            <div>
              <h3>{app.appName}<span color="gray">(AppKey:{app.appKey})</span></h3>
            </div>
            <span style={{"marginLeft": "10px"}}>
                        <Link to={LinkUtils.getNamespaceList(app.appKey)}>更多</Link>
                    </span>
            <span style={{"marginLeft": "10px"}}> <Link to={LinkUtils.getAppDashboard(app.appKey)}><Icon
              type="line-chart"/></Link>
            </span>
          </div>
          <Table
            loading={loading}
            columns={this.getColumns(appList, userMap, moduleMap)}
            dataSource={namespaceList}
            rowKey={record => record.namespaceId}
            pagination={false}
          />
        </div>
      </div>
    );
  };

  render() {
    const {data, appList, userMap, loading, moduleMap} = this.props;
    let list = data || [];
    return (
      <div>
        {list.map(appNamespaceList => this.showNamespaceList(loading, appList, userMap, moduleMap, appNamespaceList))}
      </div>
    );
  }
}
