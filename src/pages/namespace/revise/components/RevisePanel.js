import React from 'react';
import {Button, Checkbox, Col, Form, Icon, Input, message, Row, Select, Radio, Popconfirm} from '@alipay/bigfish/antd';

import {checkForm} from '@/utils/utils';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {LOADLEVEL_ARR, NS_TYPE_ARR} from '@/constants/namespace';


const FormItem = Form.Item;
const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};

import styles from './List.less';

export default class RevisePanel extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      reviseKey: 'loadLevel',
      toValue: undefined
    };
  };
  changeReviseKey = (reviseKey) => {
    this.setState({reviseKey: reviseKey, toValue: undefined});
  }
  changeToValue = (toValue) => {
    this.setState({toValue: toValue});
  }

  render() {
    const that = this;
    let {namespaceBO, onSubmit} = this.props;

    let {reviseKey, toValue} = this.state;
    const fromValue = namespaceBO[reviseKey];
    toValue = toValue == undefined ? '' : toValue;
    const _renderToValue = function (reviseKey, fromValue, toValue) {
      if (reviseKey == 'loadLevel') {
        return (<Select placeholder="请选择加载级别"
                        value={toValue}
                        onChange={(v) => {
                          that.changeToValue(v);
                        }}>
          {LOADLEVEL_ARR.map((item) => {
            return item.value == fromValue ? null : <Select.Option key={item.value}
                                                                   value={item.value}>{item.label}</Select.Option>;
          })}
        </Select>);
      } else if (reviseKey == 'type') {
        return (<Select placeholder="请选择配置类型"
                        value={toValue}
                        onChange={(v) => {
                          that.changeToValue(v);
                        }}>
          {NS_TYPE_ARR.map((item) => {
            return item.value == fromValue ? null : (<Select.Option key={item.value}
                                                                    value={item.value}>{item.label}</Select.Option>);
          })}
        </Select>);
      } else {
        return <Input
          value={toValue}
          onChange={(e) => {
            that.changeToValue(e.target.value)
          }}
        />
      }

    }
    const _renderFromValue = function (reviseKey, fromValue) {

      if (reviseKey == 'loadLevel') {
        return (ConvertUtils.getLoadLevelName(fromValue));
      } else if (reviseKey == 'type') {
        return (ConvertUtils.getNsTypeName(fromValue));

      } else {
        return fromValue;
      }

    }
    return (

      <div className={styles.holder}>
        <Form layout="horizontal">
          <FormItem label="订正字段" {...formLayout} >
            <Radio.Group value={reviseKey}
                         onChange={(v) => {
                           that.changeReviseKey(v.target.value);
                         }}>
              <Radio value="name">配置名称</Radio>
              <Radio value="loadLevel">加载级别</Radio>
              <Radio value="type">配置类型<span style={{"color":"red"}}>(若异常责任自担)</span></Radio>

            </Radio.Group>
          </FormItem>
          <FormItem label="订正前" {...formLayout} >
            <Input disabled={true}
                   value={_renderFromValue(reviseKey, fromValue)}
                   placeholder="请输入目标配置名称 "
            />

          </FormItem>
          <FormItem label="订正为" {...formLayout} >
            {_renderToValue(reviseKey, fromValue, toValue)}
          </FormItem>
          <FormItem {...formLayout}>
            <div style={{"marginLeft": "50%"}}>
              <Popconfirm
                title="申请前请务必阅读右侧的注意事项，阅读后确认仍要申请订正吗？"
                onConfirm={() => {

                  const params = {
                    namespaceId: namespaceBO.namespaceId,
                    reviseKey: reviseKey,
                    fromValue: fromValue,
                    toValue: toValue
                  }
                  onSubmit && onSubmit(params);

                }}
              >
                <Button type="primary">申请订正</Button>
              </Popconfirm>
            </div>
          </FormItem>

        </Form>
      </div>
    );
  }
}
