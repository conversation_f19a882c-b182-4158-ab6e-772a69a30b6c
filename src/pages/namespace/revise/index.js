import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';

import {<PERSON><PERSON><PERSON><PERSON>b, Button, Checkbox, Col, Form, Icon, Input, message, Popconfirm, Row} from '@alipay/bigfish/antd';
import {getFuncName} from '@/utils/utils';


import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";

import {isSystemAdmin, isTestEnv} from "@/utils/EnvUtils";
import styles from './index.less';

import namespace from '@/config/namespace';


const FormItem = Form.Item;
import RevisePanel from "./components/RevisePanel";


const NAME_SPACE = namespace.namespace.revise;

class ReviseDetail extends React.Component {
  constructor(props) {
    super(props);
  }

  _initPageData(namespaceId) {
    const {dispatch, location, match} = this.props;

    let payload = {params: {'namespaceId': namespaceId}};

    dispatch({
      type: getFuncName(NAME_SPACE, 'getDetail'),
      payload,
    });
  }

  componentWillMount() {
    const {dispatch, location, match} = this.props;
    const namespaceId = (this.props.match && this.props.match.params && this.props.match.params.namespaceId) ? this.props.match.params.namespaceId : '';
    this._initPageData(namespaceId);

  }

  componentWillReceiveProps(nextProps) {
    if (this.props != null && nextProps != null && this.props.location != nextProps.location) {
      const namespaceId1 = (this.props.match && this.props.match.params && this.props.match.params.namespaceId) ? this.props.match.params.namespaceId : '';

      const namespaceId2 = (nextProps.match && nextProps.match.params && nextProps.match.params.namespaceId) ? nextProps.match.params.namespaceId : '';
      if (namespaceId1 && namespaceId2 && namespaceId1 != namespaceId2) {
        this._initPageData(namespaceId2);

      }
    }
  }


  _renderDesc(namespaceBO, detail) {
      const {dispatch} = this.props;
      const auditState = detail && detail.auditState || {};
      const versionState = detail && detail.versionState || {};

      const appBO = detail && detail.appBO || {};

      const _cancelRevise = function () {

          dispatch({
              type: getFuncName(NAME_SPACE, 'cancelAuditingNamespace'),
              payload: {
                  params: {
                      namespaceId: namespaceBO.namespaceId,
                      procInstId: namespaceBO.auditingFlag
                  },
                  reparams: {
                      namespaceId: namespaceBO.namespaceId
                  }
              },
          });

      }

      return (
          <div>
              <h2>
                  配置信息
              </h2>
              <Form className={styles.form}>
                  <Row gutter={24}></Row>
                  <Row>
                      <Row>
                          <Col span={24}>
                              <FormItem label="配置名称">{namespaceBO.name} {namespaceBO.namespaceId ? (
                                  <Link
                                      to={LinkUtils.getNamespaceDetail(namespaceBO.namespaceId, null)}>去详情</Link>) : null}
                              </FormItem>
                          </Col>

                      </Row>
                      <Row>
                          <Col span={24}>
                              <FormItem label="客户端">{appBO.appName}( {namespaceBO.appKeyOrGroup})</FormItem>
                          </Col>
                      </Row>
                      <Row>
                          <Col span={16}>
                              <FormItem label="负责人">{namespaceBO.owners} </FormItem>
                          </Col>

                      </Row>
                      <Row>
                          <Col span={8}>
                              <FormItem
                                  label="加载级别">{ConvertUtils.getLoadLevelName(namespaceBO.loadLevel)} </FormItem>
                          </Col>
                          <Col span={8}>
                              <FormItem label="类型">{ConvertUtils.getNsTypeName(namespaceBO.type)}</FormItem>
                          </Col>
                          <Col span={8}>
                              {versionState.resourceSize ?
                                  <FormItem label="配置大小">{versionState.resourceSize}B </FormItem> : null}
                          </Col>
                      </Row>
                      <Row>
                          <Col span={12}>
                              <FormItem label="是否有版本">{versionState.hasVersion ? '有' : '无'} </FormItem>
                          </Col>
                          <Col span={12}>
                              <FormItem label="活动中的版本">{versionState.runningVersion ? (<Link
                                  to={LinkUtils.getVersionDetail(namespaceBO.namespaceId, versionState.runningVersion)}>版本详情</Link>) : (
                                  <span>无</span>)}
                              </FormItem>
                          </Col>
                      </Row>
                      <Row>
                          <Col span={8}>
                              <FormItem label="订正状态">{namespaceBO.auditingFlag == 'free' ? <span>无流程</span> :
                                  <span>流程中</span>}</FormItem>
                          </Col>
                          <Col span={16}>
                              <FormItem label="相关操作">{namespaceBO.auditingFlag == 'free' ? null :
                                  <div><Button><a
                                      href={UrlUtils.getBpmsUrl(namespaceBO.auditingFlag)}
                                      target="_blank">查看Bpms流程</a></Button> <Popconfirm
                                      title="建议先人工去bpms后台撤销流程后再到此处取消，确定要取消订正吗？"
                                      onConfirm={() => {
                                          _cancelRevise()
                                      }}
                                  >
                                      <Button>取消订正</Button>
                                  </Popconfirm>
                                  </div>}</FormItem>
                          </Col>
                      </Row>
                  </Row>
              </Form>
          </div>);

  }

  _renderTips() {

    return (
      <div>
        <h2>
          注意事项：
        </h2>
        <Form className={styles.form}>
          <FormItem>一、全局说明</FormItem>
          <FormItem>* <span className={styles.red}>订正配置类型，可能会引发客户端异常</span>，请务必确认~~~</FormItem>
          <FormItem>* 订正前，如果有活动的版本请尽快关闭，且下次发布后才真实生效~~~</FormItem>
          <FormItem>* 订正后，若曾经创建过版本，请都尽快重新发布一份上线~~~</FormItem>
          <FormItem>* 其它请根据<a href="https://yuque.antfin-inc.com/wireless-orange/wiki/vgtkbb#iXjsv"
                              target="_blank"> 帮助文档</a>自行评估 </FormItem>
          <FormItem>二、支持的订正</FormItem>
          <FormItem label="订正配置名称">仅做大小写名称的订正，非大小写原因，请重新创建</FormItem>
          <FormItem label="订正加载级别">申请提高加载级别，请确认是需要的(若配置大于20K,不允许申请HighInit) </FormItem>
          <FormItem label="订正配置类型">理论上不允许变更，若一定要变更，请做好评估，若端上异常，责任自担 </FormItem>
          <FormItem>三、订正配置类型注意事项</FormItem>
          <FormItem>* <span className={styles.red}>端上代码未上线</span>, 如果上线了, 端上异常责任自担 </FormItem>
          <FormItem>* <span className={styles.red}>生成环境配置未曾发布过</span>，否则端上可能曾经更新过，若端上异常 责任自担 </FormItem>


        </Form>
      </div>);
  }

  _renderForm(detail, namespaceBO) {
    const {dispatch, match} = this.props;
    const versionState = detail && detail.versionState || {};


    const canUpdate = namespaceBO.auditingFlag && namespaceBO.auditingFlag == 'free';
    if (!canUpdate) {
      return null;
    } else {
      if (versionState.runningVersion) {
        return (<div> <div style={{"margin":"20px 200px 20px 20px"}}>
          请前去 <Link
          to={LinkUtils.getVersionDetail(namespaceBO.namespaceId, versionState.runningVersion)}>版本详情</Link> 关闭活动中的版本
        </div></div>)
      }

      const _sumbitRevise = function (params) {
        dispatch({
          type: getFuncName(NAME_SPACE, 'auditingNamespace'),
          payload: {
            params: params,
            reparams:{
              namespaceId:namespaceBO.namespaceId
            }
          },
        });

      }
      return (<div>
        <h2>
          申请订正
        </h2>
        <div>
          <RevisePanel namespaceBO={namespaceBO} onSubmit={_sumbitRevise}/>
        </div>
      </div>)
    }

  }

  render() {
    let that = this;
    const {dispatch, pageData} = this.props;
    const {detail, loading} = pageData;

    const namespaceBO = detail && detail.namespaceBO || {};


    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>配置管理</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getNamespaceDetail(namespaceBO.namespaceId, null)}>配置详情</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>配置订正</Breadcrumb.Item>
        </Breadcrumb>
        <div style={{"maxWidth": "1200px"}}>
          <div style={{"display": " -webkit-box"}}>
            <div style={{"width": "50%"}}>
              {that._renderDesc(namespaceBO, detail)}
              {that._renderForm(detail, namespaceBO)}
            </div>
            <div style={{"width": "50%", "margin": "20px"}}>
              {that._renderTips(detail)}
            </div>
          </div>

        </div>


      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(ReviseDetail);
