import React from 'react';
import {getFuncName} from '@/utils/utils';

export class EditIndex extends React.Component {
  namespace;

  createOrUpdateHandler = (data) => {
    this.props.dispatch({
      type: getFuncName(this.namespace, 'createOrUpdateNamespace'),
      payload: {data}
    });
  };
  namespaceShow = (namespaceId) => {
    this.props.dispatch({
      type: getFuncName(this.namespace, 'getNamespace'),
      payload: {namespaceId: namespaceId}
    })
  };
  offlineShow = (namespaceId) => {
    this.props.dispatch({
      type: getFuncName(this.namespace, 'queryKnockoutForOfflineNamespace'),
      payload: {namespaceId: namespaceId}
    });
  };
  offlineNamespace = (data) => {
    this.props.dispatch({
      type: getFuncName(this.namespace, 'offlineNamespace'),
      payload: {...data}
    });
  };
  availableNamespace = (data) => {
    this.props.dispatch({
      type: getFuncName(this.namespace, 'availableNamespace'),
      payload: {...data}
    });
  };
}
