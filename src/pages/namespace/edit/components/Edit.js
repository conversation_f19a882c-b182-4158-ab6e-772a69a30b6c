import React from 'react';

export default class Edit extends React.Component {
  editNamespaceHandler = (values) => {
    this.props.editHandler(values);
  };
  offlineNamespaceHandler = (data) => {
    this.props.onOfflineOk(data);
  };
  offlineShowHandler = (data) => {
    this.props.offlineShow(data);
  };
  getNamespaceHandler = (data) => {
    this.props.namespaceShow(data);
  };
  availableNamespace = (data) => {
    this.props.onHandleOnline(data);
  }
}
