import React from 'react';
import {Checkbox, Form, Modal, Spin, Table} from '@alipay/bigfish/antd';
import {isSystemAdmin} from "@/utils/EnvUtils";

const FormItem = Form.Item;

class OfflineConfirmedModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = this.getInitState();
    this.formRef = React.createRef();
  }

  getInitState = () => {
    return {
      visible: false,
      checking: false,
      loading: false,
    };
  };
  handleOk = (e) => {
    e.preventDefault();
    let {onHandleOk} = this.props;
    this.props.form.validateFields((err, formData) => {
      if (!err) {
        onHandleOk && onHandleOk({namespaceId: this.props.record.namespaceId});
        this._hide();
      }
    });
  };
  handleCancel = () => {
    this._hide();
  };
  _hide = () => {
    this.setState({visible: false,});
  };

  showModelHandler = e => {
    if (e) e.stopPropagation();
    this.props.onShow();
    this.setState({
      visible: true,
    });
  };

  render() {
    const formItemLayout = {
      labelCol: {
        span: 4,
      },
      wrapperCol: {
        span: 30,
      },
    };
    const columns = [{
      dataIndex: 'version',
      title: 'version',
    }, {
      dataIndex: 'appVersion',
      title: 'appVersion',
    }, {
      dataIndex: 'strategy',
      title: 'strategy',
    }, {
      dataIndex: 'gmtPublish',
      title: '发布时间',
    }, {
      dataIndex: 'creator',
      title: '发布人',
    }];
    const {getFieldDecorator} = this.props.form;
    const {loading, children} = this.props;
    let knockoutNamespaceVersion = this.props.knockoutNamespaceVersion || [];
    const isAdmin = isSystemAdmin();
    const {visible, formData} = this.state;
    return (
      <div>
        <span onClick={this.showModelHandler}>{children}</span>
        <Modal title="下线namespace" visible={visible} width={800} onOk={this.handleOk}
               onCancel={this.handleCancel}>
          <Spin spinning={!knockoutNamespaceVersion}>
            <p>删除配置项下线所有生效的发布单</p>
            <Table
              loading={!knockoutNamespaceVersion}
              columns={columns}
              dataSource={knockoutNamespaceVersion}
              rowKey={record => record.version}
              pagination={false}
            />
            <Form layout="horizontal" ref={this.formRef}>
              <div style={{color: "red"}}>
                <p>1. 下线namespace无法回滚,下线后客户端无法获取到这个配置的内容。</p>
                <p>2. 我能保证客户端在无法获取到该配置内容时功能依旧正常，不会crash。</p>
                <FormItem {...formItemLayout} label="确认配置下线协议">
                  {getFieldDecorator('agreement', {
                    rules: [{
                      required: true,
                      validator: (rule, value, cb) => (value === true ? cb() : cb(true)),
                      message: '请确认配置下线协议'
                    }],
                  })(<Checkbox/>)}
                </FormItem>
              </div>
            </Form>
          </Spin>
        </Modal>
      </div>
    );
  }
}

export default Form.create()(OfflineConfirmedModal);
