import React from 'react';
import {LOADLEVEL_ARR, NS_TYPE_ARR, NS_SUB_TYPE_ARR, NS_TYPE_CUSTOM} from '@/constants/namespace';
import {Alert, AutoComplete, Form, Input, message, Modal, Select, Spin} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';
import {queryUnAvailableNamespace} from '@/services/namespace';
import {queryModuleByModuleIds} from '@/services/search';
import {getObj,} from '@/utils/utils';
import {LinkUtils} from '@/utils/LinkUtils';
import UserSelector from '@/components/UserSelector';
import ModuleSelector from '@/components/ModuleSelector';
import {UserUtils} from "@/utils/UserUtils";
import { queryUserByEmpIds, searchModule } from '@/services/search';
import { getValidStr, getArr } from '@/utils/utils';

// 淘宝iPhone客户端-ios(AppKey:21380790)、淘宝主客户端Android-android(AppKey:21646297)、手淘HarmonyOS客户端-harmony(AppKey:34639004)
export const FORCE_UPDATE_MODULE_APPKY = ['21380790', '21646297', '34639004'];

const {Option} = Select;
const FormItem = Form.Item;

class EditNamespaceModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = this.getInitState();
    this.formRef = React.createRef();
  }

  getInitState = () => {
    return {
      visible: false,
      checking: false,
      loading: false,
      isOffline: false,
      appKey: null,
      moduleTesterList: null,
    };
  };
  _hide = () => {
    this.setState({visible: false});
  };

  handleOk = (e) => {
    e.preventDefault();
    let {namespace, onHandleOnline} = this.props;
    let appBO = namespace.appBO || {};
    this.props.form.validateFields((err, formData) => {
        if (!err) {
          if (formData.namespaceId) {
            this.save(formData);
          } else {
            let isOffline = this.state.isOffline;
            queryUnAvailableNamespace({appKey: formData.appKey, name: formData.name})
              .then((res) => {
                let offlineNamespace = getObj(res);
                if (isOffline) {
                  onHandleOnline({namespaceId: offlineNamespace.namespaceId});
                  this._hide();
                } else {
                  if (offlineNamespace && offlineNamespace.namespaceId) {
                    this.setState({isOffline: true})
                  } else {
                    this.save(formData);
                  }
                }
              });
          }
        }
      }
    );
  };

  save(formData) {
    let {onHandleOk, namespace} = this.props;
    formData.owners = formData.owners && formData.owners[0] ? formData.owners.join(',') : null;
    formData.testers = formData.testers && formData.testers[0] ? formData.testers.join(',') : null;
    // 过滤掉 moduleId 为 0 的兜底默认模块
    formData.modules = formData.modules?.filter(i => i != '0')?.join(',') || '';
    if (namespace && namespace.namespaceBO) {
      let appBO = namespace.appBO || {};
      formData = {
        ...formData,
        appName: appBO.appName,
        needAuditing: formData.loadLevel > namespace.namespaceBO.loadLevel && namespace.auditState.canAudit
      };
    }
    onHandleOk && onHandleOk(formData);
    if (formData.needAuditing) {
      message.info("提交审核成功");
    }
    this._hide();
  }

  handleCancel = () => {
    this._hide();
  };

  showModelHandler = e => {
    if (e) e.stopPropagation();
    this.props.form.resetFields();
    let {onShow} = this.props;
    onShow && onShow();
    this.setState({
      visible: true,
      loadLevel: NaN,
      isOffline: false
    });
  };
  changeLoadLevel = value => {
    this.setState({loadLevel: value});
  };

  changeType = value => {
    this.setState({type: value});
  };

  selectAppKey = value => {
    this.setState({appKey: value});
    this.props.form.resetFields(['modules']);
  };

  changeAppKey = value => {
    // 清空的时候，禁用模块搜索并清空模块值
    if (!value) {
      this.setState({appKey: null});
      this.props.form.resetFields(['modules']);
    }
  };

  // 切换模块时计算是否要填充测试人员
  changeModules = value => {
    const testers = this.props.form.getFieldValue('testers');
    // 没有选择模块或者手动设置了测试人员则不自动填充
    if (!value?.length || testers?.length) {
      return this.setState({ moduleTesterList: null});
    }

    queryModuleByModuleIds({ moduleIds: value.join(',') }).then((res) => {
      const moduleTesters = new Set();
      Object.values(res || {}).forEach((item) => {
        if (item.testers?.length) {
          moduleTesters.add(...item.testers);
        }
      });
      if (moduleTesters.size > 0) {
        const empIds = [...moduleTesters];
        queryUserByEmpIds({ empIds: empIds.join(',') }).then((res) => {
          if (Object.keys(res || {}).length) {
            const moduleTesterList = empIds.map((item) => {
              return { empId: item, displayName: res[item]?.displayName };
            });
            this.setState({ moduleTesterList });
            this.props.form.setFieldsValue({ testers: empIds });
          }
        });
      }
    });
  };

  render() {
    const formItemLayout = {
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 17,
      },
    };
    const {isAdmin, emailPrefix, userName, userId} = window._DATA_;
    const defaultUserOption = [{
      empId: userId,
      displayName: userName
    }]
    const {getFieldDecorator} = this.props.form;
    let namespace = this.props.namespace || {};

    let {namespaceBO} = namespace || {};
    let auditState = namespace.auditState || {};
    let record = namespaceBO || {};
    const {children} = this.props;
    const {visible, formData, loading, type} = this.state;
    const appKey = this.state.appKey || record.appKeyOrGroup;
    let namespaceType = record.type || type;
    let {appList} = this.props;
    let loadLevels = record.namespaceId ? LOADLEVEL_ARR : [LOADLEVEL_ARR[0]];
    appList = appList || [];
    let dataSource = appList.map(item => {
      return {value: item.appKey, text: item.appKey + '-' + item.appName}
    });
    const ownerList = namespace.ownerList || [];
    const testerList = this.state.moduleTesterList || namespace.testerList || [];
    const moduleIdList = record.modules ? record.modules.split(',').map(i => parseInt(i)) : [];
    // 请求摩天轮失败时，namespace.moduleList 为空，此时展示值也用 moduleId 兜底
    const moduleList = !namespace.moduleList?.length ? moduleIdList.map(i => {
      return {moduleId: i, name: getValidStr(i)};
    }) : namespace.moduleList;

    // 目前只针对于部分应用强制填模块：1. 新增必填；2. 更新时，如果原来有，也必填
    const requireModule = FORCE_UPDATE_MODULE_APPKY.includes(appKey) &&
      (!record.namespaceId || record.modules)

    const _renderTips = function () {
      if (auditState.isAuditing && auditState.bpmUrl) {
        return (
          <span style={{marginLeft: "60px"}}>数据订正中，点击查看 <a href={auditState.bpmUrl + namespaceBO.auditingFlag}
                                                           target="_blank">变更流程</a></span>);
      } else {
        return (<span style={{marginLeft: "60px"}}>其它字段，若有订正需求，<Link
          to={LinkUtils.getNamespaceRevise(namespaceBO && namespaceBO.namespaceId)}>申请订正</Link></span>)

      }
    }

    return (
      <div>
        <span onClick={this.showModelHandler}>{children}</span>
        <Modal title={record.namespaceId ? "修改配置信息" : "新增配置信息"}
               okText={this.state.isOffline ? '重新上线' : null}
               visible={visible}
               onOk={this.handleOk}
               onCancel={this.handleCancel}
               width={600}
        >
          <Spin spinning={loading}>
            <Form ref={this.formRef} layout="horizontal">
              {getFieldDecorator('namespaceId', {
                initialValue: record.namespaceId,
              })(<Input type="hidden"/>)}
              <FormItem {...formItemLayout} label="归属应用">
                {getFieldDecorator('appKey', {
                  initialValue: record.appKeyOrGroup,
                  rules: [
                    {required: true, message: '请选择应用'},
                  ],
                })(
                  <AutoComplete
                    style={{width: 200}}
                    dataSource={dataSource}
                    onSelect={this.selectAppKey}
                    onChange={this.changeAppKey}
                    placeholder="选择应用"
                    disabled={!!record.namespaceId}
                    filterOption={(inputValue, option) => option.props.children.indexOf(inputValue) !== -1}
                  />
                )}
              </FormItem>
              <FormItem {...formItemLayout} label="名称" help='一经创建原则上不允许修改，请注意大小写'>
                {getFieldDecorator('name', {
                  initialValue: record.name,
                  rules: [
                    {required: true, message: '请输入namespace名称，不能重复'},
                  ],
                })(<Input
                  disabled={!!record.namespaceId}
                  placeholder='请输入namespace名称，不能重复'/>)}
              </FormItem>
              <FormItem {...formItemLayout} label="类型">
                {getFieldDecorator('type', {
                  initialValue: record.type || NS_TYPE_ARR[0].value,
                  rules: [
                    {required: true, message: '请选择加载类型'},
                  ],
                })(
                  <Select placeholder="请选择加载类型" disabled={!!record.namespaceId}
                          onChange={this.changeType}>
                    {NS_TYPE_ARR.filter(item => !item.internal)
                      .map(item => {
                        return <Option key={item.value}
                                       value={item.value}>{item.label}-{item.desc}</Option>;
                      })}
                  </Select>
                )}
              </FormItem>
              {
                namespaceType === NS_TYPE_CUSTOM ?
                  <FormItem {...formItemLayout} label="子类型" help='子类型仅用于配置校验'>
                    {getFieldDecorator('subType', {
                      initialValue: record.subType || '',
                      rules: [
                        {required: true, message: '请选择子类型'},
                      ],
                    })(
                      <Select placeholder="请选择子类型">
                        <Option key='empty'  value=''>未设置</Option>
                        {NS_SUB_TYPE_ARR[namespaceType].filter(item => !item.internal)
                          .map(item => {
                            return <Option key={item.value}
                                           value={item.value}>{item.desc}</Option>;
                          })}
                      </Select>
                    )}
                  </FormItem>
                  : null
              }
              <FormItem {...formItemLayout} label="加载级别" help='如果需要修改加载级别，请走数据订正（入口在此对话框下方）'>
                {getFieldDecorator('loadLevel', {
                  initialValue: record.loadLevel || loadLevels[0].value,
                  rules: [
                    {required: true, message: '请选择加载级别'},
                  ],
                })(
                  <Select placeholder="请选择加载级别" disabled={!!record.namespaceId}
                          onChange={this.changeLoadLevel}>
                    {loadLevels.map((item) => {
                      return <Option key={item.value}
                                     value={item.value}>{item.label}-{item.desc}</Option>;
                    })}
                  </Select>
                )}
              </FormItem>

              <FormItem {...formItemLayout} label="摩天轮模块" help={ requireModule ? "至少关联一个摩天轮模块": null }>
              {getFieldDecorator('modules', {
                initialValue: moduleIdList,
                rules: [{
                  required: requireModule,
                  validator: (rule, value, cb) => ( !requireModule || (value || []).length >= 1 ? cb() : cb(true)),
                  message: requireModule ? '至少关联1个模块' : null
                }]
              })(
                <ModuleSelector
                  appKey={appKey}
                  multiple
                  disabled={!appKey}
                  defaultOptions={moduleList}
                  getResult={(res) => {
                    // 报错后兜逻辑
                    if (res === undefined) {
                      return requireModule ? [{moduleId: 0, name: '默认模块'}] : [];
                    }
                    return getArr(res.content);
                  }}
                  getData={(keyword) => {
                    if (keyword && appKey) {
                      return searchModule({ keyword: keyword, appKey, pageNum: 1, pageSize: 8 });
                    }
                    return Promise.resolve({ content: [] });
                  }}
                  onChange={this.changeModules}
                />
              )}
              </FormItem>

              <FormItem {...formItemLayout} label="管理员" help="至少填写2名（创建者自动加入管理员列表）">
                {getFieldDecorator('owners', {
                  initialValue: UserUtils.getUserInitValue(ownerList, true),
                  rules: [{
                    required: true,
                    validator: (rule, value, cb) => ((value || []).length >= 2 ? cb() : cb(true)),
                    message: '至少填写2名'
                  }]
                })(
                  <UserSelector
                    multiple
                    defaultOptions={UserUtils.getUserSelectOption(ownerList, true)}
                    onChange={(v) => {
                      console.log("UserSelector change", v);
                    }}
                  />
                )}
              </FormItem>
              <FormItem {...formItemLayout} label="测试" help="至少填写1名(离职同学自动删除可直接确认)">
                {getFieldDecorator('testers', {
                  initialValue: UserUtils.getUserInitValue(testerList, false),
                  rules: [{
                    required: true,
                    validator: (rule, value, cb) => ((value || []).length >= 1 ? cb() : cb(true)),
                    message: '至少填写1名'
                  }]
                })(
                  <UserSelector
                    multiple
                    defaultOptions={UserUtils.getUserSelectOption(testerList, false)}
                    onChange={(v) => {
                      console.log("UserSelector change", v);
                    }}
                  />
                )}
              </FormItem>


              <FormItem {...formItemLayout} label="描述">
                {getFieldDecorator('detail', {
                  initialValue: record.detail,
                  rules: [{required: true}]
                })(
                  <Input.TextArea rows={5}/>
                )}
              </FormItem>
              <div style={{lineHeight: "20px"}}>
                {_renderTips()}
              </div>
              {
                this.state.isOffline ?
                  <FormItem {...formItemLayout} label='&nbsp;' colon={false}>
                    <Alert
                      message="已存在同名并且已经下线的配置项，请确认是否重新上线，如果上线内容需要保持向前兼容"
                      type="warning"
                      closable
                    />
                  </FormItem> : null
              }

            </Form>
          </Spin>
        </Modal>
      </div>
    );
  }
}

export default Form.create()(EditNamespaceModal);
