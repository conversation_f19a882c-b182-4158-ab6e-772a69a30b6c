import React from 'react';
import {Popconfirm} from '@alipay/bigfish/antd';

export default class Online extends React.Component {
  constructor(props) {
    super(props);
    this.formRef = React.createRef();
  }

  confirm = (e) => {
    let {namespace, onHandleOnline} = this.props;
    onHandleOnline({namespaceId: namespace.namespaceId});
  };

  cancel = (e) => {
  };

  render() {
    return (
      <Popconfirm
        title="内容需要向前兼容，请确定是否要重新上线?"
        onConfirm={this.confirm}
        onCancel={this.cancel}
        okText="是"
        cancelText="否"
      >
        <a href="#">上线</a>
      </Popconfirm>
    );
  }
}