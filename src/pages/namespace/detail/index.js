import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';
import {isOnlineEnv} from '@/utils/EnvUtils';

import {
  Breadcrumb,
  Button,
  Checkbox,
  Col,
  Form,
  Icon,
  Input,
  message,
  Popconfirm,
  Row,
  Tooltip
} from '@alipay/bigfish/antd';
import {getFuncName} from '@/utils/utils';
import CodeEditor from '@/components/CodeEditor';

import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {UserUtils} from "@/utils/UserUtils";

import VersionList from './components/VersionList';
import KockoutList from "./components/KockoutList";
import CodeDiffDetail from "./components/CodeDiffDetail";
import NamespacePanel from "./components/NamespacePanel";
import SelectRollbackVersion from "./components/SelectRollbackVersion";
import {queryKnockoutVersions, getRollbackView, getRollbackList} from '@/services/version';
import {NamespaceUtils} from "@/utils/NamespaceUtils";
import {getEnvName, isSystemAdmin} from "@/utils/EnvUtils";


import styles from './index.less';

import namespace from '@/config/namespace';
import {EditIndex} from "../edit/EditIndex";
import RollbackModal from "../../version/detail/components/RollbackModal";
import SelectMtlModule from './components/SelectMtlModule';
import { FORCE_UPDATE_MODULE_APPKY } from '../edit/components/EditModal';

const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;


//import DropVersionList from "./components/DropVersionList";

const NAME_SPACE = namespace.namespace.detail;

class NamespaceDetail extends EditIndex {
  namespace = NAME_SPACE;


  constructor(props) {
    super(props);
    this.state = {
      versionBO: {},
      resourceBO: {},
      overwrites: [],
      content: null,
      strategy: '',
      showExtra: true,
      hasAlert: false,
      initEditor: false

    };
  }

  componentWillMount() {
    this._initPageData(this.props);
  }


  reloadPage() {
    console.log("xxx")
  }

  _getVersionParams(match) {
    const versions = (match && match.params && match.params.version) ? match.params.version : '';
    const params = versions.split("-");

    let data = {'namespaceId': params[0], 'version': ''}
    if (params.length == 2) {
      data.version = params[1];
    } else {
      data.version = null;
    }
    return data;
  }

  _initPageData(nextProps) {
    const {dispatch, match} = nextProps;
    let payload = {params: this._getVersionParams(match)};
    dispatch({
      type: getFuncName(NAME_SPACE, 'getDetail'),
      payload,
    });
  }

  componentWillReceiveProps(nextProps) {
    if (this.props != null && nextProps != null) {
      if (this.props.location != nextProps.location) {
        const curData = this.props.pageData && this.props.pageData.detail;
        const params = this._getVersionParams(nextProps.match);
        if (!curData || !curData.namespaceBO || params.namespaceId != curData.namespaceBO.namespaceId) {
          this._initPageData(nextProps);
        }
      }

    }
    if (nextProps != null) {
      const pageData = nextProps.pageData || {};
      //if (pageData.versionBO && (!this.state.versionBO.version || JSON.stringify(this.state.versionBO) != JSON.stringify(pageData.versionBO))) {
      //this.forceUpdate();
      //}
      const detail = pageData.detail || {};
      this.setState(this._getState(detail.namespaceBO || {}, pageData.versionBO || {}, pageData.resourceBO || {}, detail.changeBO || {}));

    }

  }

  _getState(namespaceBO, versionBO, resourceBO, changeBO) {
    const defaultValue = ConvertUtils.getNsDefaultValue(namespaceBO.type);
    const initEditor = (resourceBO.srcContent || namespaceBO && namespaceBO.name && !(changeBO && changeBO.changeVersion)) ? true : false;
    const ret = {
      versionBO: versionBO,
      resourceBO: resourceBO,
      content: resourceBO.srcContent ? resourceBO.srcContent : defaultValue,
      strategy: versionBO.strategy || '',
      initEditor: initEditor
    };
    //console.log('_getState',ret)
    return ret;
  }

  _renderTips(tip) {
    return (<Tooltip title={tip} className="help">
      <span><Icon type="question-circle"/></span>
    </Tooltip>);

  }

  _renderContent(namespaceBO, hasNoContent, hasEditPermission) {
    const that = this;
    const editMode = ConvertUtils.getNsTypeMode(namespaceBO.type);
    const onContentChange = function (val) {
      //console.log('onContentChange', val)
      that.setState({"content": val})
    }
    const formLayout = {
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 17,
      },
    };
    let {content, strategy, versionBO, initEditor} = this.state;
    const isJsonFormat = NamespaceUtils.getContentFormat(namespaceBO) == 'json'
    const jsonResult = this._isJSON(content)

    return (<div>
      <div>
        <h4>发布内容: &nbsp;&nbsp;&nbsp;&nbsp; {strategy === '' && versionBO.appVersion === '*' ? (
            <span className={styles.defaultSpanner}>正在编辑「兜底」版本，请谨慎操作</span>) :
          (strategy === '' ? (<span>正在编辑「{versionBO.appVersion}」常规版本</span>) : (
            <span>正在编辑「{strategy}」策略版本</span>))}</h4>
        <br/>
        <FormItem {...formLayout} label="请输入策略表达式">
          <Input
            value={strategy}
            placeholder="请输入策略表达式(为空则发布到全部)，点击问号查看文档 ->> "
            onChange={(e) => {
              that.setState({"strategy": e.target.value || ''})
            }}
            suffix={<a href={UrlUtils.getHelpUrl("strategy")} target="_blank">?</a>}
          />

        </FormItem>
      </div>
      {initEditor ?
        <CodeEditor
          mode={editMode || 'properties'}
          value={content}
          onChange={onContentChange}
          readOnly={false}
        /> : null}
      {isJsonFormat ? (jsonResult.valid ?
          <div style={{"paddingLeft": "32px", "color": "green"}}>
            <span>{jsonResult.tip}</span>
          </div> :
          <div style={{"paddingLeft": "32px", "color": "red"}}>
            <span>{jsonResult.tip}</span>
          </div>
      ) : null}
    </div>);
  }

  _isJSON(str) {
    if (typeof str == 'string') {
      try {
        var obj = JSON.parse(str);
        if (typeof obj == 'object' && obj) {
          return {valid: true, tip: "格式正确"};
        } else {
          return {valid: false, tip: "格式错误"};
        }
      } catch (e) {
        return {valid: false, tip: '格式错误，' + e};
      }
    } else {
      return {valid: false, tip: "格式错误"};
    }
  }

  _renderList(withoutStrategyList, withStrategyList, loading, contentFormat) {
    const that = this;
    const {dispatch, match} = this.props;
    const _changeSelectVersion = function (selected) {
      let payload = {params: {'resourceId': selected.resourceId}, versionBO: selected};
      dispatch({
        type: getFuncName(NAME_SPACE, 'getVersionResource'),
        payload,
      });
      location.hash = LinkUtils.getNamespaceDetail(selected.namespaceId, selected.version);
    }
    if (!withoutStrategyList[0] && !withStrategyList[0]) {
      return null;
    }
    let {versionBO, content, overwrites, showExtra} = this.state;
    const _onVersionRecover = function (versionId) {
      let result = overwrites.filter((item) => {

        return item !== versionId;
      });
      that.setState({
        overwrites: result
      });
    }


    const _onVersionDel = function (versionId) {
      if (!overwrites.indexOf(versionId) > -1) {
        overwrites.push(versionId);

        that.setState({
          overwrites: overwrites
        });

      }
    }


    let props = {
      changeSelectVersion: _changeSelectVersion,
      loading: loading,
      versionBO: versionBO,
      content: content,
      overwrites: overwrites,
      onVersionDel: _onVersionDel,
      onVersionRecover: _onVersionRecover,
      dataformat: contentFormat
    }

    return (<div style={{"maxWidth": "500px"}}>
      <div style={{"margin": "5px"}}><span style={{"color": "red"}}>~端上命中顺序自上而下。查看按钮将数据复制到内容框, 删除按钮仅临时记录<a
        href={UrlUtils.getHelpUrl('delete')} target="_blank">(文档)</a>~</span></div>
      {!withoutStrategyList[0] ? null : <div>
        <div>
          <div><h4>常规版本（待下线）<span style={{"fontSize": "12px", "color": "rgba(0, 0, 0, 0.3)"}}> <a
            onClick={() => {
              that.setState({showExtra: !showExtra});
            }}
          >
            {showExtra ? (
              <span>
              收起 <Icon type="up"/>
            </span>
            ) : (
              <span>
              更多 <Icon type="down"/>
            </span>
            )}
          </a></span></h4>

          </div>
          <div style={showExtra ? {display: 'block'} : {display: 'none'}}>

            <VersionList data={withoutStrategyList} isStrategy={false} {...props} />
          </div>
        </div>
      </div>}
      <h4>策略版本与兜底版本</h4>
      <VersionList data={withStrategyList} isStrategy={true}  {...props}/>
    </div>);

  }

  _renderBtn(namespaceBO, hasEditPermission, withStrategyList, waitList, changeBO, viewConfig) {
    const {hasAlert} = this.state;
    const waitPublished = waitList && waitList[0];
    const _renderWaitInfo = function () {
      if (waitPublished) {
        return (<div style={{"margin": "20px", "width": "100%"}}>
          <div style={{"textAlign": "center"}}>
            <span style={{color: "red"}}>警告：仍有未发布完的版本，请完成发布后再操作</span> <Link
            to={LinkUtils.getVersionDetail(waitPublished.namespaceId, waitPublished.version)}>点此</Link> <span
            style={{color: "red"}}>完成发布</span>
          </div>
        </div>)
      } else {
        return null;
      }
    }
    if (waitPublished && !hasAlert) {
      message.warn("警告：仍有未发布完的版本，请完成发布后再操作");
      this.setState({"hasAlert": true});
    }
    if (!hasEditPermission) {
      return _renderWaitInfo();
    }

    const that = this;
    let {content, overwrites, strategy, resourceBO, versionBO} = this.state;
    const {dispatch} = this.props;
    const namespaceId = namespaceBO.namespaceId;
    const isDiffent = (resourceBO.srcContent || '') != content || (versionBO.strategy || '') != strategy;
    const needUpdateModules = isOnlineEnv() && FORCE_UPDATE_MODULE_APPKY.includes(namespaceBO.appKeyOrGroup) && !namespaceBO.modules;

    // 如果配置格式为json(自定义模式)，需要确保内容是JSON格式，否则不允许发布
    const contentFormat = NamespaceUtils.getContentFormat(namespaceBO);
    const contentValid = contentFormat == "json" ? this._isJSON(content).valid : true;
    const canSumbit = !waitPublished && (isDiffent || overwrites[0]) && contentValid;

    const _submitVersion = function () {
      const params = {
        namespaceId: namespaceId,
        content: content,
        strategy: strategy,
        overwriteVersions: overwrites.join(',')
      }

      dispatch({
        type: getFuncName(NAME_SPACE, 'createVersion'),
        payload: {
          params: params
        },
      });
    }
    const _showKnockoutVersions = function () {
      const params = {
        namespaceId: namespaceId,
        strategy: strategy,
        overwriteVersions: overwrites.join(',')
      }

      queryKnockoutVersions(params).then((res) => {
        that.refs.kockoutListModal.show(res);
      })
    }
    const _showDiff = function () {
      const differProps = {
        mode: "json",
        dataformat: contentFormat,
        dataTitle: versionBO.version || ' ',
        compareTitle: '当前',
        data: resourceBO.srcContent || ' ',
        compareData: content || ' ',
        codeMirror: {
          readOnly: true,
        },
      };

      that.refs.diffStepModal.show(differProps);
    }
    const _renderSubmitBtn = function () {
      if (needUpdateModules) {
        return <Button type="primary" disabled={!canSumbit} onClick={() => {
          that.refs.selectMtlModule.show();
        }}>{getEnvName()}提交发布</Button>
      }

      return <Popconfirm
        title="确认提交发布吗？"
        onConfirm={_showDiff}
      >
        <Button type="primary" disabled={!canSumbit}>{getEnvName()}提交发布</Button>
      </Popconfirm>
    }
    const _rollbackProps = {
      renderRollbackBtn: function () {
        return <Popconfirm
          title="确认回滚吗？"
          onConfirm={() => {
            getRollbackList({
              'namespaceId': namespaceId
            }).then((res) => {
              const views = res;
              that.refs.selectRollbackVersion.show(views);
            });

          }}
        >
          <Button disabled={waitPublished}>提交回滚</Button>
        </Popconfirm>
      },
      onRollbackSelect: function (toVersion) {
        getRollbackView({
          'namespaceId': namespaceId,
          fromVersion: changeBO.versionVersion,
          toVersion: toVersion
        }).then((res) => {

          const views = res;

          that.refs.rollbackModal.show(views);
        });
      },
      onRollbackSubmit: function (fromVersion, toVersion) {
        const params = {'namespaceId': namespaceId, fromVersion: fromVersion, toVersion: toVersion};
        let payload = {
          params: params
        };

        dispatch({
          type: getFuncName(NAME_SPACE, 'rollbackVersion'),
          payload,
        });

      }
    };
    const _openRollbackAny = viewConfig && viewConfig.rollbackAny || isSystemAdmin();
    return (<div style={{"margin": "40px 20px"}}>
      {_openRollbackAny ? _rollbackProps.renderRollbackBtn() : null}
      {_renderSubmitBtn()}
      {_renderWaitInfo()}
      {needUpdateModules ? <SelectMtlModule ref="selectMtlModule" namespace={namespaceBO} nextStep={_showDiff}/> : null}
      <CodeDiffDetail ref="diffStepModal" nextStep={_showKnockoutVersions}/>
      <KockoutList ref="kockoutListModal" nextStep={_submitVersion}/>
      <SelectRollbackVersion ref="selectRollbackVersion" onSelectVersion={_rollbackProps.onRollbackSelect}/>
      <RollbackModal ref="rollbackModal" onHandleOk={_rollbackProps.onRollbackSubmit}/>
    </div>);

  }

  render() {
    const that = this;
    const {dispatch, pageData} = this.props;
    const {loading, detail, namespace} = pageData;
    const appBO = detail && detail.appBO || {};
    const namespaceBO = detail && detail.namespaceBO || {};
    const changeBO = detail && detail.changeBO || {};
    const userMap = detail && detail.userMap || {};
    const waitList = detail && detail.waitList || [];
    const list = detail && detail.list || [];
    const noStrategyList = detail && detail.noStrategyList || [];
    let withStrategyList = detail && detail.list || [];
    let withoutStrategyList = noStrategyList.filter((item) => {
      return item.appVersion != "*";
    });
    const viewConfig = detail.viewConfig || {};
    /*
       for (let i = 0; i < list.length; i++) {
         const each = list[i];
         if (each.appVersion == '*') {
           withStrategyList.push(each);
         } else {
           withoutStrategyList.push(each);
         }
       }
    */

    const isNsReady = namespaceBO.namespaceId;
    const noVersion = !list[0];
    const hasNoContent = isNsReady && noVersion;
    const contentFormat = NamespaceUtils.getContentFormat(namespaceBO)
    const hasEditPermission = UserUtils.hasNsEditPermission(namespaceBO.owners);

    const { resourceBO } = this.state;

    if (resourceBO && resourceBO.scenesContentsMap && Object.keys(resourceBO.scenesContentsMap).length > 0) {
      message.error('多语言配置请前往新平台进行操作');
    }

    const _onSyncLoadLevel = () => {
      let params = {
        namespaceId: namespaceBO.namespaceId,
        action: "SYNC_LOADLEVEL"
      };
      let payload = {
        params: params,
        reparams: params
      };

      dispatch({
        type: getFuncName(NAME_SPACE, 'namespaceTools'),
        payload,
      });
      message.info('刷新完成')

    };


    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>配置管理</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getMyNamespaceList()}>配置列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>配置详情</Breadcrumb.Item>
        </Breadcrumb>
        <div style={{"maxWidth": "1200px"}}>
          {<NamespacePanel appBO={appBO} namespaceBO={namespaceBO} changeBO={changeBO}
                           list={list}
                           userMap={userMap}
                           namespaceShow={this.namespaceShow}
                           namespace={namespace}
                           viewConfig={viewConfig}
                           loading={loading}
                           onOfflineOk={this.offlineNamespace}
                           editHandler={this.createOrUpdateHandler}
                           syncLoadLevel={_onSyncLoadLevel}
          />}
          <div>
            <div style={{"display": " -webkit-box"}}>
              <div style={{"width": "55%", "height": "600px"}}>
                {that._renderContent(namespaceBO, hasNoContent, hasEditPermission)}
                {that._renderBtn(namespaceBO, hasEditPermission, withStrategyList, waitList, changeBO, viewConfig)}
              </div>
              <div style={{"width": "45%", "marginLeft": "10px"}}>

                {that._renderList(withoutStrategyList, withStrategyList, loading, contentFormat)}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

function

mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)

(
  NamespaceDetail
)
;
