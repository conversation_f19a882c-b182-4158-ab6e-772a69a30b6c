import React from 'react';
import {Icon, Modal, Table} from '@alipay/bigfish/antd';
import {formatTime} from '@/utils/TimeUtils';


export default class KockoutList extends React.Component {

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      list: [],
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = (list) => {
    this.setState({
      visible: true,
      list: list || []
    });
  };

  handleOk = () => {
    this.state = this.getInitState();
    this.setState({
      visible: false,
      list: []
    });
  };

  handleCancel = () => {
    this.state = this.getInitState();

    this.setState({
      visible: false,
      list: []
    });
  };

  componentWillReceiveProps(nextProps) {

  }

  getColumns = () => {
    let self = this;
    //Version	AppVersion	Strategy	发布时间	发布人
    const columns = [
      {
        dataIndex: 'version',
        title: 'version'
      }, {
        title: 'AppVersion',
        dataIndex: 'appVersion',
      }, {
        title: 'Strategy',
        dataIndex: 'strategy',
      }, {
        title: '发布时间',
        dataIndex: 'gmtPublish',
        render(text) {
          return formatTime(text);
        },
      }, {
        title: '发布人',
        dataIndex: 'creator',
      }
    ];
    return columns;
  };

  render() {
    const {visible, loading, list} = this.state;
    const that = this;
    const {nextStep} = this.props;
    const onNextStep = function () {
      that.handleOk();
      nextStep && nextStep();
    }
    const _renderList = function (targetList) {
      return (<Table
        columns={that.getColumns()}
        dataSource={targetList}
        rowKey={record => record.version}
        pagination={false}
      />)
    }
    const _renderKockList = function () {
      return (<div>{list.map(item => {
        return <div key={item.reasonId}>
          <div style={{color: "red", "margin": "10px 20px"}}>{item.reason} <Icon type="caret-down"/></div>
          {_renderList(item.targetList)}
        </div>;
      })}</div>);

    }

    return (<Modal title="此次发布会删除：" visible={visible} width={800}
                   onOk={onNextStep} onCancel={this.handleCancel}
                   destroyOnClose>
      {list[0] ? _renderKockList() : <div>暂无</div>}
    </Modal>);
  }

}
