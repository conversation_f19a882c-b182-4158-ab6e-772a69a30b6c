import React from 'react';
import { But<PERSON>, Icon, Table } from '@alipay/bigfish/antd';
import { Link } from 'dva/router';

import styles from './List.less';
import { LinkUtils } from '@/utils/LinkUtils';

import { VersionUtils } from '@/utils/VersionUtils';
import CodeDiffDetail from './CodeDiffDetail';

import { getResourceDetail } from '@/services/version';

export default class VersionList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      overwrites: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (JSON.stringify(nextProps.overwrites) != JSON.stringify(this.props.overwrites)) {
      this.setState({
        overwrites: nextProps.overwrites || ' ',
      });
    }
  }

  getColumns = (
    doCompareResource,
    changeSelectVersion,
    isStrategy,
    overwrites,
    onVersionDel,
    onVersionRecover,
    versionBO,
  ) => {
    const self = this;

    const columns = [
      {
        dataIndex: 'version',
        title: 'Version',
        width: '140px',
        render(text, record) {
          return (
            <div>
              {overwrites.indexOf(record.version) > -1 ? (
                <span className={styles.del}>
                  {record.version}({record.creator}){' '}
                </span>
              ) : (
                <span className={styles.txtEllipsis}>
                  {record.version}({record.creator})
                </span>
              )}
              {versionBO && versionBO.version == record.version ? (
                <span className={styles.red}>
                  <Icon type="check" />
                </span>
              ) : null}
            </div>
          );
        },
      },
    ];
    if (isStrategy) {
      columns.push({
        dataIndex: 'strategy',
        title: 'strategy',
        width: '145px',
        render(text, record) {
          return (
            <div>
              <span className={styles.txtEllipsis}>{text}</span>
                </div>
          );
        }
      });
    } else {
      columns.push({
        dataIndex: 'appVersion',
        title: 'appVersion',
        width: '100px',
      });
    }
    columns.push({
      title: '操作',
      width: '125px',
      render(text, record) {
        const _renderDel = function () {
          if (record.appVersion == '*' && !record.strategy) {
            return null;
          }

          if(versionBO && versionBO.version  && versionBO.version==record.version){
            return null;
          }
          if (overwrites.indexOf(record.version) > -1) {
            return (
              <Button
                type="dashed"
                size="small"
                onClick={function () {
                  onVersionRecover(record.version);
                }}
              >
                恢复
              </Button>
            );
          }
          return (
            <Button
              type="dashed"
              size="small"
              onClick={function () {
                onVersionDel(record.version);
              }}
            >
              删除
            </Button>
          );
        };
        return (
          <div>
            <Button
              type="dashed"
              size="small"
              onClick={function () {
                changeSelectVersion(record);
              }}
            >
              查看
            </Button>{' '}
            <Button
              type="dashed"
              size="small"
              onClick={function () {
                doCompareResource(record.version, record.resourceId);
              }}
            >
              对比
            </Button>{' '}
            {_renderDel()}
            <span
              style={{
                border: '1px dashed #d9d9d9',
                padding: '2px 9px 2px 9px',
                marginLeft: '3px',
              }}
            >
              <Link to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>更多</Link>
            </span>
          </div>
        );
      },
    });
    return columns;
  };

  removeItem = () => {};

  render() {
    const self = this;
    const { loading } = this.props;
    let {
      data,
      versionBO,
      content,
      changeSelectVersion,
      isStrategy,
      overwrites,
      onVersionDel,
      onVersionRecover,
      dataformat
    } = this.props;
    if (data && data[0]) {
      //VersionUtils.sortVersionList(data);
    } else {
      data = [];
    }
    const _doCompareResource = function (version2, resouceId2) {
      getResourceDetail({ resourceId: resouceId2 }).then(res => {
        self.setState({
          version2,
          resouceId2,
          resourceBO2: res,
        });
        const resourceBO2 = res;
        const differProps = {
          mode: 'json',
          dataformat: dataformat,
          dataTitle: version2 || ' ',
          compareTitle: '当前',
          data: (resourceBO2 && resourceBO2.srcContent) || ' ',
          compareData: content || ' ',
          codeMirror: {
            readOnly: true,
          },
        };

        self.refs.codeDiffDetail.show(differProps);
      });
    };

    return (
      <div className={styles.holder}>
        <Table
          loading={loading}
          columns={this.getColumns(
            _doCompareResource,
            changeSelectVersion,
            isStrategy,
            overwrites,
            onVersionDel,
            onVersionRecover,
            versionBO,
          )}
          className= {styles.onRowHover}
          rowClassName={(record, index) => record.version == versionBO.version ? styles.activeRow:''}
          dataSource={data}
          rowKey={record => record.id}
          pagination={false}
        />
        <CodeDiffDetail ref="codeDiffDetail" />
      </div>
    );
  }
}
