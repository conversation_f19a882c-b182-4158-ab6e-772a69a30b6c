import React from 'react';
import {Link} from 'dva/router';

import {Checkbox, Col, Form, Icon, Row, Tooltip, Button, Popconfirm} from '@alipay/bigfish/antd';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {isSystemAdmin,isAliGroup} from "@/utils/EnvUtils";
import {UserUtils} from "@/utils/UserUtils";


import Edit from "../../edit/components/Edit";
import EditModal from "../../edit/components/EditModal";
import Offline from "../../edit/components/Offline";
import styles from '../index.less';
import editStyles from './List.less';

import {NS_SUB_TYPE_JSON, NS_TYPE_CUSTOM} from "../../../../constants/namespace";

const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;


export default class NamespacePanel extends Edit {
  constructor(props) {
    super(props);
  }

  render() {
    const {
      userMap,
      namespaceBO,
      appBO,
      changeBO,
      viewConfig,
      list,
      onOfflineOk,
      editHandler,
      syncLoadLevel
    } = this.props;
    const namespaceId = namespaceBO.namespaceId;
    let {namespace} = this.props;
    namespace = namespace || [];
    const appList = [appBO];
    const self = this;
    const isAdmin = isSystemAdmin();

    const _renderTips = function (tip) {
      return (<Tooltip title={tip} className="help">
        <span style={{"color": "red"}}><Icon type="exclamation-circle"/></span>
      </Tooltip>);

    }


    const _showLoadLevel = function () {
      if (!changeBO.id) {
        return null;
      }
      const key1 = namespaceBO.loadLevel;
      const key2 = changeBO.loadLevel;
      if (key1 != key2) {
        const value1 = ConvertUtils.getLoadLevelName(key1);
        const value2 = ConvertUtils.getLoadLevelName(key2);
        const tips = "加载级别不一致：配置为 " + value1 + "，索引为 " + value2 + "。 请将配置级别修正，或者重新发布一份线上版本";
        return (<span>
                  {_renderTips(tips)} {isAdmin ? (<Popconfirm
          title="涉及发布，确认要将配置的加载级别同步上线吗？"
          onConfirm={() => {
            syncLoadLevel && syncLoadLevel()
          }}
        ><a><Icon type="sync"/></a></Popconfirm>) : null}
                </span>);
      }
      return null;
    }

    const _showType = function () {
      if (!changeBO.id || !changeBO.metas || !JSON.parse(changeBO.metas)) {
        return null;
      }
      const key1 = namespaceBO.type;
      const value1 = ConvertUtils.getNsTypeName(key1);
      const value2 = JSON.parse(changeBO.metas).type;
      if (value1 != value2) {
        const tips = "类型不一致：配置为 " + value1 + "，索引为 " + value2 + "。请将类型修正，或者重新发布一份线上版本";
        return (<span>{_renderTips(tips)}</span>);
      }
      return null;
    }


    return (
      <div style={{"width": "1200px", "padding": "10px"}}>
        <div style={{"display": " -webkit-box"}}>
          <h2>
            基础信息
          </h2>
          <EditModal ref="editModal"
                     onShow={() => self.getNamespaceHandler(namespaceBO.namespaceId)}
                     namespace={namespace} appList={appList}
                     onHandleOk={editHandler}>
            <a><Icon className="hf-ml" type="edit"/></a>
          </EditModal>
          <Offline ref="offlineModal"
                   record={namespaceBO}
                   onShow={() => function () {
                   }}
                   knockoutNamespaceVersion={list}
                   onHandleOk={onOfflineOk}>
            <a><Icon className="hf-ml" type="delete"/></a>
          </Offline>
        </div>
        <Form className={styles.form}>
          <Row gutter={24}></Row>

          <Row>
            <Col span={8}>
              <FormItem label="namespace">{namespaceBO.name}</FormItem>
            </Col>
            <Col span={8}>
              <FormItem label="appKey">{appBO.appName}( {appBO.appKey})</FormItem>
            </Col>
            <Col span={4}>
              <FormItem
                label="加载级别">{ConvertUtils.getLoadLevelName(namespaceBO.loadLevel)} {_showLoadLevel()}</FormItem>
            </Col>
            <Col span={4}>
              <FormItem label="类型">{ConvertUtils.getNsTypeName(namespaceBO.type)}
                {namespaceBO.type === NS_TYPE_CUSTOM && namespaceBO.subType === NS_SUB_TYPE_JSON ? " (" + (ConvertUtils.getNsSubTypeName(namespaceBO.type, namespaceBO.subType)) + ")" : null}
                {_showType()}</FormItem>
            </Col>

          </Row>

          <Row>
            <Col span={6}>
              <FormItem label="创建时间">{namespaceBO.gmtCreateTime} </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="负责人"><span
                className={editStyles.txtEllipsis}>{UserUtils.getUsersDisplayName(namespaceBO.owners, userMap)}</span>
              </FormItem>
            </Col>
            <Col span={6}>
              <FormItem
                label="测试"><span
                className={editStyles.txtEllipsis}>{UserUtils.getUsersDisplayName(namespaceBO.testers, userMap)} </span>{!namespaceBO.testers ? _renderTips("测试负责人不能为空，发布审核与验证时使用(安全生产要求)。请点击标题旁的编辑ICON设置测试负责人。") : null}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={3}>
              <Icon type="flag"/><Link to={LinkUtils.getVersionListOfNs(namespaceBO.name, appBO.appKey)}> 发布单列表</Link>
            </Col>
            <Col span={3}>
              {viewConfig.showReport ?
                <div><Icon type="line-chart"/><Link
                  to={LinkUtils.getNamespaceReport(namespaceBO.namespaceId)}>数据报表</Link></div>
                :
                <div><Icon type="line-chart"/><a
                  href={UrlUtils.getHelpUrl("report")} target="_blank">&nbsp;报表接入</a></div>
              }
            </Col>
            {isAliGroup()? <Col span={3}>
              <div><Icon type="line-chart"/><a
                  href={UrlUtils.getNamespaceFbiUrl(appBO.appKey,namespaceBO.name)} target="_blank">成本与治理</a></div>
            </Col> :null}

            <Col span={3}>
              <Icon type="tool"/> <Link to={LinkUtils.getDebugDetail(namespaceId)}> Debug(新)</Link>
            </Col>
            <Col span={6}>
              <Icon type="info-circle"/> <span style={{color: "red", "fontSize": "18px"}}>~~~(<a
              href={UrlUtils.getHelpUrl("namespace")} target="_blank">帮助文档</a>)~~~</span>
            </Col>

            <Col span={3}>
              <a href={UrlUtils.getNamespaceUrl(namespaceId, null)}>去旧版详情</a>
            </Col>
            <Col span={3}>
              <a href={UrlUtils.getOldDebugUrl(appBO.appKey)}>Debug(旧)</a>
            </Col>

          </Row>

        </Form>

      </div>);
  }
}
