import React from 'react';
import {Modal, Button} from '@alipay/bigfish/antd';
import {message} from 'antd';
import ModuleSelector from '@/components/ModuleSelector';
import {searchModule} from '@/services/search';
import {getArr} from '@/utils/utils';
import {updateNamespace} from '@/services/namespace';

export default class SelectMtlModule extends React.Component {
  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      visible: false,
      moduleIdList: [],
    };
  };

  show = () => {
    this.setState({
      visible: true,
      moduleIdList: [],
    });
  };

  handleCancel = () => {
    this.setState({
      visible: false,
      moduleIdList: [],
    });
  };

  render() {
    const {visible, loading, moduleIdList} = this.state;
    const {nextStep, namespace} = this.props;
    const appKey = namespace.appKeyOrGroup;
    const that = this;
    const handlePublish = function () {
      that.setState({ visible: false });
      nextStep && nextStep();
    }

    return (
      <Modal
        title="关联摩天轮模块"
        visible={visible}
        width={550}
        destroyOnClose
        onCancel={this.handleCancel}
        footer={[
          <Button key="cancle" onClick={this.handleCancel}>
            取消
          </Button>,
          <Button key="skip" onClick={handlePublish}>
            跳过关联并发布
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={() => {
            if (moduleIdList && moduleIdList.length > 0) {
              updateNamespace(Object.assign({}, namespace, { modules: moduleIdList.join(',') }));
              handlePublish();
            } else {
              message.error("请选择至少一个摩天轮模块");
            }
          }}>
            关联并发布
          </Button>,
        ]}>
        <h4 style={{fontSize: 15}}>识别到您的配置还未关联摩天轮模块，请先进行模块关联后再发布配置</h4>
        <ModuleSelector
          appKey={appKey}
          multiple
          style={{width: '500px', marginBottom: '8px', marginTop: '8px'}}
          disabled={!appKey}
          defaultOptions={[]}
          getResult={(res) => {
            // 报错后兜逻辑
            if (res === undefined) {
              return [];
            }
            return getArr(res.content);
          }}
          getData={(keyword) => {
            if (keyword && appKey) {
              return searchModule({ keyword: keyword, appKey, pageNum: 1, pageSize: 8 });
            }
            return Promise.resolve({ content: [] });
          }}
          onChange={(value) => {
            that.setState({ moduleIdList: value });
          }}
        />
        <p style={{ color: "gray" }}>查询不到对应模块或有其他的问题，可搜索加入钉钉【Orange官方答疑群】进行咨询</p>
    </Modal>);
  }

}
