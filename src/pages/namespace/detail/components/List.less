.holder {
  background: #fff;
  padding: 16px;
}

.del {
  text-decoration: line-through;
  text-overflow: ellipsis;
  word-break: break-all;
}

.txtEllipsis {
  text-overflow: ellipsis;
  word-break: break-all;
}

.red {
  color: red;
}

.activeRow {
  background:  #C4E1FF;
}

.onRowHover {
  :global {
    .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
      background-color:  #C4E1FF;
    }

    .ant-table-body .ant-table-row-hover {
      background:  #C4E1FF;
    }

    .ant-table-body .ant-table-row-hover > td {
      background:  #C4E1FF;
    }
  }
}


