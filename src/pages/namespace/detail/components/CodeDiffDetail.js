import React from 'react';
import {Modal} from '@alipay/bigfish/antd';
import {CodeDiffer} from '@ali/emasd-pro';
import {isEmpty} from "../../../../utils/LangUtils.js";


export default class CodeDiffDetail extends React.Component {

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      differProps: undefined,
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = (differProps) => {
    this.setState({
      visible: true,
      differProps: differProps
    });
  };

  handleOk = () => {
    this.state = this.getInitState();
    this.setState({
      visible: false,
      differProps: undefined
    });
  };

  handleCancel = () => {
    this.state = this.getInitState();

    this.setState({
      visible: false,
      differProps: undefined
    });
  };

  componentWillReceiveProps(nextProps) {
    /*if (JSON.stringify(nextProps.differProps) != JSON.stringify(this.props.differProps)) {
      this.setState({
        differProps: nextProps.differProps,
      });
    }*/
  }

  render() {
    const {visible, loading, differProps} = this.state;
    const that = this;
    const {nextStep} = this.props;
    const onNextStep = function () {
      that.handleOk();
      nextStep && nextStep();
    }

    if (differProps) {
      try {
        differProps.data = differProps.dataformat === 'json' && !isEmpty(differProps.data) ? JSON.stringify(JSON.parse(differProps.data), null, 4) : differProps.data;
        differProps.compareData = differProps.dataformat === 'json' && !isEmpty(differProps.compareData) ? JSON.stringify(JSON.parse(differProps.compareData), null, 4) : differProps.compareData;
      } catch (e) {
        console.log('parse data error',e);
        differProps.data = differProps.data;
        differProps.compareData = differProps.compareData;

      }
    }

    return (<Modal title={differProps?.title || '数据对比'} visible={visible} width={800}
                   onOk={onNextStep} onCancel={this.handleCancel}
                   destroyOnClose>
      <CodeDiffer  {...differProps}/>
    </Modal>);
  }

}
