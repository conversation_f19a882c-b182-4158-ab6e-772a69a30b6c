import React from 'react';
import {Modal, Select, Form, Input, Spin, Radio, Checkbox, Table, Icon, Button} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';
import styles from './List.less';

const FormItem = Form.Item;
import indexStyles from '../index.less';
import {checkForm} from '@/utils/utils';
import {LinkUtils} from '@/utils/LinkUtils';
import { VersionUtils } from '@/utils/VersionUtils';
import {
  RECORD_STATUS_SUCCESS
} from "@/constants/version";


const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};
export default class SelectRollbackVersion extends React.Component {

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      versionList: undefined,
      onSelectVersion: undefined,
      formData: {},
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = (versionList, onSelectVersion) => {
    this.setState({
      visible: true,
      versionList: versionList,
      onSelectVersion: onSelectVersion,
      formData: {},
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        this.setState({
          visible: false,
          versionList: undefined,
          onSelectVersion: undefined,
          formData: {},
        });
      },
    );
  };

  handleCancel = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {

        let {onHandleCancel} = this.props;
        onHandleCancel && onHandleCancel();
        this.setState({
          visible: false,
          versionList: undefined,
          onSelectVersion: undefined,
          formData: {},
        });
      },
    );
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);

    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {

      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  componentWillReceiveProps(nextProps) {

  }

  getColumns = (
    onSelectVersion,
  ) => {
    const self = this;

    const columns = [
      {
        dataIndex: 'version',
        title: 'Version',
        width: '140px'
      }, {
        dataIndex: 'strategy',
        title: 'strategy',
        width: '145px',
        render(text, record) {
          return (
            <div>
              <span className={styles.txtEllipsis}>{text}</span>
            </div>
          );
        }
      },
      {
        dataIndex: 'creator',
        title: '创建人',
        width: '80px'
      },
      {
        dataIndex: 'gmtPublishTime',
        title: '发布时间',
        width: '125px'
      }, {
        title: '操作',
        width: '125px',
        render(text, record) {

          return (
            <div>
              <Button
                size="small"
                type="primary"
                onClick={function () {
                  onSelectVersion(record.version);
                }}
              >
                回滚
              </Button>{' '}
              <span
                style={{
                  border: '1px dashed #d9d9d9',
                  padding: '2px 9px 2px 9px',
                  marginLeft: '3px',
                }}
              >
              <Link to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>详情</Link>
            </span>
            </div>
          );
        },
      }
    ];
    return columns;
  };

  render() {
    const {visible, loading, versionList, formData} = this.state;
    const {onSelectVersion} = this.props
    const {getFormProps, changeForm} = this;
    return (
      <Modal title="选择回滚版本" visible={visible} okText="关闭"
             onOk={this.handleCancel} onCancel={this.handleCancel} width={1200}
             destroyOnClose>
        <div className={styles.holder}>
          <Table
            loading={loading}
            columns={this.getColumns(
              onSelectVersion
            )}
            dataSource={versionList}
            rowKey={record => record.id}
            pagination={false}
          />
        </div>
      </Modal>
    );
  }

}
