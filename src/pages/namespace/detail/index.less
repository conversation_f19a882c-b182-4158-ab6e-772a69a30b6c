@import "../../../layouts/app.less";

.holder {
  background: #fff;
  padding: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}


.form {
  margin-top: 8px;
  margin-bottom: 8px;


  padding: 24px;
  background: #fff;
  border-radius: 6px;

  :global {
    .ant-form-item {
      display: flex;
      margin-bottom: 8px;
    }

    .ant-form-item-label {
      overflow: visible;
    }
  }
}

.downlink {
  color: #bbb;
  font-size: 16px;
}


.memo {
  height: 120px;
  width: 400px;
  word-wrap: break-word;
}


.versionStatus {
  padding: 0 8px;
  min-width: 56px;
  text-align: center;
  background-color: #f4faff;
  color: #71bbff;
  border-radius: 2px;
  border: 1px solid #71bbff;
}

.process {
  background: #fff;
  padding: 16px;
  margin-top: 14px;
  margin-bottom: 8px;
}


.processdetail {
  padding: 20px;
  text-align: center;
}

.processtip {
  padding-left: 20px;
}


.tabTitle {
  font-size: 14px;

}

.tabLine {
  margin: 6px 6px 0px 6px;
}

.tabLabel {
  text-align: left;
  font-size: 8px;

}

.tabValue {
  margin-left: 20px;
  text-align: right;
  font-size: 14px;

}

.hBox {
  display: -webkit-box;
  -webkit-box-orient: horizontal;
}

.vBox {
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.fBox {
  display: -webkit-flex;
  flex-wrap: wrap;
}

.auto {
  -webkit-box-flex: 1;
  flex-grow: 1;
  flex-shrink: 1;
}


.formdata {
  margin-top: 8px;
  margin-bottom: 8px;

  border-radius: 6px;

  :global {
    .ant-form-item {
      display: flex;
      margin-bottom: 8px;
    }

    .ant-form-item-label {
      overflow: visible;
    }
  }
}

.dataholder {
  background: #fff;
  padding: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
  margin-left: 8px;
  width: 200px;
  flex-wrap: wrap;
}

.formIntem {
  border-top-width: 20px;
}

.defaultSpanner {
  color: red;
}
