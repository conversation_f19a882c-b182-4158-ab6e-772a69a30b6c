import React from 'react';
import {<PERSON>Complete, Button, Form} from '@alipay/bigfish/antd';


import styles from './QueryForm.less';
import {UrlUtils} from "@/utils/LinkUtils";
import {isSubstring} from "@/utils/utils";

const FormItem = Form.Item;

class QueryForm extends React.Component {
  handleSubmit = (e) => {
    e.preventDefault();
    const {onQuery} = this.props;
    this.props.form.validateFields((err, values) => {
      if (!err) {
        onQuery(values);
      }
    });
  };

  render() {
    const {getFieldDecorator} = this.props.form;
    const params = this.props.params;
    let {appList} = this.props;
    appList = appList || [];
    let dataSource = appList.map(item => {
      return {value: item.appKey, text: item.appKey + '-' + item.appName}
    });
    return (
      <div className={styles.holder} style={{"margin": "10px 20px"}}>
        <Form layout="inline" className="clearfix" onSubmit={this.handleSubmit}>
          <FormItem label="应用">
            {getFieldDecorator('appKey', {
              initialValue: params.appKey,
              rules: [
                {required: true, message: '请输入appKey或名称!'},
              ],
            })(
              <AutoComplete
                style={{width: 300}}
                dataSource={dataSource}
                placeholder="所有app应用"
                // value={params.appKey}
                filterOption={(inputValue, option) => isSubstring(inputValue, option.props.children)}
              />
            )}
          </FormItem>
          <FormItem>
            <Button className="st-mr" type="primary" htmlType="submit">
              查询
            </Button>
          </FormItem>
          <FormItem>
            <Button className="st-mr" onClick={function (e) {
              location.href = UrlUtils.getMappcenterHomeUrl();
            }}>开通应用</Button>
          </FormItem>
        </Form>
      </div>
    );
  }
}

export default Form.create()(QueryForm);
