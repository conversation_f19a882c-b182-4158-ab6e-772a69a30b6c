import React from 'react';
import {Button, Popconfirm, Table} from '@alipay/bigfish/antd';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {Link} from 'dva/router';
import {message} from 'antd';


import styles from './List.less';
import Edit from "../../edit/components/Edit";
import EditModal from "../../edit/components/EditModal";
import Offline from "../../edit/components/Offline";
import {isSystemAdmin} from "@/utils/EnvUtils";
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {UserUtils} from "@/utils/UserUtils";
import {permissionApply} from '@/services/namespace';


export default class List extends Edit {
    getColumns = (userMap, moduleMap) => {
        let self = this;
        const _renderApply = function (record, role) {
            const roleName = role == 'nsOwner' ? '负责人' : '测试';
            const tips = `您可以联系负责人直接操作，或者点此走审批流程，确定要申请${record.name}的${roleName}权限吗(注意不要重复申请)？`;

            return (
                <Popconfirm
                    title={tips}
                    onConfirm={() => {
                        _applySubmit(record.namespaceId, role)
                    }}
                > <Button type="dashed">申请{roleName}权限</Button>
                </Popconfirm>)
        }
        const _applySubmit = function (namespaceId, role) {

            permissionApply({
                objectKey: namespaceId,
                permissionRole: role

            }).then((res) => {
                if (res == 'SKIP') {
                    message.success("申请成功，可去我的配置里查看", 10);
                } else if (res) {
                    message.success(<div style={{"textAligh": "left"}}><h4>申请成功</h4>申请权限流程已创建，<a
                        href={UrlUtils.getBpmsUrl(res)} target="_blank">查看审批流程</a></div>, 10);
                }
            });
        };

        const columns = [
            {
                dataIndex: 'name',
                title: '名称',
                render(text, record) {
                    return (
                        <div>
                            <span>{text}</span>
                            {UserUtils.hasNsEditPermission(record.owners) || UserUtils.hasNsTestPermission(record.testers) ?
                                <span><Link
                                    to={LinkUtils.getNamespaceDetail(record.namespaceId, null)}>详情</Link></span> : null}
                            <span> {!UserUtils.hasNsEditPermission(record.owners) ? _renderApply(record, 'nsOwner') : null}</span>
                            <span> {!UserUtils.hasNsEditPermission(record.owners) && !UserUtils.hasNsTestPermission(record.testers) ? _renderApply(record, 'nsTester') : null}</span>

                        </div>
                    );
                }
            }, {
                title: '类型',
                dataIndex: 'type',
                render(text, record) {
                    return (
                        <div>
                            <p>{ConvertUtils.getNsTypeName(text)}</p>
                        </div>
                    );
                },
            }, {
                title: '加载级别',
                dataIndex: 'loadLevel',
                render(text, record) {
                    return (
                        <div>
                            <p>{ConvertUtils.getLoadLevelName(record.loadLevel)}</p>
                        </div>
                    );
                },
            },
            {
                title: '摩天轮模块',
                dataIndex: 'modules',
                render(text) {
                    return <div>{ConvertUtils.getModulesName(text, moduleMap)}</div>;
                },
            },
            {
                title: '负责人',
                dataIndex: 'owners',
                width: 250,
                render(text) {
                    return <span>{UserUtils.getUsersDisplayName(text, userMap)}</span>;
                },
            },
            {
                title: '测试',
                dataIndex: 'testers',
                width: 80,
                render(text) {
                    return <span>{UserUtils.getUsersDisplayName(text, userMap)}</span>;
                },
            },
            {
                title: '描述',
                dataIndex: 'detail',
                width: 200
            },
        ];
        if (isSystemAdmin()) {
            columns.push({
                title: '操作',
                render(text, record) {
                    let {knockoutNamespaceVersion, namespace, appList} = self.props;
                    knockoutNamespaceVersion = knockoutNamespaceVersion || [];
                    return (
                        <div>
                            <EditModal namespace={namespace}
                                       onShow={() => self.getNamespaceHandler(record.namespaceId)}
                                       appList={appList}
                                       onHandleOk={self.editNamespaceHandler}>
                                <a>编辑</a>
                            </EditModal>
                            <Offline record={record}
                                     onShow={() => self.offlineShowHandler(record.namespaceId)}
                                     knockoutNamespaceVersion={knockoutNamespaceVersion}
                                     onHandleOk={self.offlineNamespaceHandler}>
                                <a>下线</a>
                            </Offline>
                            <p><Link to={LinkUtils.getVersionListOfNs(record.name, record.appKeyOrGroup)}>发布列表</Link>
                            </p>
                        </div>
                    );
                },
            });
        }
        return columns;
    };

    removeItem = () => {
    };

    render() {
        const {loading} = this.props;
        let {data, userMap, moduleMap} = this.props;
        if (data && data[0]) {
        } else {
            data = [];
        }
        return (
            <div className={`${styles.holder} ${styles.tableBreak}`}>
                <Table
                    loading={loading}
                    columns={this.getColumns(userMap, moduleMap)}
                    dataSource={data}
                    rowKey={record => record.namespaceId}
                    pagination={false}
                />
            </div>
        );
    }
}
