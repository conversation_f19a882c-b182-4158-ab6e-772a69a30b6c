import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';
import {Breadcrumb, Button, Col, Row} from '@alipay/bigfish/antd';
import {getFuncName} from '@/utils/utils';
import {LinkUtils} from '@/utils/LinkUtils';

import QueryForm from './components/QueryForm';

import List from './components/List';

import {EditIndex} from '../edit/EditIndex';


import namespace from '@/config/namespace';
import EditModal from "../edit/components/EditModal";

const NAME_SPACE = namespace.namespace.list;

class NamespaceList extends EditIndex {
  namespace = NAME_SPACE;

  componentWillMount() {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }

  render() {
    const {dispatch, data} = this.props;
    const {namespaceList, knockoutNamespaceVersion, namespace, loading, appList, params, userMap, moduleMap} = data;
    const queryHandler = (params) => {
      dispatch({
        type: getFuncName(NAME_SPACE, 'queryList'),
        payload: {params}
      });
    };

    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>配置管理</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getNamespaceList()}>全部配置</Link>
          </Breadcrumb.Item>
        </Breadcrumb>
        <div>
          <Row>
            <Col span={18}>
              <QueryForm params={params} appList={appList} onQuery={queryHandler}/>
            </Col>
            <Col span={6}>
              <div className="pull-right" style={{margin: '16px 0 14px 0'}}>
                <EditModal namespace={{}} appList={appList}
                           onHandleOnline={this.availableNamespace}
                           onHandleOk={this.createOrUpdateHandler}>
                  <Button id="applyButton" type="primary">新建配置</Button>
                </EditModal>
              </div>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <List data={namespaceList}
                    reload={this.reload}
                    appList={appList}
                    namespaceShow={this.namespaceShow}
                    offlineShow={this.offlineShow}
                    namespace={namespace}
                    userMap={userMap}
                    moduleMap={moduleMap}
                    knockoutNamespaceVersion={knockoutNamespaceVersion}
                    loading={loading}
                    onHandleOnline={this.availableNamespace}
                    onOfflineOk={this.offlineNamespace}
                    editHandler={this.createOrUpdateHandler}/>
            </Col>
          </Row>
        </div>
      </div>
    );
  }
}

function mapStateToProps(state) {
  return {
    data: state[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(NamespaceList);
