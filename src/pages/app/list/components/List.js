import React from 'react';
import {Table} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';


import {formatTime} from '@/utils/TimeUtils';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";


import styles from './List.less';


export default class List extends React.Component {
  getColumns = () => {

    const self = this;
    const columns = [
      {
        title: 'AppKey',
        dataIndex: 'appKey',
        render(text) {
          return text;
        },
      }, {
        title: 'appName',
        dataIndex: 'appName',
      }, {
        title: 'motuAppId',
        dataIndex: 'motuAppId',
        render(text) {
          return text;
        },
      }, {
        title: '应用中心ID',
        dataIndex: 'appId',
        render(text) {
          return <span> <a href={UrlUtils.getMappcenterAppUrl(text)} target="_blank">
            {text}</a></span>;
        },
      },
      {
        title: '操作',
        render(text, record) {
          return (
            <div>
              <span>
                <Link to={LinkUtils.getNamespaceList(record.appKey)}>配置列表</Link>

              </span> | <span style={{"marginLeft": "2px"}}>
                <Link to={LinkUtils.getVersionListOfNs('', record.appKey)}>版本列表</Link>
              </span> | <span style={{"marginLeft": "2px"}}>
                 <Link to={LinkUtils.getAppDashboard(record.appKey)}>整体大盘</Link>
               </span> | <span style={{"marginLeft": "2px"}}>
                <a href={UrlUtils.getAppFbiUrl(record.appKey)} target="_blank">云上报表</a>
               </span> | <span style={{"marginLeft": "2px"}}>
                  <Link to={LinkUtils.getAppDetail(record.appKey)}>设置管理</Link>
               </span>
            </div>
          );
        },
      }

    ];
    return columns;
  };

  removeItem = () => {
  };

  render() {
    const {loading} = this.props;
    let {data} = this.props;
    if (data && data[0]) {
    } else {
      data = [];
    }
    const pagination = {
      pageSize: 20,
      total: data.length || 0,
      showTotal: (total, range) => `共 ${total} 条数据`,
    };

    return (
      <div className={styles.holder}>
        <Table
          loading={loading}
          columns={this.getColumns()}
          dataSource={data}
          rowKey={record => record.appKey}
          pagination={pagination}
        />
      </div>
    );
  }
}
