import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';
import {Form,Icon,Input,Breadcrumb} from '@alipay/bigfish/antd';
import {getFuncName} from '@/utils/utils';
import {LinkUtils} from '@/utils/LinkUtils';
import {CONFIG_KEY_DEFAULT} from '@/constants/setting';


import List from './components/List';
const FormItem = Form.Item;


import namespace from '@/config/namespace';

const NAME_SPACE = namespace.app.list;

class AppList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      keyword: ''

    };
  }
  componentWillMount() {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }

  render() {
    const that=this;
    const {dispatch, pageData} = this.props;
    const {keyword} = this.state;

    const {appList,loading} = pageData;


    const retList = appList.filter(v => !keyword?true:JSON.stringify(v).indexOf(keyword)>-1);

    const formLayout = {
      labelCol: {
        span: 5,
      },
      wrapperCol: {
        span: 17,
      },
    };

    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>管理中心</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getAppList()}>App列表</Link>
          </Breadcrumb.Item>
        </Breadcrumb>
        <Form layout="inline" className="clearfix" >

        <FormItem label="关键字">
          <Input style={{width: "300px"}}
            value={keyword}
            placeholder="可输入关键字搜索"
            onChange={(e) => {
              that.setState({"keyword": e.target.value || ''})
            }}
          />
        </FormItem>
        <FormItem >
          <Link to={LinkUtils.getAppDetail(CONFIG_KEY_DEFAULT)}>查看全局配置</Link>
        </FormItem>
        </Form>


        <List data={retList}  loading={loading}/>
      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(AppList);
