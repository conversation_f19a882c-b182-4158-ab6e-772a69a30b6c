import React from 'react';
import {Checkbox, Form, Input, Modal, Select, Spin} from '@alipay/bigfish/antd';

import {checkForm} from '@/utils/utils';
import {isSystemAdmin} from "@/utils/EnvUtils";

const CheckboxGroup = Checkbox.Group;
const {Option} = Select;

const FormItem = Form.Item;

const formLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 14,
  },
};

export default class EditReportModal extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    let {detailDO} = this.props;
    let initForm = {};
    if (detailDO) {
      initForm = {
        statsDurationSeconds: detailDO.statsDurationSeconds || '',
        samplingRate: detailDO.samplingRate || '',
        baseAppStrategy: detailDO.baseAppStrategy || '',
        indexRateReportId: detailDO.indexRateReportId || '',
        configRateReportId: detailDO.configRateReportId || '',
        diffIndexReportId: detailDO.diffIndexReportId || '',
        configUpdateReportId: detailDO.configUpdateReportId || '',
        configUseReportId: detailDO.configUseReportId || '',
        dmInsightConfigUpdateReportId: detailDO.dmInsightConfigUpdateReportId || '',
        enabledTasks: detailDO.enabledTasks || [],
      }
    }
    return {
      formData: initForm,
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        const {formData} = this.state;
        let {onHandleOk} = this.props;
        onHandleOk && onHandleOk(formData);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    this.setState(this.getInitState());
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);
    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'type': {
          if (!value) {
            help = '必选';
          }
          break;
        }

      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  componentWillReceiveProps(nextProps) {

    if (nextProps.detailDO && JSON.stringify(nextProps.detailDO) != JSON.stringify(this.props.detailDO)) {
      this.changeForm({
        statsDurationSeconds: nextProps.detailDO.statsDurationSeconds || '',
        samplingRate: nextProps.detailDO.samplingRate || '',
        baseAppStrategy: nextProps.detailDO.baseAppStrategy || '',
        indexRateReportId: nextProps.detailDO.indexRateReportId || '',
        configRateReportId: nextProps.detailDO.configRateReportId || '',
        diffIndexReportId: nextProps.detailDO.diffIndexReportId || '',
        configUpdateReportId: nextProps.detailDO.configUpdateReportId || '',
        configUseReportId: nextProps.detailDO.configUseReportId || '',
        dmInsightConfigUpdateReportId: nextProps.detailDO.dmInsightConfigUpdateReportId || '',
        enabledTasks: nextProps.detailDO.enabledTasks || [],
      });
    }

  }


  render() {
    let {getFormProps, changeForm} = this;
    const {visible, formData, loading} = this.state;
    const isAdmin = isSystemAdmin();

    const {allReportTasks} = this.props;
    const toString = function (key) {
      return '' + key;
    }
    return (
      <Modal title="修改报表配置" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}>
        <Spin spinning={loading}>
          <Form ref="form" layout="horizontal">
            <FormItem label="统计周期(s)" {...getFormProps('statsDurationSeconds')}>
              <Input
                value={formData.statsDurationSeconds}
                onChange={(e) => {
                  changeForm({statsDurationSeconds: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="更新采样率" {...getFormProps('samplingRate')} help="比如0.01">
              <Input
                value={formData.samplingRate}
                onChange={(e) => {
                  changeForm({samplingRate: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="更新率基础策略" {...getFormProps('baseAppStrategy')}>
              <Input
                value={formData.baseAppStrategy}
                disabled={!isAdmin}
                onChange={(e) => {
                  changeForm({baseAppStrategy: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="索引更新率报表ID" {...getFormProps('indexRateReportId')}>
              <Input
                value={formData.indexRateReportId}
                onChange={(e) => {
                  changeForm({indexRateReportId: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="配置更新率报表ID" {...getFormProps('configRateReportId')}>
              <Input
                value={formData.configRateReportId}
                onChange={(e) => {
                  changeForm({configRateReportId: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="差量报表ID" {...getFormProps('diffIndexReportId')}>
              <Input
                value={formData.diffIndexReportId}
                onChange={(e) => {
                  changeForm({diffIndexReportId: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="更新报表ID" {...getFormProps('configUpdateReportId')}>
              <Input
                value={formData.configUpdateReportId}
                onChange={(e) => {
                  changeForm({configUpdateReportId: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="使用报表ID" {...getFormProps('configUseReportId')}>
              <Input
                value={formData.configUseReportId}
                onChange={(e) => {
                  changeForm({configUseReportId: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="dmInsight配置更新报表ID" {...getFormProps('dmInsightConfigUpdateReportId')}>
              <Input
                value={formData.dmInsightConfigUpdateReportId}
                onChange={(e) => {
                  changeForm({dmInsightConfigUpdateReportId: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="打开开报表任务" {...getFormProps('enabledTasks')}>
              <Checkbox.Group options={allReportTasks}
                              value={formData.enabledTasks}
                              onChange={(checkedValues) => {
                                changeForm({enabledTasks: checkedValues});
                              }}/>
            </FormItem>
          </Form>
        </Spin>
      </Modal>
    );
  }
}
