import React from 'react';
import {Checkbox, Form, Input, Modal, Select, Spin} from '@alipay/bigfish/antd';

import {SETTING_BOOLEAN_ARR} from '@/constants/setting';
import {checkForm} from '@/utils/utils';
import {isSystemAdmin} from "@/utils/EnvUtils";
import {showBooleanValue} from "@/utils/LangUtils";


const CheckboxGroup = Checkbox.Group;
const {Option} = Select;

const FormItem = Form.Item;

const formLayout = {
    labelCol: {
        span: 8,
    },
    wrapperCol: {
        span: 14,
    },
};

export default class EditExtModal extends React.Component {
    static propTypes = {

        // appList: React.PropTypes.Array,
        //packageDO: React.PropTypes.object,
    };

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }


    getInitState = () => {
        return {
            formData: {},
            configKey: undefined,
            configCode: undefined,
            visible: false,
            checking: false,
            loading: false,
        };

    };

    show = (detailDO, configKey, configCode) => {
        this.setState({
            visible: true,
            formData: {
                massEnv: detailDO.massEnv || '',
                openWopCheck: showBooleanValue(detailDO.openWopCheck) || '',
                closeCfStepCheck: showBooleanValue(detailDO.closeCfStepCheck) || ''
            },
            configKey: configKey,
            configCode: configCode,
        });
    };

    handleOk = () => {
        let that = this;

        this.setState(
            {
                checking: true,
            },
            () => {
                if (!checkForm(this.refs.form)) {
                    return;
                }

                const {formData, configKey, configCode} = this.state;
                let {onHandleOk} = this.props;
                onHandleOk && onHandleOk(formData, configKey, configCode);
                this.setState({
                    visible: false,
                });
            },
        );
    };

    handleCancel = () => {
        this.setState(this.getInitState());
    };

    changeForm = (params) => {
        const newData = Object.assign({}, this.state.formData, params);
        this.setState({
            formData: newData,
            checking: false,
        });
    };

    getFormProps = (name) => {
        const {checking, formData} = this.state;
        let help = '';
        const value = formData[name];
        if (checking) {
            switch (name) {
                case 'type': {
                    if (!value) {
                        help = '必选';
                    }
                    break;
                }

            }
        }
        return {
            ...formLayout,
            help,
            validateStatus: help ? 'error' : '',
        };
    };

    componentWillReceiveProps(nextProps) {
        /*const that = this;
        console.log('will', nextProps)

        if (nextProps.detailDO && JSON.stringify(nextProps.detailDO) != JSON.stringify(this.props.detailDO) || nextProps.configCode != this.props.configCode) {
          console.log('change', nextProps)
          this.changeForm({
            openForceGrayStep: showBooleanValue(nextProps.detailDO.openForceGrayStep) || '',
            openDiffProbe: showBooleanValue(nextProps.detailDO.openDiffProbe) || '',
            diffProbeGapMinutes: nextProps.detailDO.diffProbeGapMinutes || '',
          });
          that.setState({configKey: nextProps.configKey, configCode: nextProps.configCode})
        }*/

    }


    render() {
        let {getFormProps, changeForm} = this;
        const {visible, formData, loading} = this.state;
        const isAdmin = isSystemAdmin();

        const toString = function (key) {
            return '' + key;
        }

        return (
            <Modal title="修改扩展配置" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}>
                <Spin spinning={loading}>
                    <Form ref="form" layout="horizontal">
                        <FormItem label="Mass的集群名" {...getFormProps('massEnv')} help="高危，未经确认不要操作">
                            <Input
                                value={formData.massEnv}
                                onChange={(e) => {
                                    changeForm({massEnv: e.target.value});
                                }}
                            />
                        </FormItem>
                        <FormItem label="打开WOP卡口" {...getFormProps('openWopCheck')} >
                            <Select
                                value={formData.openWopCheck}
                                onChange={(v) => {
                                    changeForm({openWopCheck: v});
                                }}
                            >
                                {SETTING_BOOLEAN_ARR.map((item) => {
                                    return <Option key={"wop_" + item.value} value={toString(item.value)}>{item.label}</Option>;
                                })}
                            </Select>
                        </FormItem>
                        <FormItem label="关闭Changefree精细化管控" {...getFormProps('closeCfStepCheck')}>
                            <Select
                                value={formData.closeCfStepCheck}
                                onChange={(v) => {
                                    changeForm({closeCfStepCheck: v});
                                }}
                            >
                                {SETTING_BOOLEAN_ARR.map((item) => {
                                    return <Option key={"cf_" + item.value} value={toString(item.value)}>{item.label}</Option>;
                                })}
                            </Select>
                        </FormItem>
                    </Form>
                </Spin>
            </Modal>
        );
    }
}
