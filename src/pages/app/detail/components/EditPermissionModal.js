import React from 'react';
import {Checkbox, Form, Input, Modal, Select, Spin} from '@alipay/bigfish/antd';

import {SETTING_BOOLEAN_ARR} from '@/constants/setting';
import {checkForm} from '@/utils/utils';
import {isSystemAdmin} from "@/utils/EnvUtils";
import {showBooleanValue} from "@/utils/LangUtils";


const CheckboxGroup = Checkbox.Group;
const {Option} = Select;

const FormItem = Form.Item;

const formLayout = {
    labelCol: {
        span: 8,
    },
    wrapperCol: {
        span: 14,
    },
};

export default class EditPermissionModal extends React.Component {
    static propTypes = {

        // appList: React.PropTypes.Array,
        //packageDO: React.PropTypes.object,
    };

    constructor(props) {
        super(props);
        this.state = this.getInitState();
    }


    getInitState = () => {
        return {
            formData: {},
            configKey: undefined,
            configCode: undefined,
            visible: false,
            checking: false,
            loading: false,
        };

    };

    show = (detailDO, configKey, configCode) => {
        this.setState({
            visible: true,
            formData: {
                appAdmins: detailDO.appAdmins || '',
                gocAdmins: detailDO.gocAdmins || ''
            },
            configKey: configKey,
            configCode: configCode,
        });
    };

    handleOk = () => {
        let that = this;

        this.setState(
            {
                checking: true,
            },
            () => {
                if (!checkForm(this.refs.form)) {
                    return;
                }

                const {formData, configKey, configCode} = this.state;
                let {onHandleOk} = this.props;
                onHandleOk && onHandleOk(formData, configKey, configCode);
                this.setState({
                    visible: false,
                });
            },
        );
    };

    handleCancel = () => {
        this.setState(this.getInitState());
    };

    changeForm = (params) => {
        const newData = Object.assign({}, this.state.formData, params);
        this.setState({
            formData: newData,
            checking: false,
        });
    };

    getFormProps = (name) => {
        const {checking, formData} = this.state;
        let help = '';
        const value = formData[name];
        if (checking) {
            switch (name) {
                case 'type': {
                    if (!value) {
                        help = '必选';
                    }
                    break;
                }

            }
        }
        return {
            ...formLayout,
            help,
            validateStatus: help ? 'error' : '',
        };
    };

    componentWillReceiveProps(nextProps) {


    }


    render() {
        let {getFormProps, changeForm} = this;
        const {visible, formData, loading} = this.state;
        const isAdmin = isSystemAdmin();

        const toString = function (key) {
            return '' + key;
        }

        return (
            <Modal title="修改权限配置" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}>
                <Spin spinning={loading}>
                    <Form ref="form" layout="horizontal">
                        <FormItem label="APP负责人" {...getFormProps('appAdmins')} help="输入工号，逗号分割，用于跳过灰度、调整加载级别等审批">
                            <Input
                                value={formData.appAdmins}
                                onChange={(e) => {
                                    changeForm({appAdmins: e.target.value});
                                }}
                            />
                        </FormItem>
                        <FormItem label="安全生产负责人" {...getFormProps('gocAdmins')} help="输入工号，逗号分割，用于一键跳过所有发布流程">
                                                    <Input
                                                        value={formData.gocAdmins}
                                                        onChange={(e) => {
                                                            changeForm({gocAdmins: e.target.value});
                                                        }}
                                                    />
                                                </FormItem>
                    </Form>
                </Spin>
            </Modal>
        );
    }
}
