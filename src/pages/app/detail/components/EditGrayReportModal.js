import React from 'react';
import {Checkbox, Form, Input, Modal, Select, Spin} from '@alipay/bigfish/antd';

import {SETTING_BOOLEAN_ARR} from '@/constants/setting';
import {checkForm} from '@/utils/utils';
import {isSystemAdmin} from "@/utils/EnvUtils";
import {showBooleanValue} from "@/utils/LangUtils";

const CheckboxGroup = Checkbox.Group;
const {Option} = Select;

const FormItem = Form.Item;

const formLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 14,
  },
};

export default class EditGrayReportModal extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    let {detailDO} = this.props;
    let initForm = {};
    if (detailDO) {
      initForm = {
        openRatioGray: showBooleanValue(detailDO.openRatioGray) || '',
        grayEffectUrlTemplate: detailDO.grayEffectUrlTemplate || '',
        minAppVersionOfSupportOsAndModel: detailDO.minAppVersionOfSupportOsAndModel || '',
        namespaceNamesOfSupportOsAndModel: detailDO.namespaceNamesOfSupportOsAndModel || '',
        maxGrayCntOfMassCircle: detailDO.maxGrayCntOfMassCircle || ''
      }
    }
    return {
      formData: initForm,
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        const {formData} = this.state;
        let {onHandleOk} = this.props;
        onHandleOk && onHandleOk(formData);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    this.setState(this.getInitState());
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);
    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'type': {
          if (!value) {
            help = '必选';
          }
          break;
        }

      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  componentWillReceiveProps(nextProps) {

    if (nextProps.detailDO && JSON.stringify(nextProps.detailDO) != JSON.stringify(this.props.detailDO)) {
      this.changeForm({
        openRatioGray: showBooleanValue(nextProps.detailDO.openRatioGray) || '',
        grayEffectUrlTemplate: nextProps.detailDO.grayEffectUrlTemplate || '',
        minAppVersionOfSupportOsAndModel: nextProps.detailDO.minAppVersionOfSupportOsAndModel,
        namespaceNamesOfSupportOsAndModel: nextProps.detailDO.namespaceNamesOfSupportOsAndModel,
        maxGrayCntOfMassCircle: nextProps.detailDO.maxGrayCntOfMassCircle,
      });
    }

  }


  render() {
    let {getFormProps, changeForm} = this;
    const {visible, formData, loading} = this.state;
    const isAdmin = isSystemAdmin();

    const {allReportTasks} = this.props;
    const toString = function (key) {
      return '' + key;
    }
    return (
      <Modal title="修改灰度配置" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel} width={800}>
        <Spin spinning={loading}>
          <Form ref="form" layout="horizontal">
            <FormItem label="灰度效果页地址" {...getFormProps('grayEffectUrlTemplate')}>
              <Input
                value={formData.grayEffectUrlTemplate}
                onChange={(e) => {
                  changeForm({grayEffectUrlTemplate: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="支持Os、Model的最小APP版本" {...getFormProps('minAppVersionOfSupportOsAndModel')}>
              <Input
                value={formData.minAppVersionOfSupportOsAndModel}
                onChange={(e) => {
                  changeForm({minAppVersionOfSupportOsAndModel: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="支持Os、Model的namespace列表" {...getFormProps('namespaceNamesOfSupportOsAndModel')} help="全部请输入*">
              <Input
                value={formData.namespaceNamesOfSupportOsAndModel}
                onChange={(e) => {
                  changeForm({namespaceNamesOfSupportOsAndModel: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="最大灰度设备数(MassCircle)" {...getFormProps('maxGrayCntOfMassCircle')} help="最终取Min(diamond,此值)">
              <Input
                value={formData.maxGrayCntOfMassCircle}
                onChange={(e) => {
                  changeForm({maxGrayCntOfMassCircle: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="开启百分比灰度" {...getFormProps('openRatioGray')}>
              <Select
                value={formData.openRatioGray}
                onChange={(v) => {
                  changeForm({openRatioGray: v});
                }}
              >
                {SETTING_BOOLEAN_ARR.map((item) => {
                  return <Option key={"ratio_gray_" + item.value} value={toString(item.value)}>{item.label}</Option>;
                })}
              </Select>
            </FormItem>
          </Form>
        </Spin>
      </Modal>
    );
  }
}
