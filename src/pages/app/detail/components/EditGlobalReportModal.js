import React from 'react';
import {Checkbox, Form, Modal, Select, Spin} from '@alipay/bigfish/antd';

import {checkForm} from '@/utils/utils';
import {isSystemAdmin} from "@/utils/EnvUtils";

const CheckboxGroup = Checkbox.Group;
const {Option} = Select;

const FormItem = Form.Item;

const formLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 14,
  },
};

export default class EditGlobalReportModal extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    let {detailDO = {}} = this.props;
    let {globalReportConfig = {}, allReportTasks = []} = detailDO;
    let initForm = {
      allReportTasks: allReportTasks,
      enabledTasks: globalReportConfig.enabledTasks || [],
    }
    return {
      formData: initForm,
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        const {formData} = this.state;
        let {onHandleOk} = this.props;
        onHandleOk && onHandleOk(formData);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    this.setState(this.getInitState());
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);
    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'type': {
          if (!value) {
            help = '必选';
          }
          break;
        }

      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  componentWillReceiveProps(nextProps) {
    if (nextProps.detailDO && JSON.stringify(nextProps.detailDO) !== JSON.stringify(this.props.detailDO)) {
      let {detailDO = {}} = nextProps;
      let {globalReportConfig = {}, allReportTasks = []} = detailDO;
      this.changeForm({
        allReportTasks: allReportTasks,
        enabledTasks: globalReportConfig.enabledTasks || [],
      });
    }

  }


  render() {
    let {getFormProps, changeForm} = this;
    const {visible, formData, loading} = this.state;
    const isAdmin = isSystemAdmin();

    let {detailDO = {}} = this.props;
    let {globalReportConfig = {}, allReportTasks = []} = detailDO;

    const toString = function (key) {
      return '' + key;
    }
    return (
      <Modal title="修改全局报表配置" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}>
        <Spin spinning={loading}>
          <Form ref="form" layout="horizontal">
            <FormItem label="打开开报表任务" {...getFormProps('enabledTasks')}>
              <Checkbox.Group options={allReportTasks}
                              value={formData.enabledTasks}
                              onChange={(checkedValues) => {
                                changeForm({enabledTasks: checkedValues});
                              }}/>
            </FormItem>
          </Form>
        </Spin>
      </Modal>
    );
  }
}
