import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';

import {Breadcrumb, Col, Form, Icon, Row} from '@alipay/bigfish/antd';

import {CONFIG_KEY_DEFAULT, CONFIG_KEY_GLOBAL} from '@/constants/setting';

import {getFuncName} from '@/utils/utils';

import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {isSystemAdmin} from "@/utils/EnvUtils";
import {showBooleanValue} from "@/utils/LangUtils";
import styles from './index.less';

import namespace from '@/config/namespace';
import EditReportModal from './components/EditReportModal';
import EditPublishModal from "./components/EditPublishModal";
import EditGlobalReportModal from "./components/EditGlobalReportModal";
import EditGrayReportModal from "./components/EditGrayReportModal";
import EditExtModal from "./components/EditExtModal";
import EditPermissionModal from "./components/EditPermissionModal";


const FormItem = Form.Item;


const NAME_SPACE = namespace.app.detail;

class AppDetail extends React.Component {


  initPageData(matchData) {
    const {dispatch, location} = this.props;
    const appKey = (matchData && matchData.params && matchData.params.appKey) ? matchData.params.appKey : '';
    let payload = {params: {'appKey': appKey}};

    dispatch({
      type: getFuncName(NAME_SPACE, 'getDetail'),
      payload,
    });

  }

  componentWillMount() {
    const {dispatch, location, match} = this.props;
    this.initPageData(match);
  }


  componentWillReceiveProps(nextProps) {
    if (this.props != null && nextProps != null) {

      if (this.props.location != nextProps.location) {
        this.initPageData(nextProps.match);
      }

    }

  }

  _renderTips() {
  }

  _renderDetail(configKey, appBO) {
    if (configKey == CONFIG_KEY_DEFAULT) {
      return null;
    }
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    return (
      <div>
        <h3>
          基础信息

        </h3>
        <Form className={styles.form}>
          <Row gutter={24}></Row>
          <Row>
            <Col span={12}>
              <FormItem label="appKey">{appBO.appKey}  </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="appName">{appBO.appName}  </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="appId">{appBO.appId}
                <a href={UrlUtils.getMappcenterAppUrl(appBO.appId)} target="_blank"> 查看应用</a>
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="motuAppId">{appBO.motuAppId}  </FormItem>
            </Col>

          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="摩天轮Id">{appBO.mtlId} </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="包名">{appBO.appPackageName} </FormItem>
            </Col>

          </Row>
          <Row>
            <Col span={24}>
              <FormItem label="描述">{appBO.appDetail} </FormItem>
            </Col>
          </Row>
        </Form>
        <div style={{"margin": "10px 20px"}}><Link to={LinkUtils.getAppDetail(CONFIG_KEY_DEFAULT)}>查看全局配置</Link></div>
      </div>);

  }


  _renderReport(configKey, reportConfig, allReportTasks, reparams) {
    if (!reportConfig) {
      return null;
    }
    const that = this;
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    const onEditSubmit = (formData) => {
      let params = {
        configKey: configKey,
        configCode: (configKey == CONFIG_KEY_DEFAULT) ? 'REPORT_APP_DEFAULT_CODE' : 'REPORT_APP_CODE',
        statsDurationSeconds: formData.statsDurationSeconds || '',
        samplingRate: formData.samplingRate || '',
        baseAppStrategy: formData.baseAppStrategy || '',
        indexRateReportId: formData.indexRateReportId || '',
        configRateReportId: formData.configRateReportId || '',
        diffIndexReportId: formData.diffIndexReportId || '',
        configUpdateReportId: formData.configUpdateReportId || '',
        configUseReportId: formData.configUseReportId || '',
        dmInsightConfigUpdateReportId: formData.dmInsightConfigUpdateReportId || '',
        enabledTasks: (formData.enabledTasks || []).join(",")
      };
      let payload = {params: params, reparams: reparams};
      dispatch({
        type: getFuncName(NAME_SPACE, 'appConfigSetting'),
        payload,
      });

    };
    return (
      <div>
        <h3>
          {(configKey == CONFIG_KEY_DEFAULT) ? '默认报表配置' : '报表配置'}
          {isAdmin ? <Icon className="hf-ml" type="edit" onClick={() => {
            that.refs.editReportModal.show();
          }}
          ></Icon> : null}

        </h3>
        <Form className={styles.form}>
          <Row gutter={24}></Row>
          <Row>
            <Col span={6}>
              <FormItem label="统计周期(s)">{reportConfig.statsDurationSeconds || '-'}  </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label="更新采样率">{reportConfig.samplingRate || '-'}  </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="更新率基础策略">{reportConfig.baseAppStrategy || '-'}  </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <FormItem label="索引更新成功率报表ID">{reportConfig.indexRateReportId || '-'}  </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label="配置更新成功率报表ID">{reportConfig.configRateReportId || '-'}  </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label="差量索引报表ID">{reportConfig.diffIndexReportId || '-'}  </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label="更新报表ID">{reportConfig.configUpdateReportId || '-'}  </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <FormItem label="配置使用报表ID">{reportConfig.configUseReportId || '-'}  </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label="dmInsight配置更新报表ID">{reportConfig.dmInsightConfigUpdateReportId || '-'}  </FormItem>
            </Col>
          </Row>
          <Row>
              <Col span={24}>
                <FormItem className={styles.longBreak}
                          label="已打开任务">{reportConfig.enabledTasks ? reportConfig.enabledTasks.join(',') : '-'}</FormItem>
              </Col>
          </Row>
        </Form>
        <EditReportModal ref="editReportModal"
                         detailDO={reportConfig}
                         allReportTasks={allReportTasks}
                         onHandleOk={onEditSubmit}/>
      </div>);

  }


  _renderGrayConfig(configKey, grayConfig, reparams) {
    if (!grayConfig) {
      return null;
    }
    const that = this;
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    const onEditSubmit = (formData) => {
      let params = {
        configKey: configKey,
        configCode: "GRAY_APP_CODE",
        grayEffectUrlTemplate: formData.grayEffectUrlTemplate || '',
        minAppVersionOfSupportOsAndModel: formData.minAppVersionOfSupportOsAndModel || '',
        namespaceNamesOfSupportOsAndModel: formData.namespaceNamesOfSupportOsAndModel || '',
        maxGrayCntOfMassCircle: formData.maxGrayCntOfMassCircle || '',
        openRatioGray: formData.openRatioGray || ''
      };
      let payload = {params: params, reparams: reparams};
      dispatch({
        type: getFuncName(NAME_SPACE, 'appConfigSetting'),
        payload,
      });

    };
    return (
      <div>
        <h3>
          灰度配置
          {isAdmin ? <Icon className="hf-ml" type="edit" onClick={() => {
            that.refs.grayReportModal.show();
          }}
          ></Icon> : null}

        </h3>
        <Form className={styles.form}>
          <Row gutter={24}></Row>
          <Row>
            <Col span={24}>
              <FormItem label="灰度效果页url xxx"><pre>{grayConfig.grayEffectUrlTemplate || '-'}</pre></FormItem>
            </Col>

          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="支持Os、Model灰度的最小APP版本"><pre>{grayConfig.minAppVersionOfSupportOsAndModel || '-'}</pre></FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="支持Os、Model的namespace列表"><pre>{grayConfig.namespaceNamesOfSupportOsAndModel || '-'}</pre></FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="最大灰度设备数(MassCircle)"><pre>{grayConfig.maxGrayCntOfMassCircle || '-'}</pre></FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="开启百分比灰度"><pre>{showBooleanValue(grayConfig.openRatioGray) || '-'}</pre></FormItem>
            </Col>
          </Row>

        </Form>
        <EditGrayReportModal ref="grayReportModal"
                         detailDO={grayConfig}
                         onHandleOk={onEditSubmit}/>
      </div>);

  }

  _renderGlobalReport(configKey, globalReportConfig, allReportTasks, reparams) {
    const that = this;
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    const onEditSubmit = (formData) => {
      let params = {
        configKey: configKey,
        configCode: 'REPORT_GLOBAL_CODE',
        enabledTasks: (formData.enabledTasks || []).join(",")
      };
      let payload = {params: params, reparams: reparams};
      dispatch({
        type: getFuncName(NAME_SPACE, 'appConfigSetting'),
        payload,
      });

    };
    return (
      <div>
        <h3>
          全局报表配置
          {isAdmin ? <Icon className="hf-ml" type="edit" onClick={() => {
            that.refs.editGlobalReportModal.show();
          }}
          /> : null}
        </h3>
        <Form className={styles.form}>
          <Row gutter={24}/>
          <Row>
            <Col span={12}>
              <FormItem className={styles.longBreak}
                label="已打开任务">{globalReportConfig.enabledTasks ? globalReportConfig.enabledTasks.join(',') : '-'}</FormItem>
            </Col>
          </Row>
        </Form>
        <EditGlobalReportModal ref="editGlobalReportModal" detailDO={{globalReportConfig, allReportTasks}}
                               onHandleOk={onEditSubmit}/>
      </div>);

  }


  _renderPublish(configKey, publishConfig, reparams) {
    if (!publishConfig) {
      return null;
    }
    const that = this;
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    let configCode = 'PUBLISH_APP_CODE';
    let title = '发布配置';
    let modelName = 'editPublishModal';
    if (configKey == CONFIG_KEY_DEFAULT) {
      configCode = 'PUBLISH_APP_DEFAULT_CODE';
      title = '默认发布配置';
    } else if (configKey == CONFIG_KEY_GLOBAL) {
      configCode = 'PUBLISH_GLOBAL_CODE';
      title = '全局发布配置';
      modelName = 'editGlobalPublishModal';
    }
    const _onPublishSubmit = (formData, configKey, configCode) => {
      let params = {
        configKey: configKey,
        configCode: configCode,
        cdnHost: formData.cdnHost || '',
        openForceGrayStep: formData.openForceGrayStep || '',
        openDiffProbe: formData.openDiffProbe || '',
        diffProbeGapMinutes: formData.diffProbeGapMinutes || '',
        openChangeVersion: formData.openChangeVersion || '',
        openRollbackAny: formData.openRollbackAny || '',

      };
      //console.log('onsub', params)
      let payload = {params: params, reparams: reparams};
      dispatch({
        type: getFuncName(NAME_SPACE, 'appConfigSetting'),
        payload,
      });

    };
    return (
      <div>
        <h3>
          {title}
          {isAdmin ? <Icon className="hf-ml" type="edit" onClick={() => {
            that.refs[modelName].show(publishConfig, configKey, configCode);
          }}
          ></Icon> : null}

        </h3>
        <Form className={styles.form}>
          <Row gutter={24}></Row>
          <Row>
            <Col span={12}>
              <FormItem label="自定义CDN域名">{publishConfig.cdnHost || '-'}  </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="打开强制灰度(未使用)">{showBooleanValue(publishConfig.openForceGrayStep) || '-'}  </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="打开差量索引">{showBooleanValue(publishConfig.openDiffProbe) || '-'}  </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="差量索引间隔">{publishConfig.diffProbeGapMinutes || '-'}  </FormItem>
            </Col>
          </Row>

          <Row>
            <Col span={12}>
              <FormItem label="打开ChangeVersion">{showBooleanValue(publishConfig.openChangeVersion) || '-'}  </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="打开回滚到任一版本">{showBooleanValue(publishConfig.openRollbackAny) || '-'}  </FormItem>
            </Col>
          </Row>

        </Form>

        <EditPublishModal ref={modelName} onHandleOk={_onPublishSubmit}/>
      </div>);

  }



  _renderExt(configKey, extConfig, reparams) {
    if (!extConfig) {
      return null;
    }
    const that = this;
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    let configCode = 'EXT_APP_CODE';
    let title = '扩展配置';
    let modelName = 'editExtModal';
    const _onExtSubmit = (formData, configKey, configCode) => {
      let params = {
        configKey: configKey,
        configCode: configCode,
        massEnv: formData.massEnv || '',
        openWopCheck: formData.openWopCheck || '',
        closeCfStepCheck: formData.closeCfStepCheck || '',

      };
      //console.log('onsub', params)
      let payload = {params: params, reparams: reparams};
      dispatch({
        type: getFuncName(NAME_SPACE, 'appConfigSetting'),
        payload,
      });

    };
    return (
        <div>
          <h3>
            {title}
            {isAdmin ? <Icon className="hf-ml" type="edit" onClick={() => {
              that.refs[modelName].show(extConfig, configKey, configCode);
            }}
            ></Icon> : null}

          </h3>
          <Form className={styles.form}>
            <Row gutter={24}></Row>
            <Row>
              <Col span={8}>
                <FormItem label="Mass集群">{extConfig.massEnv || '-'}  </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="打开WOP卡口">{showBooleanValue(extConfig.openWopCheck) || '-'}  </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="关闭Changefree精细化管控">{showBooleanValue(extConfig.closeCfStepCheck) || '-'}  </FormItem>
              </Col>
            </Row>

          </Form>
          <EditExtModal ref={modelName} onHandleOk={_onExtSubmit}/>
        </div>);

  }



  _renderPermission(configKey, permConfig, reparams) {
    if (!permConfig) {
      return null;
    }
    const that = this;
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    let configCode = 'PERMISSION_APP_CODE';
    let title = '权限配置';
    let modelName = 'permissionExtModal';
    const _onExtSubmit = (formData, configKey, configCode) => {
      let params = {
        configKey: configKey,
        configCode: configCode,
        appAdmins: formData.appAdmins || '',
        gocAdmins: formData.gocAdmins || ''

      };
      //console.log('onsub', params)
      let payload = {params: params, reparams: reparams};
      dispatch({
        type: getFuncName(NAME_SPACE, 'appConfigSetting'),
        payload,
      });

    };
    return (
        <div>
          <h3>
            {title}
            {isAdmin ? <Icon className="hf-ml" type="edit" onClick={() => {
              that.refs[modelName].show(permConfig, configKey, configCode);
            }}
            ></Icon> : null}

          </h3>
          <Form className={styles.form}>
            <Row gutter={24}></Row>
            <Row>
              <Col span={12}>
                <FormItem label="APP负责人">{permConfig.appAdmins || '-'}  </FormItem>
              </Col>
              <Col span={2}>
                <FormItem label="安全生产负责人">{permConfig.gocAdmins || '-'}  </FormItem>
              </Col>
            </Row>

          </Form>
          <EditPermissionModal ref={modelName} onHandleOk={_onExtSubmit}/>
        </div>);

  }

  render() {
    let that = this;
    const {dispatch, pageData} = this.props;
    const {loading, detail} = pageData;
    const configKey = detail.configKey || CONFIG_KEY_DEFAULT;
    const appBO = detail.appBO || {};
    const allReportTasks = detail.allReportTasks || [];
    const reportConfig = detail.reportConfig && detail.reportConfig.configValueDO || {};
    const publishConfig = detail.publishConfig && detail.publishConfig.configValueDO || {};
    const grayConfig = detail.grayConfig && detail.grayConfig.configValueDO || {};
    const extConfig = detail.extConfig && detail.extConfig.configValueDO || {};
    const permissionConfig = detail.permissionConfig && detail.permissionConfig.configValueDO || {};

    const globalReportConfig = detail.globalReportConfig && detail.globalReportConfig.configValueDO || {};
    const globalPublishConfig = detail.globalPublishConfig && detail.globalPublishConfig.configValueDO || {};

    const reparams = {
      appKey: configKey
    }

    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>管理中心</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getAppList()}>App列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{configKey == CONFIG_KEY_DEFAULT ? '全局配置' : 'App详情'}</Breadcrumb.Item>
        </Breadcrumb>
        <div>
          {that._renderDetail(configKey, appBO)}
          {configKey == CONFIG_KEY_DEFAULT ? that._renderPublish(CONFIG_KEY_GLOBAL, globalPublishConfig, reparams) : null}
          {that._renderPublish(configKey, publishConfig, reparams)}
          {that._renderGrayConfig(configKey, grayConfig, reparams)}
          {that._renderExt(configKey, extConfig, reparams)}
          {that._renderPermission(configKey, permissionConfig, reparams)}

          {configKey == CONFIG_KEY_DEFAULT ? that._renderGlobalReport(CONFIG_KEY_GLOBAL, globalReportConfig, allReportTasks, reparams) : null}
          {that._renderReport(configKey, reportConfig, allReportTasks, reparams)}
        </div>


      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(AppDetail);
