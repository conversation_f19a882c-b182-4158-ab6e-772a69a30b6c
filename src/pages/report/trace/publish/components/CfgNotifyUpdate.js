import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {getFirstValues, toDateValues} from '@/utils/ReportUtils';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class CfgNotifyUpdate extends React.Component {
  getOption = (notifyNum, updateNum) => {
    const notifyData = getFirstValues(notifyNum);
    const updateData = getFirstValues(updateNum);
    const notifyEntries = toDateValues(notifyData);
    const updateEntries = toDateValues(updateData);
    return {
      title: {
        text: '更新数',
      },
      legend: {
        data: ['更新数'],
      },
      grid: {
        left: '7%',
        right: '10%',
        bottom: '10%',
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let tips = [params[0].value[0]];
          (params || [])
            .forEach(param => {
              let values = param.value;
              let tip = `${param.marker}${param.seriesName}: ${values[1]}`;
              tips.push(tip);
            });
          return tips.join('<br/>');
        },
      },
      xAxis: {
        type: 'time',
        axisLabel: {
          formatter: function (value) {
            return moment(value).format(dateFormat);
          },
        },
        splitLine: {
          lineStyle: {
            type: 'dotted',
          },
          show: true,
        },
      },
      yAxis: [
        {
          type: 'value',
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
            show: true,
          },
        },
      ],
      series: [
        // {
        //   name: '通知数',
        //   data: notifyEntries,
        //   type: 'line',
        //   symbol: 'circle',
        //   itemStyle: {
        //     color: '#4d6fb6',
        //   }
        // },
        {
          name: '更新数',
          data: updateEntries,
          type: 'line',
          symbol: 'circle',
          itemStyle: {
            color: '#57b3ee',
          }
        },
      ],
    };
  };

  render() {
    const {notifyNum, updateNum} = this.props;
    return (
      <div>
        <ReactEcharts option={this.getOption(notifyNum, updateNum)} lazyUpdate={true} notMerge={true}/>
      </div>
    );
  }
}
