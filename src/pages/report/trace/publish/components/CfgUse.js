import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {dateValuesToMap, getFirstValues, toDateValues} from '@/utils/ReportUtils';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class CfgUse extends React.Component {
  getOption = (nsUseNum, nsVersionUseNum, params) => {
    const nsVersionUseData = getFirstValues(nsVersionUseNum);
    const nsUseData = getFirstValues(nsUseNum);
    const nsCurrUseEntries = toDateValues(nsVersionUseData);
    const nsAllEntries = toDateValues(nsUseData);
    let currMap /*map[time, value]*/ = dateValuesToMap(nsCurrUseEntries);
    let allMap = dateValuesToMap(nsAllEntries);
    // let nsOtherEntries = Object.keys(allMap).map(time => {
    //   let otherNum = (parseInt(allMap[time]) || 0) - (parseInt(currMap[time]) || 0);
    //   otherNum = otherNum > 0 ? otherNum : 0;
    //   return {value: [time, otherNum]};
    // });
    const times = Object.keys({...currMap, ...allMap}).sort();
    const rates = times.map(time => {
      let v1 = parseInt(currMap[time]);
      let v = parseInt(allMap[time]);
      if (v1 > 0 && v > 0) {
        return {value: [time, (v1 * 100 / v).toFixed(2)]};
      }
      return {value: [time, -1]};
    }).filter(v => v.value[1] >= 0 && v.value[1] <= 100);
    return {
      title: {
        text: '使用占比',
      },
      grid: {
        left: '7%',
        right: '10%',
        bottom: '10%',
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let tips = [params[0].value[0]];
          (params || [])
            .forEach(param => {
              let values = param.value;
              let tip;
              if (param.seriesName === '当前版本占比') {
                tip = `${param.marker}${param.seriesName}: ${values[1]}%`;
              } else {
                tip = `${param.marker}${param.seriesName}: ${values[1]}`;
              }
              tips.push(tip);
            });
          return tips.join('<br/>');
        },
      },
      legend: {
        data: ['当前版本数', '当前版本占比'],
      },
      xAxis: {
        type: 'time',
        axisLabel: {
          formatter: function (value) {
            return moment(value).format(dateFormat);
          },
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
          show: true,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '版本使用数',
          axisLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
            show: true,
          },
        },
        {
          type: 'value',
          name: '当前版本占比',
          splitLine: {
            lineStyle: {
              type: 'dotted',
            },
            show: true,
          },
        },
      ],
      series: [
        {
          name: '当前版本数',
          type: 'line',
          barWidth: '30%',
          symbol: 'circle',
          data: nsCurrUseEntries,
          itemStyle: {
            color: '#4d6fb6',
          },
          lineStyle: {
            type: 'dashed'
          }
        },
        // {
        //   name: '其它版本数',
        //   type: 'line',
        //   barWidth: '30%',
        //   symbol: 'circle',
        //   data: nsOtherEntries,
        //   itemStyle: {
        //     color: '#57b3ee',
        //   },
        //   lineStyle: {
        //     type: 'dashed'
        //   }
        // },
        {
          name: '当前版本占比',
          yAxisIndex: 1,
          data: rates,
          type: 'line',
          symbol: 'circle',
          itemStyle: {
            color: '#D74B4B'
          }
        },
      ],
    };
  };

  render() {
    const {nsUseNum, nsVersionUseNum, params} = this.props;
    return (
      <div>
        <ReactEcharts option={this.getOption(nsUseNum, nsVersionUseNum, params)} lazyUpdate={true} notMerge={true}/>
      </div>
    );
  }
}
