import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {dateValuesToMap, getFirstValues} from '@/utils/ReportUtils';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class CfgAccNotifyUpdate extends React.Component {
  getOption = (notifyNum, updateNum) => {
    const notifyData = getFirstValues(notifyNum);
    const updateData = getFirstValues(updateNum);
    let accNotify = 0;
    const notifyEntries = Object.entries(notifyData || {}).map(v => {
      let dateStr = moment(parseInt(v[0])).format(dateFormat);
      // accNotify += parseInt(v[1]);
      return {value: [dateStr, parseInt(v[1])]};
    });
    let accUpdate = 0;
    const updateEntries = Object.entries(updateData || {}).map(v => {
      let dateStr = moment(parseInt(v[0])).format(dateFormat);
      // accUpdate += parseInt(v[1]);
      return {value: [dateStr, parseInt(v[1])]};
    });
    let notifyMap = dateValuesToMap(notifyEntries);
    let updateMap = dateValuesToMap(updateEntries);
    let lastAccNotify, lastAccUpdate;
    const rates = Object.keys({...notifyMap, updateMap})
      .sort()
      .map(time => {
        lastAccNotify = parseInt(notifyMap[time]) || lastAccNotify;
        lastAccUpdate = parseInt(updateMap[time]) || lastAccUpdate;
        let rate = 0;
        if (lastAccUpdate >= 0 && lastAccNotify > 0) {
          rate = (lastAccUpdate * 100 / lastAccNotify).toFixed(2);
        }
        return {value: [time, rate]};
      });
    return {
      title: {
        text: '更新数',
      },
      grid: {
        left: '7%',
        right: '10%',
        bottom: '10%',
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let tips = [params[0].value[0]];
          (params || [])
            .forEach(param => {
              let values = param.value;
              let tip;
              if (param.seriesName === '更新通知占比') {
                tip = `${param.marker}${param.seriesName}: ${values[1]}%`;
              } else {
                tip = `${param.marker}${param.seriesName}: ${values[1]}`;
              }
              tips.push(tip);
            });
          return tips.join('<br/>');
        },
      },
      legend: {
        data: ['更新数'],
      },
      xAxis: {
        type: 'time',
        axisLabel: {
          formatter: function (value) {
            return moment(value).format(dateFormat);
          },
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
          show: true,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '更新数',
          axisLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dotted',
            },
            show: true,
          },
        },
        // {
        //   type: 'value',
        //   name: '更新通知占比',
        //   splitLine: {
        //     lineStyle: {
        //       type: 'dashed',
        //     },
        //     show: true,
        //   },
        // },
      ],
      series: [
        // {
        //   name: '通知数',
        //   data: notifyEntries,
        //   type: 'line',
        //   symbol: 'circle',
        //   itemStyle: {
        //     color: '#4d6fb6',
        //   },
        //   lineStyle: {
        //     type: 'dashed'
        //   }
        // },
        {
          name: '更新数',
          data: updateEntries,
          type: 'line',
          symbol: 'circle',
          itemStyle: {
            color: '#57b3ee',
          },
          lineStyle: {
            type: 'dashed'
          }
        },
        // {
        //   name: '更新通知占比',
        //   type: 'line',
        //   symbol: 'circle',
        //   barWidth: '30%',
        //   yAxisIndex: 1,
        //   data: rates,
        //   itemStyle: {
        //     color: '#D74B4B'
        //   }
        // },
      ],
    };
  };

  render() {
    const {notifyNum, updateNum} = this.props;
    return (
      <div>
        <ReactEcharts option={this.getOption(notifyNum, updateNum)} lazyUpdate={true} notMerge={true}/>
      </div>
    );
  }
}
