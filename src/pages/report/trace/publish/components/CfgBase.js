import React from 'react';
import {Card, Col, Icon, Row, Statistic, Tooltip} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';
import baseStyles from '@/styles/base.less';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";

export default class CfgBase extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      viewType: 'publish',
    };
  }

  timeToString(seconds) {
    let str = '';
    let curr = seconds;
    if (curr > 0) {
      if (curr % 60 > 0) {
        str = (curr % 60) + '秒' + str;
      }
      curr = (curr / 60).toFixed();
    }
    if (curr > 0) {
      if (curr % 60 > 0) {
        str = (curr % 60) + '分' + str;
      }
      curr = Math.floor(curr / 60);
    }
    if (curr > 0) {
      if (curr % 24 > 0) {
        str = (curr % 24) + '小时' + str;
      }
      curr = Math.floor(curr / 24);
    }
    if (curr > 0) {
      str = curr + '天' + str;
    }
    return str;
  }

  render() {
    const {versionDetail, namespace, app, params} = this.props;
    let versionStatusTitle;
    switch (versionDetail.status) {
      case 200:
        versionStatusTitle = '已发布';
        break;
      case 255 :
        versionStatusTitle = '已删除';
        break;
      default:
        versionStatusTitle = '未发布';
    }
    return (
      <div style={{lineHeight: 4}}>
        <div className={baseStyles.holder}>
          <Row>
            <Col span={8}>
              appKey:&nbsp;{app.appName}-({app.appKey} - {versionDetail.appVersion})
            </Col>
            <Col span={8}>namespace:&nbsp;{versionDetail.name}&nbsp;
              <Link to={LinkUtils.getNamespaceDetail(versionDetail.namespaceId, null)}>配置详情</Link>&nbsp;
              <Link to={LinkUtils.getNamespaceReport(versionDetail.namespaceId)}><Icon type="line-chart"/></Link>
            </Col>
            <Col span={8}>version:&nbsp;{versionDetail.version}</Col>
          </Row>
          <Row>
            <Col span={8}>strategy:&nbsp;{versionDetail.strategy}</Col>
            <Col span={8}>创建时间:&nbsp;{versionDetail.gmtCreateTime}</Col>
            <Col span={8}>开始生效时间:&nbsp;{versionDetail.gmtOnlineTime}</Col>
          </Row>
          <Row>
            <Col span={8}>加载级别:{ConvertUtils.getLoadLevelName(namespace.loadLevel)}</Col>
            <Col span={9}><Icon type="info-circle"/><a
              href={UrlUtils.getHelpUrl("report")} target="_blank">&nbsp;帮助文档</a></Col>
            <Col span={8}/>
          </Row>
        </div>
      </div>
    );
  }
}
