import React from 'react';
import {connect} from 'dva';
import {getFuncName} from '@/utils/utils';
import {Link} from 'dva/router';
import namespace from '@/config/namespace';
import QueryForm from './components/QueryForm';
import CfgUse from './components/CfgUse';
import CfgBase from './components/CfgBase';
import CfgNotifyUpdate from './components/CfgNotifyUpdate';
import CfgAccNotifyUpdate from './components/CfgAccNotifyUpdate';
import baseStyles from '@/styles/base.less';
import {LinkUtils} from '@/utils/LinkUtils';
import {Breadcrumb, Col, Row} from '@alipay/bigfish/antd';

const NAME_SPACE = namespace.report.trace;

class PublishReport extends React.Component {
  constructor(props) {
    super(props);
  }

  componentWillMount() {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }

  onQuery = params => {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'queryData'),
      payload: {params},
    });
  };

  render() {
    const {
      params,
      app,
      namespace,
      versionDetail,
      dims,
      cfgUpdateRateView,
      notifyNum,
      updateNum,
      nsUseNum,
      nsVersionUseNum,
    } = this.props.data;
    const viewType = params.viewType || 'publish';
    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>数据度量</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getVersionList()}>发布列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getVersionDetail(versionDetail.namespaceId, versionDetail.version)}>
              发布单详情
            </Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>发布跟踪</Breadcrumb.Item>
        </Breadcrumb>
        <div>
          <CfgBase
            versionDetail={versionDetail}
            cfgUpdateRateView={cfgUpdateRateView}
            app={app}
            namespace={namespace}
            params={params}
          />
        </div>
        <p/>
        <div>
          <QueryForm params={params} versionDetail={versionDetail} onQuery={this.onQuery} dims={dims}/>
        </div>
        <div>
          {viewType === 'range'
            ? this.getRangeView(notifyNum, updateNum, nsUseNum, nsVersionUseNum, params)
            : this.getPublishView(notifyNum, updateNum, nsUseNum, nsVersionUseNum, params)}
        </div>
      </div>
    );
  }

  getPublishView(notifyNum, updateNum, nsUseNum, nsVersionUseNum, params) {
    return (
      <div>
        <div style={{marginTop: '20px'}} className={baseStyles.holder}>
          <Row>
            <Col span={24}>
              <CfgAccNotifyUpdate notifyNum={notifyNum} updateNum={updateNum}/>
            </Col>
          </Row>
        </div>
        <div style={{marginTop: '20px'}} className={baseStyles.holder}>
          <Row>
            <Col span={24}>
              <CfgUse nsUseNum={nsUseNum} nsVersionUseNum={nsVersionUseNum} params={params}/>
            </Col>
          </Row>
        </div>
      </div>
    );
  }

  getRangeView(notifyNum, updateNum, nsUseNum, nsVersionUseNum, params) {
    return (
      <div>
        <div style={{marginTop: '20px'}} className={baseStyles.holder}>
          <Row>
            <Col span={24}>
              <CfgNotifyUpdate notifyNum={notifyNum} updateNum={updateNum} params={params}/>
            </Col>
          </Row>
        </div>
        <div style={{marginTop: '20px'}} className={baseStyles.holder}>
          <Row>
            <Col span={24}>
              <CfgUse nsUseNum={nsUseNum} nsVersionUseNum={nsVersionUseNum} params={params}/>
            </Col>
          </Row>
        </div>
      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    data: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(PublishReport);
