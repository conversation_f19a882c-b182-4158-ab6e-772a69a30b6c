import React from 'react';
import {connect} from 'dva';
import moment from 'moment';
import {getFuncName} from '@/utils/utils';
import {Link} from 'dva/router';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from '@/utils/ConvertUtils';
import StatusSpan from '@/components/StatusSpan';
import namespace from '@/config/namespace';
import CfgVersionsRate from './components/CfgVersionsRate';
import QueryForm from './components/QueryForm';
import {Breadcrumb, Col, Icon, Radio, Row, Table, Tooltip} from '@alipay/bigfish/antd';
import {getFirstValues} from '@/utils/ReportUtils';
import baseStyles from '@/styles/base.less';
import CfgVersionsNum from "./components/CfgVersionsNum";

const NAME_SPACE = namespace.report.namespace;

class NamespaceReport extends React.Component {
  constructor(props) {
    super(props);
  }

  componentWillMount() {
    this.setState({viewType: 'rateView'});
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }

  onQuery = params => {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'queryVersionsData'),
      payload: {params},
    });
  };

  changeView = e => {
    let viewType = e.target.value;
    this.setState({viewType: viewType});
  };

  getColumns({chartData, visibleVersions, versionsSum, timeUVTotal, totalSum}) {
    return [
      {
        title: '别名',
        dataIndex: 'alias',
        key: 'alias',
      },
      {
        title: 'version',
        dataIndex: 'version',
      },
      {
        title: 'appVersion',
        dataIndex: 'appVersion',
      },
      {
        title: '策略',
        dataIndex: 'strategy',
      },
      {
        title: (
          <div>
            使用汇总
            <Tooltip title={'为当前时间窗口使用的累加值，非绝对使用数，设备累加是有重复的'} className="help">
              <span><Icon type="question-circle"/></span>
            </Tooltip>
          </div>
        ),
        dataIndex: 'usedNum',
        render(text, record) {
          return versionsSum[record.alias] || '';
        },
      },
      {
        title: (<div>
          累加占比
          <Tooltip title={'为当前时间窗口使用的累加值/当前时间窗口全部的累加值，设备累加是有重复的'} className="help">
            <span><Icon type="question-circle"/></span>
          </Tooltip>
        </div>),
        dataIndex: 'usedRate',
        render(text, record) {
          return totalSum > 0 ? ((versionsSum[record.alias] || 0) * 100 / totalSum).toFixed(2) + '%' : '';
        },
      },
      {
        title: '发布时间',
        dataIndex: 'publish_time',
      },
      {
        title: '状态',
        dataIndex: 'status',
        render(text, record) {
          let title = record.isAvailable === 'y' ? '生效中' : '失效中';
          let type = record.isAvailable === 'y' ? 'success' : 'fail';
          return text ? <StatusSpan title={title} type={type}/> : null;
        },
      },
      {
        title: '操作',
        render(text, record) {
          return (
            <div>
              <Link to={LinkUtils.getVersionDetail(record.namespaceId, record.version)}>版本详情</Link>
              &nbsp;|&nbsp;
              <Link to={LinkUtils.getPublishTrace(record.namespaceId, record.version)}>发布跟踪</Link>
            </div>
          );
        },
      },
    ];
  }

  showTables({chartData, visibleVersions, versionsSum, timeUVTotal, totalSum}) {
    return (
      <div className={baseStyles.holder} style={{margin: '20px 5px 20px 5px'}}>
        <h4>版本列表</h4>
        <Row>
          <Col span={24}>
            <Table
              dataSource={visibleVersions}
              columns={this.getColumns({chartData, visibleVersions, versionsSum, timeUVTotal, totalSum})}
              size="small"
              pagination={{pageSize: 10}}
            />
          </Col>
        </Row>
      </div>
    );
  }

  simple(versions, nsVersionsUseNum) {
    let i = 0;
    let aliasM = versions.reduce((aliasM, v) => {
      let alias = `v${moment(v.gmtCreate).format('YYYYMMDD_HHmmss')}_${i++}`;
      v.alias = alias;
      aliasM[v.version] = alias;
      return aliasM;
    }, {});
    const chartData = (nsVersionsUseNum.data || []).map(dimData => {
      let version = dimData.dpDataDimValueMap.configVersion;
      return {
        alias: aliasM[version],
        version: version,
        data: dimData.values[0].values, /* map[time, value] */
      };
    });
    return {versions, chartData};
  }

  oldAggregation(params, nsUseNum, versions, chartData) {
    let versionsSum = {}; //当前版本总个数
    let timeUVTotal = getFirstValues(nsUseNum);
    let timeSum = {}; //当前时间下总个数
    chartData.forEach((cd) => {
      cd.data = cd.data || [];
      versionsSum[cd.alias] = Object.values(cd.data || []).reduce((acc, c) => acc + (parseInt(c) || 0), 0);
      // Object.entries(cd.data).forEach(([key, value]) => timeSum[key] = (timeSum[key] || 0) + (parseInt(value) || 0));
    });

    const startTime = params.startTime;
    let inValidCount = 1;
    //所有有效的，开始时间一周内无效的或加上一周内所有无效不会超过5
    const newFilter = (version) => {
      if (!version) return false;
      if (!version.valid) {
        if (version.gmtCreate - startTime < 7 * 24 * 3600 * 1000 && inValidCount < 8) {
          inValidCount++;
          return true;
        }
        if (versionsSum[version.alias] > 0 && inValidCount < 5) {
          inValidCount++;
          return true;
        }
      }
      return version.valid;
    };

    inValidCount = 0;
    let visibleVersions = versions.filter(newFilter);
    visibleVersions.sort((a, b) => b.gmtCreate - a.gmtCreate);
    // if (versions.length > visibleVersions.length) {
    //   visibleVersions.push({alias: 'other'});
    // }

    inValidCount = 0;
    let visibleAlias = new Set(visibleVersions.map(v => v.alias));
    chartData = chartData.filter((cd) => visibleAlias.has(cd.alias)); //和表格对展示的对齐
    let chartRateData = chartData.map((cd) => {
      let data = cd.data || {};
      data = {...data};
      Object.keys(data).forEach((key) => {
        let num = parseInt(data[key]);
        // let sum = parseInt(timeSum[key]);
        let total = parseInt(timeUVTotal[key]);
        let rate = 0;
        if (num && total) {
          rate = num * 100 / total;
          if (rate > 100) {
            rate = 100;
          }
        }
        data[key] = rate.toFixed(2);
      });
      return {...cd, data};
    });
    let totalSum = Object.values(timeUVTotal).reduce((acc, c) => acc + (0 || parseInt(c)), 0);
    // let totalSum = Object.values(versionsSum).reduce((acc, c) => acc + (0 || parseInt(c)), 0);
    // versionsSum['other'] = Object.entries(versionsSum)
    //   .filter(([key, value]) => !visibleAlias.has(key))
    //   .reduce((acc, [key, value]) => acc + (parseInt(value) || 0), 0);
    return {chartData, chartRateData, visibleVersions, versionsSum, timeUVTotal, totalSum};
  }

  render() {
    const data = this.props.data;
    const {app, namespace, nsUseNum, nsVersionsUseNum, params, dims} = data;
    let {versions, chartData} = this.simple(data.versions, nsVersionsUseNum);
    const chartTableData = this.oldAggregation(params, nsUseNum, versions, chartData);
    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>
            <Link to={LinkUtils.getMyNamespaceList()}>配置列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getNamespaceDetail(namespace.namespaceId, null)}>配置详情</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>数据度量</Breadcrumb.Item>
        </Breadcrumb>

        <div className={baseStyles.holder} style={{lineHeight: 2, margin: '20px 5px'}}>
          <h4>基础信息</h4>

          <Row>
            <Col span={8}>
              appKey:&nbsp;{app.appName}-({app.appKey})
            </Col>
            <Col span={7}>namespace:&nbsp;{namespace.name}&nbsp;<Link
              to={LinkUtils.getNamespaceDetail(namespace.namespaceId, null)}>配置详情</Link>
            </Col>
            <Col span={3}>类型:&nbsp;{ConvertUtils.getNsTypeName(namespace.type)}</Col>
            <Col span={3}>加载级别:{ConvertUtils.getLoadLevelName(namespace.loadLevel)}</Col>
            <Col span={3}><Icon type="info-circle"/><a
              href={UrlUtils.getHelpUrl("report")} target="_blank">&nbsp;帮助文档</a></Col>
          </Row>
        </div>
        <div className={baseStyles.holder} style={{margin: '20px 5px'}}>
          <QueryForm params={params} namespace={namespace} dims={dims} onQuery={this.onQuery}/>
        </div>
        <div className={baseStyles.holder}>
          <Row>
            <Col>
              <Radio.Group onChange={this.changeView} defaultValue='rateView' buttonStyle="solid">
                <Radio.Button value="rateView">使用占比视图</Radio.Button>
                <Radio.Button value="numView">使用数视图</Radio.Button>
              </Radio.Group>
            </Col>
          </Row>
          <Row><Col>&nbsp;</Col></Row>
          <Row>
            <Col span={24}>
              {this.state.viewType === 'numView' ? <CfgVersionsNum chartTableData={chartTableData}/> :
                <CfgVersionsRate chartTableData={chartTableData}/>}
            </Col>
          </Row>
        </div>
        {this.showTables(chartTableData)}
      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    data: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(NamespaceReport);
