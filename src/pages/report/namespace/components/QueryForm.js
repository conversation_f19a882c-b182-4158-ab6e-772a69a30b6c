import React from 'react';
import {Button, DatePicker, Form, Select} from '@alipay/bigfish/antd';
import {VersionUtils} from '@/utils/VersionUtils';
import moment from "moment";

const {Option} = Select;
const FormItem = Form.Item;

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

class QueryForm extends React.Component {
  constructor(props) {
    super(props);
  }

  handleSubmit = e => {
    e.preventDefault();
    this.doSubmit({});
  };

  doSubmit(changeParams) {
    let {onQuery, params} = this.props;
    this.props.form.validateFields((err, formData) => {
      if (!err) {
        let {
          viewType, startTime, endTime, osVersion, appVersion, brand, deviceModel, country,
        } = formData;
        let dimValues = [{
          "dimName": "osVersion",
          "dimValues": osVersion ? [osVersion] : []
        }, {
          "dimName": "appVersion",
          "dimValues": appVersion ? [appVersion] : []
        }, {
          "dimName": "brand",
          "dimValues": brand ? [brand] : []
        }, {
          "dimName": "deviceModel",
          "dimValues": deviceModel ? [deviceModel] : []
        }, {
          "dimName": "country",
          "dimValues": country ? [country] : []
        }];
        let {dataQuery} = params;
        dimValues = dimValues.concat(
          dataQuery.dimValues.filter(
            dv => dv.dimName === 'configName' || dv.dimName === 'configVersion',
          ),
        );
        let queryParams = {
          ...params,
          viewType,
          ...changeParams,
          startTime: startTime.toDate().getTime(),
          endTime: endTime.toDate().getTime(),
          dataQuery: {
            ...params.dataQuery,
            startTime: startTime.toDate().getTime(),
            endTime: endTime.toDate().getTime(),
            dimValues,
          },
        };
        onQuery && onQuery(queryParams);
      }
    });
  }

  getDimValues(dimsMap, dimName) {
    let dim = dimsMap[dimName];
    if (dim) {
      return VersionUtils.sortXXVersions(dim.dimValues).reverse();
    }
    return [];
  }

  render() {
    const {params, dims} = this.props;
    const {getFieldDecorator} = this.props.form;
    const dimsData = dims.data || {};
    const dimValues = dimsData.values || [];
    const dimsMap = dimValues.reduce((map, v) => {
      map[v.dimName] = v;
      return map;
    }, {});
    const startTime = moment(params.startTime);
    let endTime = moment(params.endTime || Date.now() - 120 * 1000);
    if (endTime.isBefore(startTime)) {
      endTime = moment();
    }
    return (
      <div>
        <Form layout="inline" className="clearfix" onSubmit={this.handleSubmit}>
          <FormItem label="系统版本">
            {getFieldDecorator('osVersion', {
              initialValue: '',
            })(
              <Select style={{width: 150}} showSearch>
                <Option value="">所有</Option>
                {this.getDimValues(dimsMap, 'osVersion').map(v => {
                  return (
                    <Option key={v} value={v}>
                      {v}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem label="客户端版本">
            {getFieldDecorator('appVersion', {
              initialValue: '',
            })(
              <Select style={{width: 150}} showSearch>
                <Option value="">所有</Option>
                {this.getDimValues(dimsMap, 'appVersion').map(v => {
                  return (
                    <Option key={v} value={v}>
                      {v}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem label="厂商">
            {getFieldDecorator('brand', {
              initialValue: '',
            })(
              <Select style={{width: 150}} showSearch>
                <Option value="">所有</Option>
                {this.getDimValues(dimsMap, 'brand').map(v => {
                  return (
                    <Option key={v} value={v}>
                      {v}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem label="机型">
            {getFieldDecorator('deviceModel', {
              initialValue: '',
            })(
              <Select style={{width: 150}} showSearch>
                <Option value="">所有</Option>
                {this.getDimValues(dimsMap, 'deviceModel').map(v => {
                  return (
                    <Option key={v} value={v}>
                      {v}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <FormItem label="国家">
            {getFieldDecorator('country', {
              initialValue: '',
            })(
              <Select style={{width: 150}} showSearch>
                <Option value="">所有</Option>
                {this.getDimValues(dimsMap, 'country').map(v => {
                  return (
                    <Option key={v} value={v}>
                      {v}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </FormItem>
          <p/>
          <FormItem>
            {getFieldDecorator('startTime', {
              initialValue: startTime,
              rules: [{required: true, message: '请选择开始时间'}],
            })(
              <DatePicker
                style={{width: 200}}
                disabled={params.viewType === 'publish'}
                showTime
                format={dateFormat}
                placeholder="Start"
              />,
            )}
            ~
            {getFieldDecorator('endTime', {
              initialValue: endTime,
              rules: [{required: true, message: '请选择结束时间'}],
            })(
              <DatePicker
                style={{width: 200}}
                disabled={params.viewType === 'publish'}
                showTime
                format={dateFormat}
                placeholder="End"
              />,
            )}
          </FormItem>
          <FormItem>
            <Button className="st-mr" type="primary" htmlType="submit">
              查询
            </Button>
          </FormItem>
        </Form>
      </div>
    );
  }
}

export default Form.create()(QueryForm);
