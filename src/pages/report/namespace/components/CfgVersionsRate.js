import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {toDateValues} from '@/utils/ReportUtils';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class CfgVersionsRate extends React.PureComponent {
  getOption = ({chartData, chartRateData, visibleVersions, versionsSum, timeUVTotal, totalSum}) => {
    let legend = chartRateData.map(v => v.alias);
    let series = chartRateData.map((o, i) => {
      let data = toDateValues(o.data);
      return {
        name: o.alias,
        type: 'line',
        symbol: 'circle',
        data: data,
      };
    });
    return {
      title: {
        text: '配置使用占比',
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let t0 = params[0];
          let tips = [t0.value[0]];
          tips.push('当前使用设备占比');
          (params || [])
            .sort((a, b) => -a.seriesName.localeCompare(b.seriesName))
            .forEach(param => {
              let values = param.value;
              let tip = `${param.marker}${param.seriesName}: ${values[1]}%`;
              tips.push(tip);
            });
          return tips.join('<br/>');
        },
      },
      legend: {
        data: legend,
        show: legend.length < 5
      },
      grid: {
        left: '7%',
        right: '10%',
        bottom: '10%',
        show: false,
      },
      xAxis: {
        type: 'time',
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
          show: true,
        },
        axisLabel: {
          formatter: function (value) {
            return moment(value).format(dateFormat);
          },
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '使用占比',
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
            show: true,
          },
        },
      ],
      series: series,
    };
  };

  render() {
    const {chartTableData} = this.props;
    return (
      <div>
        <ReactEcharts option={this.getOption(chartTableData)} lazyUpdate={true} notMerge={true}/>
      </div>
    );
  }
}
