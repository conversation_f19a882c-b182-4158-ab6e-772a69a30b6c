import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {getFirstValues, toDateValues} from '@/utils/ReportUtils';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class DcFailPiePanel extends React.Component {
  getOption = (legend, data) => {
   return  {
     title: {
       text: 'DC异常分布',
         left: 'center'
     },
      tooltip: {
        trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
          right: 10,
          top: 40,
          data: legend
      },
      series: [
        {
          name: '访问来源',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '30',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    }
  }

  render() {
    //const {notifyNum, updateNum} = this.props;
    const currentDcDash = this.props.currentDcDash || {};
    const data = [];
    const legend = [];
    const errorList = currentDcDash.errorCodeDetailList || [];
    errorList.forEach(function (t, i,array ){
      if (t.name !== "成功") {
        data.push(t);
        legend.push(t.name);
      }
    })
    return (
      <div>
        <ReactEcharts   option={this.getOption(legend, data)}/>
      </div>
    );
  }
}
