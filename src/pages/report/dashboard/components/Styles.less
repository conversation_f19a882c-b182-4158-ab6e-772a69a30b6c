
.holder {
  margin-bottom: 15px;
  background: #fff;


}
.form{
  margin-bottom: 15px;
  .typeSelector {
    width: 100px;
  }

  .typeSelectormax {
    width: 300px;
  }
}

.search {
  width: 200px;
  display: inline-block;
}

.rcornersBlack {
  border: 1px solid #bbb;
  border-radius: 20%;
  background: #bbb;
  padding: 3px;
  margin: 10px;
  color: #fff;
}

.rcornersWhite {
  border: 1px solid #bbb;
  border-radius: 20%;
  background: #fff;
  padding: 3px;
  margin: 10px;
}

.metrix {

  margin: 20px 20px 0px 20px;
  min-width: 200px;
  min-height: 100px;
  .title {
    font-size: 14px;
    line-height: 20px;
    color: #73777a;
    margin: 20px 10px 4px 20px;
  }


  .score {
    font-size: 30px;
    line-height: 40px;
    color: rgba(0, 0, 0, 0.65);
    margin: 20px 10px 4px 30px;
    .suffix {
      font-size: 20px;
      line-height: 20px;
      color: #73777a;
      margin-left: 4px;

    }
  }


}

.split{
  border-top:1px dashed #cccccc;
  height: 1px;
  overflow:hidden
}

.txtEllipsis{
  width:200px;
  max-width:200px;
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
  line-height: 24px;
}
