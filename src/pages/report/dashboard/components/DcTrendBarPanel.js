import React from 'react';
import ReactEcharts from 'echarts-for-react';

import moment from 'moment';
import {getFirstValues, toDateValues} from '@/utils/ReportUtils';
import {ConvertUtils} from '@/utils/ConvertUtils';


const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class DcTrendBarPanel extends React.Component {
  getOption = (xAxis, legend, series) => {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {            // 坐标轴指示器，坐标轴触发有效
          type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
        }
      },
      //color: ['#4d6fb6', '#57b3ee', '#a9d8f5', '#c8e7f9', '#eaf6fc', '#f2f2f4'],
      color: [ '#f2f2f4', '#eaf6fc', '#c8e7f9', '#a9d8f5', '#57b3ee','#4d6fb6'],

      legend: {
        data: legend,
        show: true,
        orient: 'horizontal',
        top: 5,
        right: 15,
        textStyle: {
          fontSize: 14,
          fontWeight: 100,
          color: '#bbb',
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisTick: {
          show: true,
          length: 8,
          lineStyle: {
            width: 1,
            color: '#dedede',
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          // formatter: lab => (`${lab}:00`),
        },
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisTick: {show: false},
        axisLine: {show: false},
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: lab => (`${lab}次`),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#dedede',
            width: 1,
            type: 'dashed',
          },
        },
      },
      series: series
    }
  }

  render() {
    const dcHHTrend = this.props.dcHHTrend || [];
    function unique (arr) {
      return Array.from(new Set(arr))
    }
    let trendData = dcHHTrend.filter((data) => {
      return !(data.name === null || data.name === "");
    });
    let legend = unique(trendData.map((data) => {
      return data.name + '|' + data.code;
    }));
    let xAxis = unique(trendData.map((data) => {
      return data.ds;
    }));
    let series = [];
    legend.forEach(function (item, index, array) {
      let s = {};
      s.name = item;
      s.type = 'bar';
      s.stack = 'api';
      s.barWidth = 5;
      s.data = trendData.filter((data) => {
        return data.name + '|' + data.code === item;
      }).map((data) => {
        return data.data;
      });
      series.push(s);
    })

    let newXAxis = [];
    function transLanguage(input) {
      let strArray = (input || "").split("|");
      let output = "";
      output += ConvertUtils.getReportServletName(strArray[0]);
      if (strArray.length == 2) {
        output += ConvertUtils.getReportDCFailName(strArray[1]);
      }

      return output;
    }

    if ((xAxis[0] + "").length > 8) {
      xAxis.forEach(function (item, index, array) {
        newXAxis.push((item + "").substring(8) + ":00");
      });
    } else {
      xAxis.forEach(function (item, index, array) {
        newXAxis.push((item + "").substring(4, 6) + "-" + (item + "").substring(6));
      });
    }
    let newSeries = [];
    series.forEach(function (item, index, array){
      let newItem = JSON.parse(JSON.stringify(item));
      newItem["barWidth"] = 12;
      newItem.name = transLanguage(item.name);
      newSeries.push(newItem);
    })
    let newLegend = [];
    legend.forEach(function (item, index, array) {
      newLegend.push(transLanguage(item));
    })
    //const {notifyNum, updateNum} = this.props;
    return (
      <div>
        <ReactEcharts option={this.getOption(newXAxis, newLegend, newSeries)}/>
      </div>
    );
  }
}
