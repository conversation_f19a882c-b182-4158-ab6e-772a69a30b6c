import React from 'react';
import baseStyles from '@/styles/base.less';
import {Tabs} from "@alipay/bigfish/antd";
import ConfigReqTrendLinePanel from "./req/ConfigReqTrendLinePanel";
import IndexReqTrendLinePanel from "./req/IndexReqTrendLinePanel";
import OrangeCrashTrendLinePanel from "./crash/OrangeCrashTrendLinePanel";
import BizCrashTrendLinePanel from "./crash/BizCrashTrendLinePanel";
import DashboardMetrixColPanel from "../dashboard/DashboardMetrixColPanel";


const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class ConfigExceptionPanel extends React.Component {
  constructor(props) {
    super(props);
    this.state = {rateView: 'configUpdateRateView'};
    this.panelDivRef = React.createRef();
  }

  changeView = (e) => this.setState({rateView: e.target.value});

  getDefaultValue(obj, name) {
    return obj ? obj[name] : 0;
  }

  componentDidMount() {
    let elem = this.panelDivRef.current;
    this.panelDivWidth = elem.clientWidth;
  }

  render() {
    const {rateView} = this.state;
    const {compareAppDash, currentAppDash, appHHTrend, params} = this.props;
    const {
      bizCrashFields: compareBizCrashFields,
      orangeCrashFields: compareOrangeCrashFields,
      indexReqFields: compareIndexReqFields,
      configReqFields: compareConfigReqFields
    } = compareAppDash || {};
    const {
      bizCrashFields: currentBizCrashFields,
      orangeCrashFields: currentOrangeCrashFields,
      indexReqFields: currentIndexReqFields,
      configReqFields: currentConfigReqFields
    } = currentAppDash || {};
    let baseIndicators = [{
      key: 'indexReq',
      title: '索引请求成功率',
      suffix: '%',
      current: this.getDefaultValue(currentIndexReqFields, 'successRate') * 100.0,
      compared: this.getDefaultValue(compareIndexReqFields, 'successRate') * 100.0,
      tabPane: <IndexReqTrendLinePanel appHHTrend={appHHTrend} params={params}/>
    }, {
      key: 'configReq',
      title: '配置请求成功率',
      suffix: '%',
      current: this.getDefaultValue(currentConfigReqFields, 'successRate') * 100.0,
      compared: this.getDefaultValue(compareConfigReqFields, 'successRate') * 100.0,
      tabPane: <ConfigReqTrendLinePanel appHHTrend={appHHTrend} params={params}/>
    }, {
      key: 'bizCrash',
      title: '业务crash',
      current: this.getDefaultValue(currentBizCrashFields, 'count'),
      compared: this.getDefaultValue(compareBizCrashFields, 'count'),
      suffix: '',
      tabPane: <BizCrashTrendLinePanel appHHTrend={appHHTrend} params={params}/>
    }, {
      key: 'orangeCrash',
      title: 'orange crash',
      suffix: '',
      current: this.getDefaultValue(currentOrangeCrashFields, 'crashCount'),
      compared: this.getDefaultValue(compareOrangeCrashFields, 'crashCount'),
      tabPane: <OrangeCrashTrendLinePanel appHHTrend={appHHTrend} params={params}/>
    }];

    return (
      <div style={{lineHeight: 2, margin: '20px 5px', width: this.panelDivWidth || -1}} ref={this.panelDivRef}>
        <div className={baseStyles.holder} style={{marginRight: 10}}>
          <Tabs defaultActiveKey="0">
            {
              baseIndicators.map((indicator, idx) => {
                return (
                  <Tabs.TabPane tab={this._showCompareDom(indicator)} key={idx}>
                    <div style={{padding: '0 24px'}}>
                      {indicator.tabPane}
                    </div>
                  </Tabs.TabPane>
                )
              })
            }
          </Tabs>
        </div>
      </div>
    );
  }

  _showCompareDom(props) {
    return <DashboardMetrixColPanel {...props}/>
  }
}
