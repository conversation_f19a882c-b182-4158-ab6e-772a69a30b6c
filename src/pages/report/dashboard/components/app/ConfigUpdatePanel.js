import React from 'react';
import ConfigUpdateRateScatterPanel from "./update/ConfigUpdateRateScatterPanel";
import baseStyles from '@/styles/base.less';
import {Col, Radio, Row} from "@alipay/bigfish/antd";
import ConfigUpdateRateDayTrendLinePanel from "./update/ConfigUpdateRateDayTrendLinePanel";
import ConfigUpdateRankPanel from "./update/ConfigUpdateRankPanel";
import ConfigUseRateScatterPanel from "./use/ConfigUseRateScatterPanel";
import ConfigUpdateUseDayTrendLinePanel from "./use/ConfigUpdateUseDayTrendLinePanel";
import ConfigUseRankPanel from "./use/ConfigUseRankPanel";

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class ConfigUpdatePanel extends React.Component {
  constructor(props) {
    super(props);
    this.state = {rateView: 'configUpdateRateView'};
  }

  changeView = (e) => this.setState({rateView: e.target.value});

  render() {
    const {appIntervalDetails, appDayTrend, currentAppDash, compareAppDash, params} = this.props;
    const {rateView} = this.state;
    return (
      <div style={{lineHeight: 2, margin: '20px 5px'}}>
        <Row>
          <Col span={16}>
            <div className={baseStyles.holder} style={{marginRight: 10}}>
              <Radio.Group onChange={this.changeView} defaultValue='configUpdateRateView'
                           buttonStyle="solid">
                <Radio.Button value="configUpdateRateView">更新率</Radio.Button>
                <Radio.Button value="configUseRateView">使用率</Radio.Button>
              </Radio.Group>
              <div style={{marginTop: 20}}>
                {
                  !rateView || rateView === 'configUpdateRateView' ?
                    <ConfigUpdateRateScatterPanel appIntervalDetails={appIntervalDetails}/>
                    :
                    <ConfigUseRateScatterPanel appIntervalDetails={appIntervalDetails}/>
                }
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div className={baseStyles.holder}>
              <Row>
                <Col>
                  <div>
                    {
                      !rateView || rateView === 'configUpdateRateView' ?
                        <ConfigUpdateRateDayTrendLinePanel appDayTrend={appDayTrend}
                                                           currentAppDash={currentAppDash}
                                                           compareAppDash={compareAppDash}
                                                           params={params}/>
                        :
                        <ConfigUpdateUseDayTrendLinePanel appDayTrend={appDayTrend}
                                                          currentAppDash={currentAppDash}
                                                          compareAppDash={compareAppDash}
                                                          params={params}/>
                    }
                  </div>
                </Col>
                <Col>
                  <div>
                    {
                      !rateView || rateView === 'configUpdateRateView' ?
                        <ConfigUpdateRankPanel appIntervalDetails={appIntervalDetails} params={params}/>
                        :
                        <ConfigUseRankPanel appIntervalDetails={appIntervalDetails} params={params}/>
                    }
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </div>
    );
  }
}
