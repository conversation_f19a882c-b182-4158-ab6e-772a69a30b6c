import React from 'react';
import {List} from 'antd';
import {getDisplayInfo} from "@/utils/LangUtils";
import {Link} from 'dva/router';
import {LinkUtils} from '@/utils/LinkUtils';
import styles from "../../Styles.less";

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class ConfigUpdateRankPanel extends React.Component {

  render() {
    const {appIntervalDetails, appDayTrend} = this.props;
    let data = appIntervalDetails.filter(detail => detail
      && detail.configUpdateRateDetailFields
      && detail.configUpdateRateDetailFields.canStats)
      .sort((a, b) => {
        let f1 = a.configUpdateRateDetailFields;
        let f2 = b.configUpdateRateDetailFields;
        return (f1.updateRate || 0) - (f2.updateRate || 0);
      }).map(detail => {
        let fields = detail.configUpdateRateDetailFields;
        let updateRatePer = getDisplayInfo({type: 'percent', value: fields.updateRate, needParse: true});
        return {
          namespaceId: detail.namespaceId,
          namespace: detail.namespace,
          version: detail.version,
          updateRate: `${updateRatePer.value}${updateRatePer.suffix}`
        }
      }).slice(0, 5);
    return (
      <div className={styles.holder} style={{margin: 10, height: 290}}>
        <List
          size="small"
          split={true}
          header={<div><b>更新率排行</b></div>}
          dataSource={data}
          renderItem={(item, idx) => (
            <List.Item key={idx}
                       actions={[
                         <Link target='_blank' to={LinkUtils.getVersionDetail(item.namespaceId, item.version)}>
                           more
                         </Link>
                       ]}>
              <List.Item.Meta
                title={
                  <div className={styles.txtEllipsis}><span
                    className={idx <= 2 ? styles.rcornersBlack : styles.rcornersWhite}>{idx + 1}</span>{item.namespace}
                  </div>
                }/>
              <div>{item.updateRate}</div>
            </List.Item>
          )}
        />
      </div>
    );
  }
}
