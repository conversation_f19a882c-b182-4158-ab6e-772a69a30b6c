import React from 'react';
import ReactEcharts from 'echarts-for-react';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {getDisplayInfo} from "@/utils/LangUtils";
import {formatTime, getDayOfMills} from "@/utils/TimeUtils";

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class ConfigUpdateRateScatterPanel extends React.Component {
  getOption = (appIntervalDetails) => {
    let legendNames = ['measurable', 'highInit', 'highLazy', 'DEFAULT'];
    let groups = legendNames.reduce((groups, name) => {
      groups[name] = {
        name: name,
        symbolSize: 10,
        type: 'scatter',
        showSymbol: true,
        data: [],
      };
      return groups;
    }, {});
    appIntervalDetails.filter(detail => detail && detail.configUpdateRateDetailFields)
      .forEach(detail => {
        let fields = detail.configUpdateRateDetailFields;
        let loadName = ConvertUtils.getLoadLevelName(fields.loadLevel);
        let dayOfMills = getDayOfMills(new Date(fields.gmtEnd));
        groups[loadName].data.push([dayOfMills, fields.updateRate, detail]);
        if (fields.canStats) {
          groups['measurable'].data.push([dayOfMills, fields.updateRate, detail]);
        }
      });
    return {
      title: {
        // text: '配置更新率'
      },
      color: ['#4d6fb6', '#57b3ee', '#a9d8f5', '#c8e7f9', '#eaf6fc', '#f2f2f4'],
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let param0 = params[0];
          let detail = param0.value[2];
          let fields = detail.configUpdateRateDetailFields;
          let tips = [];
          tips.push(`时间: ${formatTime(fields.gmtEnd)}`);
          tips.push(`namespace: ${detail.namespace}`);
          tips.push(`version: ${detail.version}`);
          let updateRatePer = getDisplayInfo({type: 'percent', value: fields.updateRate});
          tips.push(`更新率: ${updateRatePer.value}${updateRatePer.suffix}`);
          return tips.join('<br/>');
        }
      },
      legend: {
        show: true,
        orient: 'horizontal',
        top: 5,
        right: 15,
        textStyle: {
          fontSize: 14,
          fontWeight: 100,
          color: '#bbb',
        },
        data: legendNames
      },
      grid: {
        top: 50,
        left: 15,
        right: 15,
        bottom: 15,
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: false,
        axisTick: {
          show: true,
          length: 8,
          lineStyle: {
            width: 1,
            color: '#dedede',
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: function (value) {
            return (value / 3600 / 1000 + '').padStart(2, 0) + ":00";
          },
        },
        min: 0,
        max: 24 * 3600 * 1000,
        interval: 2 * 3600 * 1000,
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisTick: {show: false},
        axisLine: {show: false},
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: lab => (`${(lab*100).toFixed(2)}%`),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#dedede',
            width: 1,
            type: 'dashed',
          },
        },
      },
      series: Object.values(groups)
    }
  }

  render() {
    const {appIntervalDetails} = this.props;
    return (
      <div>
        <h4>配置更新率</h4>
        <ReactEcharts option={this.getOption(appIntervalDetails)} style={{height: 330}}/>
      </div>
    );
  };
}
