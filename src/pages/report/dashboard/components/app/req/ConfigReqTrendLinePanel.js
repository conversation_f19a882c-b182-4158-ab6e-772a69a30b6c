import React from 'react';
import ReactEcharts from 'echarts-for-react';
import {getDisplayInfo} from "@/utils/LangUtils";

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class ConfigReqTrendLinePanel extends React.Component {

  getOption = (appHHTrend, params) => {
    params = params || {};
    const legend1 = params.current || '当前';
    // const legend2 = params.compared || '对比';

    let seriesData = (appHHTrend || []).map(trendData => {
      let {ds, configReqFields} = trendData;
      let rate = configReqFields ? configReqFields.successRate : null;
      return [parseInt(ds.substr(-2, 2)), rate, trendData];
    });
    return {
      title: {
        // text: '配置请求成功率'
      },
      color: ['#4d6fb6', '#57b3ee', '#a9d8f5', '#c8e7f9', '#eaf6fc', '#f2f2f4'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
          },
        },
        formatter: function (params) {
          let tips = [(params[0].value[0] + '').padStart(2, '0') + ':00'];
          (params || [])
            .forEach(param => {
              let [hh, rate, data] = param.value;
              let rateStr = getDisplayInfo({type: 'percentStr', value: rate, needParse: true});
              let tip = `${param.marker}${data.ds}: ${rateStr}`;
              tips.push(tip);
            });
          return tips.join('<br/>');
        },
      },
      legend: {
        show: false,
        orient: 'horizontal',
        top: 5,
        right: 15,
        textStyle: {
          fontSize: 14,
          fontWeight: 100,
          color: '#bbb',
        },
        data: [legend1]
      },
      grid: {
        top: 50,
        left: 15,
        right: 15,
        bottom: 15,
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: false,
        axisTick: {
          show: true,
          length: 8,
          lineStyle: {
            width: 1,
            color: '#dedede',
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: function (value) {
            return (value + '').padStart(2, 0) + ":00";
          },
        },
        min: 0,
        max: 24,
        interval: 2,
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisTick: {show: false},
        axisLine: {show: false},
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: lab => (`${(lab*100).toFixed(0)}%`),
        },
        min: (value) => {
          const min = Math.ceil((0 || value.min) * 10) / 10 - 0.1;
          return min > 0 ? min : 0;
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#dedede',
            width: 1,
            type: 'dashed',
          },
        },
      },
      series: [{
        name: legend1,
        data: seriesData,
        type: 'line',
        symbolSize: 10,
        showSymbol: true,
      }]
    }
  }

  render() {
    const {appHHTrend} = this.props;
    return (
      <div>
        <h4>配置请求成功率</h4>
        <ReactEcharts option={this.getOption(appHHTrend)}/>
      </div>
    );
  };
}
