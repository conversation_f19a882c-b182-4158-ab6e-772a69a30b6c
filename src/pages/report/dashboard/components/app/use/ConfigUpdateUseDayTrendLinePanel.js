import React from 'react';
import ReactEcharts from 'echarts-for-react';
import echarts from 'echarts';
import {getTrendRangeDays} from "@/utils/TimeUtils";
import {Col, Row, Statistic} from "@alipay/bigfish/antd";
import {getDisplayInfo} from "@/utils/LangUtils";

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class ConfigUpdateUseDayTrendLinePanel extends React.Component {
  getOption = (appDayTrend) => {
    let map = appDayTrend.filter(trendData => trendData && trendData.configUseFields)
      .reduce((map, data) => {
        let fields = data.configUseFields;
        map[data.ds.substr(0, 8)] = [data.ds.substr(0, 8), fields.meanUseRate, data];
        return map;
      }, {});
    let seriesData = Object.values(map);
    let xAxisData = getTrendRangeDays();
    return {
      title: {
        // text: '7天趋势'
      },
      color: ['#4d6fb6', '#57b3ee', '#a9d8f5', '#c8e7f9', '#eaf6fc', '#f2f2f4'],
      grid: {
        top: 5,
        bottom: 10
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#dedede'
          }
        }
      },
      yAxis: {
        type: 'value',
        show: false,
        axisTick: {
          show: false
        },
        min: 0,
        max: 2,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
          },
        },
        formatter: function (params) {
          let tips = [(params[0].value[0] + '').padStart(2, '0')];
          (params || [])
            .forEach(param => {
              let [hh, rate, data] = param.value;
              let percentStr = getDisplayInfo({type: 'percentStr', value: rate, needParse: true});
              let tip = `${param.marker}${data.ds}: ${percentStr}`;
              tips.push(tip);
            });
          return tips.join('<br/>');
        },
      },
      series: [{
        data: seriesData,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: '#4d6fb6'
          }, {
            offset: 1,
            color: '#eaf6fc'
          }])
        },
      }]
    };
  }

  render() {
    const {appDayTrend, currentAppDash} = this.props;
    let {configUseFields} = (currentAppDash || {});
    let rate = (configUseFields || {}).meanUseRate * 100 || ' -';
    return (
      <div>
        <Row>
          <Col span={10}>
            <Statistic
              title='7天更新率'
              value={rate >= 0 ? rate : ' --'}
              valueStyle={{fontSize: 32}}
              precision={2}
              prefix={<span style={{fontSize: 4}}>当前</span>}
              suffix={rate >= 0 ? '%' : ''}
            />
          </Col>
          <Col span={14}>
            <div style={{marginTop: 20}}>
              <ReactEcharts option={this.getOption(appDayTrend)} style={{height: '80px'}}/>
            </div>
          </Col>
        </Row>
      </div>
    );
  };
}
