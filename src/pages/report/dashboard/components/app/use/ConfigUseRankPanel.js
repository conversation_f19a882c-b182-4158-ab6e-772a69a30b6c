import React from 'react';
import {List} from 'antd';
import {getDisplayInfo} from "@/utils/LangUtils";
import {Link} from 'dva/router';
import {LinkUtils} from '@/utils/LinkUtils';
import styles from "../../Styles.less";

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class ConfigUpdateRankPanel extends React.Component {

  render() {
    const {appIntervalDetails, appDayTrend} = this.props;
    let data = appIntervalDetails.filter(detail => detail
      && detail.configUseDetailFields
      && detail.configUseDetailFields.canStats)
      .sort((a, b) => {
        let f1 = a.configUseDetailFields;
        let f2 = b.configUseDetailFields;
        return (f1.useRate || 0) - (f2.useRate || 0);
      }).map(detail => {
        let fields = detail.configUseDetailFields;
        let useRatePer = getDisplayInfo({type: 'percent', value: fields.useRate, needParse: true});
        return {
          namespaceId: detail.namespaceId,
          namespace: detail.namespace,
          version: detail.version,
          useRate: `${useRatePer.value}${useRatePer.suffix}`
        }
      }).slice(0, 5);
    return (
      <div className={styles.holder} style={{margin: 10, height: 290}}>
        <List
          size="small"
          header={<div><b>使用率排行</b></div>}
          split={true}
          dataSource={data}
          renderItem={(item, idx) => (
            <List.Item key={idx}
                       actions={[
                         <Link target='_blank' to={LinkUtils.getVersionDetail(item.namespaceId, item.version)}>
                           more
                         </Link>
                       ]}>
              <List.Item.Meta
                title={
                  <div><span
                    className={idx <= 2 ? styles.rcornersBlack : styles.rcornersWhite}>{idx + 1}</span>{item.namespace}
                  </div>
                }/>
              <div>{item.useRate}</div>
            </List.Item>
          )}
        />
      </div>
    );
  }
}
