import React from 'react';
import ReactEcharts from 'echarts-for-react';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class OrangeCrashTrendLinePanel extends React.Component {

  getOption = (appHHTrend, params) => {
    params = params || {};
    const legend1 = params.current || '当前';
    // const legend2 = params.compared || '对比';

    let seriesData = (appHHTrend || []).map(trendData => {
      let {ds, orangeCrashFields} = trendData;
      let crashCount = orangeCrashFields ? orangeCrashFields.crashCount : '';
      return [parseInt(ds.substr(-2, 2)), crashCount, trendData];
    });
    return {
      title: {
        // text: 'orange crash率'
      },
      color: ['#4d6fb6', '#57b3ee', '#a9d8f5', '#c8e7f9', '#eaf6fc', '#f2f2f4'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
          },
        },
        formatter: function (params) {
          let tips = [(params[0].value[0] + '').padStart(2, '0') + ':00'];
          (params || [])
            .forEach(param => {
              let [hh, crashCount, data] = param.value;
              let tip = `${param.marker}${data.ds}: ${crashCount >= 0 ? crashCount : '-'}`;
              tips.push(tip);
            });
          return tips.join('<br/>');
        },
      },
      legend: {
        show: false,
        orient: 'horizontal',
        top: 5,
        right: 15,
        textStyle: {
          fontSize: 14,
          fontWeight: 100,
          color: '#bbb',
        },
        data: [legend1]
      },
      grid: {
        top: 50,
        left: 15,
        right: 15,
        bottom: 15,
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        boundaryGap: false,
        axisTick: {
          show: true,
          length: 8,
          lineStyle: {
            width: 1,
            color: '#dedede',
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: function (value) {
            return (value + '').padStart(2, 0) + ":00";
          },
        },
        min: 0,
        max: 24,
        interval: 2,
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisTick: {show: false},
        axisLine: {show: false},
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: lab => (`${lab}次`),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#dedede',
            width: 1,
            type: 'dashed',
          },
        },
      },
      series: [{
        name: legend1,
        data: seriesData,
        type: 'line',
        symbolSize: 10,
        showSymbol: true,
      }]
    }
  }

  render() {
    const {appHHTrend} = this.props;
    return (
      <div>
        <h4>orange crash</h4>
        <ReactEcharts option={this.getOption(appHHTrend)}/>
      </div>
    );
  };
}
