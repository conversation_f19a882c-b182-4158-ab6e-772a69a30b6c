import React from 'react';
import {Icon, Table, List} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';
import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from '@/utils/ConvertUtils';
import {VersionUtils} from '@/utils/VersionUtils';

import styles from '../Styles.less';

export default class ConsoleCountRankPanel extends React.Component {
  getColumns = (appKey) => [
    {
      dataIndex: 'item',
      title: '配置名词'
    },
    {
      dataIndex: 'count',
      title: '发布次数',
      render(text, record) {
        return (<div>{text}</div>);
      }
    },
    {
      title: '操作',
      render(text, record) {
        return (
          <div>
            <p>
              <Link to={LinkUtils.getVersionListOfNs(record.item, appKey)}>
                详情
              </Link>
            </p>
          </div>
        );
      },
    },
  ];

  removeItem = () => {
  };

  render() {
    const {loading} = this.props;
    let {current, compared, appKey} = this.props;
    let data = current || [];

    /* let data = [{item: "ns1", count: 11}, {item: "ns2", count: 2}, {item: "ns3", count: 3}, {
       item: "ns4",
       count: 34
     }, {item: "ns5", count: 15}, {item: "ns6", count: 65}];
     */
    data.sort(function (a, b) {
      return b.count - (a.count);
    });

    data.forEach(function (item, index, array) {
      item.index = index + 1;
    });
    data = data.slice(0, 5);

    /*
     const pagination = false;
    <Table
            loading={loading}
            columns={this.getColumns(appKey)}
            dataSource={data}
            rowKey={record => record.item}
            pagination={pagination}
          />
                    header={<div><b>更新率排行</b></div>}

          */
    return (
      <div className={styles.holder} style={{"margin": "10px"}}>

        <List
          size="small"
          split={true}
          dataSource={data}
          renderItem={item => (
            <List.Item key={item.item} actions={[<Link to={LinkUtils.getVersionListOfNs(item.item, appKey)}>
              more
            </Link>]}>
              <List.Item.Meta
                title={<div className={styles.txtEllipsis}><span
                  className={item.index <= 3 ? styles.rcornersBlack : styles.rcornersWhite}>{item.index}</span>
                  <span>{item.item}</span>
                </div>}/>

              <div>{item.count}</div>
            </List.Item>

          )}
        />


      </div>
    );
  }
}
