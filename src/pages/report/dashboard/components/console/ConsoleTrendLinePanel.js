import React from 'react';
import ReactEcharts from 'echarts-for-react';

export default class ConsoleTrendLinePanel extends React.Component {
  getOption = (xAxis, series11, series12, series21, series22, params) => {
    const legend11 = params.current || '当前';
    const legend12 = params.current + '汇总';
    const legend21 = params.compared || '对比';
    const legend22 = params.compared + '汇总';
    return {
      title: {
        //text: 'DC请求成功率'
      },
      color: ['#4d6fb6', '#4d6fb6', '#57b3ee', '#57b3ee'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
          },
        },
        /*formatter: params => {
        //  console.log( params[0])
          const { name, seriesName, dataIndex, value } = params[0];
          return `<span>${name}时:${value}次</span>`;
        },*/
      },
      legend: {
        show: true,
        orient: 'horizontal',
        top: 5,
        right: 15,
        textStyle: {
          fontSize: 14,
          fontWeight: 100,
          color: '#bbb',
        },
        data: [legend11, legend12, legend21, legend22],
      },
      grid: {
        top: 50,
        left: 15,
        right: 15,
        bottom: 15,
        containLabel: true,
      },
      toolbox: {
        feature: {
          // saveAsImage: {}
        }
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        boundaryGap: false,
        axisTick: {
          show: true,
          length: 8,
          lineStyle: {
            width: 1,
            color: '#dedede',
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          // formatter: lab => (`${lab}:00`),
        },

      },
      yAxis: {
        type: 'value',
        scale: true,
        axisTick: {show: false},
        axisLine: {show: false},
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: lab => (`${lab}次`),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#dedede',
            width: 1,
            type: 'dashed',
          },
        },
      },
      series: [{
        data: series11,
        name: legend11,
        type: 'bar',
        symbolSize: 10,
        showSymbol: true,
      }, {
        data: series12,
        name: legend12,
        type: 'line',
        symbolSize: 10,
        showSymbol: true,
      }, {
        data: series21,
        name: legend21,
        type: 'bar',
        symbolSize: 10,
        showSymbol: true,
      }, {
        data: series22,
        name: legend22,
        type: 'line',
        symbolSize: 10,
        showSymbol: true,
      }]
    }
  }

  render() {
    const currentList = this.props.current || [];
    const compareList = this.props.compared || [];
    const params = this.props.params || {};

    let currenthMap = {};
    let compareMap = {};

    currentList.forEach(function (item, index, array) {
      const ds = item.item;
      const hh = ds.slice(8, ds.length);
      currenthMap[hh] = item.count;
    });
    compareList.forEach(function (item, index, array) {
      const ds = item.item;
      const hh = ds.slice(8, ds.length);
      compareMap[hh] = item.count;
    });
    let xAxis = [];
    let series11 = [];
    let series21 = [];
    let series12 = [];
    let series22 = [];
    let sum1 = 0;
    let sum2 = 0;
    for (let i = 0; i < 24; i++) {
      const key = i < 10 ? '0' + i : '' + i;
      xAxis.push(key + ":00");
      const value1 = currenthMap[key] || 0;
      sum1 += value1;
      series11.push(value1);
      series12.push(sum1)
      const value2 = compareMap[key] || 0;
      sum2 += value2;
      series21.push(value2);
      series22.push(sum2);

    }
    return (
      <div>
        <ReactEcharts option={this.getOption(xAxis, series11, series12, series21, series22, params)}/>
      </div>
    );
  }
}
