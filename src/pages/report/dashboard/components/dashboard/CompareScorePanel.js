import React from 'react';
import {Icon} from '@alipay/bigfish/antd';


export default class CompareScorePanel extends React.Component {


  showComparePercent = (current, compared) => {

    if (!compared) {
      return null;
    }
    const comparePercent = compared ? ((current - compared) * 100.0 / compared) : 0
    const compareStyle = (comparePercent >= 0) ? 'up-color' : 'down-color';
    const num = (Number(comparePercent) * 1.0).toFixed(1);
    if (isNaN(num) || Number(compared) === 0) {
      return '-';
    }
    const dir = num >= 0 ? 'up' : 'down';
    return (
      <span className={`${compareStyle} pull-right`}>
      <span>
      <Icon className={`${dir}-color`} type={`caret-${dir}`} style={{"marginRight": "2px"}}/> {num} %
        </span>
    </span>
    );
  }
  getColumns = (_showComparePercent) => [{

    dataIndex: 'title',
    title: 'title'

  }, {
    dataIndex: 'current',
    title: 'current',
    render(text, record) {
      return <span>{record.current ? record.current : '-'}{record.suffix}</span>;
    }

  },
    {
      title: '对比结果',
      render(text, record) {
        return _showComparePercent(record.current, record.compared, "0px 0px");
      }
    }
  ]

  render() {
    const current = this.props.current || 0;
    const compared = this.props.compared || 0;

    return this.showComparePercent(current, compared);


  }
}
