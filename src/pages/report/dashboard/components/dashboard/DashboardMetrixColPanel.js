import React from 'react';
import {Statistic} from '@alipay/bigfish/antd';
import CompareScorePanel from "./CompareScorePanel";


export default class DashboardSubMetrixPanel extends React.Component {

  getColumns = () => [{

    dataIndex: 'title',
    title: 'title'

  }, {
    dataIndex: 'current',
    title: 'current',
    render(text, record) {
      return current ? <span>{record.current}{record.suffix}</span> : <span>-</span>;
    }

  },
    {
      title: '对比结果',
      render(text, record) {
        return <CompareScorePanel current={record.current} compared={record.compared} margin="0px 0px"/>;
      }
    }
  ]

  render() {
    const that = this;
    const title = this.props.title || "更新率";
    const current = this.props.current || 0;
    const compared = this.props.compared || 0;
    const suffix = this.props.suffix || '';
    const fontSize = this.props.fontSize || '32px';
    const margin = this.props.margin || "0px 20px 0px 0px";

    return (<div style={{width: "200px"}}>
      <Statistic
        title={title}
        value={current}
        valueStyle={{fontSize: fontSize, marginLeft: "20px"}}
        suffix={suffix}
        precision={suffix === '%' ? 2 : '-'}
      />
      {compared ?
        <div style={{"margin": margin}}>
          <CompareScorePanel current={current} compared={compared}/>
        </div> : null}

    </div>);
  }
}
