import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {getFirstValues, toDateValues} from '@/utils/ReportUtils';

export default class DashboardGaugePanel extends React.Component {
  getOption = (score) => {
    let show = (score < '80') ? '差' : '中';
    if (score > 90) {
      show = '优';
    }

    const option = {
      tooltip: {
        formatter: "{a} : {c}分（ {b} ）"
      },
      toolbox: {
        feature: {
          //restore: {},
          //saveAsImage: {}
        }
      },
      series: [
        {
          name: '全局打分',
          type: 'gauge',
          min: 50,
          max: 100,
          beginAngle: 0,
          endAngle: 0,
          splitNumber: 5,
          // detail: {formatter: '{value}%'}, //仪表盘显示数据
          axisLine: { //仪表盘轴线样式
            lineStyle: {
              width: 10
            }
          },
          splitLine: { //分割线样式
            length: 10
          },
          data: [{value: score, name: show}],
          markPoint: {
            symbol: 'circle',
            symbolSize: 5,
            data: [
//跟你的仪表盘的中心位置对应上，颜色可以和画板底色一样
              {x: 'center', y: 'center', itemStyle: {color: '#FFF'}}
            ]
          },
        }
      ]
    };
    return option;
  }

  render() {
    const score = this.props.score || 0;
    //上右下左
    return (
      <div style={{margin: "-40px -10px 0px -10px", height: "200px"}}>
        <ReactEcharts option={this.getOption(score)}/>
      </div>
    );
  }
}
