import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {Breadcrumb, Col, Icon, Progress, Radio, Row, List, Table} from '@alipay/bigfish/antd';

import {getFirstValues, toDateValues} from '@/utils/ReportUtils';


import styles from '../Styles.less';
import CompareScorePanel from "./CompareScorePanel";


export default class DashboardMetrixPanel extends React.Component {

  getColumns = () => [{

    dataIndex: 'title',
    title: 'title'

  }, {
    dataIndex: 'current',
    title: 'current',
    render(text, record) {
      return (record.current) ? (<span>{record.current}{record.suffix}</span>) : <span>-</span>;
    }

  },
    {
      title: '对比结果',
      render(text, record) {
        return <CompareScorePanel current={record.current} compared={record.compared} margin="0px 0px"/>;
      }
    }
  ]

  render() {
    const that = this;
    const title = this.props.title || "更新率";
    const current = this.props.current || 0;
    const compared = this.props.compared || 0;
    const suffix = this.props.suffix || '分';

    let detailList = this.props.detailList || [];
    /***
     *<List
     size="small"
     split={false}
     dataSource={detailList}
     renderItem={item => (
                <List.Item key={item.title} actions={[ this.showComparePercent(item.current, item.compared,"0px 0px")]}>
                  <List.Item.Meta
                    title={<div>{item.title}
                    </div>}/>

                  <div>{item.current}{item.suffix}</div>
                </List.Item>

              )}
     /
     */
    //上右下左
    return (
      <div className={styles.holder}>

        <div className={styles.metrix}>
          <div className={styles.title}>
            {title}
          </div>
          <div>
            {current ? <span className={styles.score}>{parseInt(current)}<span className={styles.suffix}>{suffix}</span></span> :
              <span className={styles.score}>-</span>}
            {compared ? <CompareScorePanel current={parseInt(current)} compared={parseInt(compared)} margin="20px 0px 0px"/> : null}
          </div>
          <div style={{margin: "10px 5px 10px 20px"}}>
            <div className={styles.split}></div>
            {detailList && detailList[0] ? <Table
              bordered={false}
              showHeader={false}
              columns={this.getColumns()}
              dataSource={detailList}
              rowKey={record => record.title}
              pagination={false}
            /> : null
            }

          </div>

        </div>
      </div>

    );
  }
}
