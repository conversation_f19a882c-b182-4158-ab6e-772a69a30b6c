import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {getFirstValues, toDateValues} from '@/utils/ReportUtils';


const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export default class DcRequestCountPanel extends React.Component {
    getOption = (yAxis, series) => {
        return  {
            title: {
                text: 'DC请求数统计'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                boundaryGap: [0, 0.01]
            },
            yAxis: {
                type: 'category',
                data: yAxis
            },
            series: [
                {
                    type: 'bar',
                    data: series
                }
            ]
        }
    }

    render() {
        const currentDcDash = this.props.currentDcDash || {};
        let yAxis = [];//日期
        let series2 = [];
        yAxis.push("请求数");
        series2.push(currentDcDash.totalCnt);
        yAxis.push("成功数");
        series2.push(currentDcDash.successCnt);
        yAxis.push("失败数");
        series2.push(currentDcDash.totalCnt - currentDcDash.successCnt);
        const apiList = currentDcDash.apiDetailList || []
        apiList.forEach(function (item, index, array) {
            yAxis.push(item.name);
            series2.push(item.value);
        })
        //const {notifyNum, updateNum} = this.props;
        return (
            <div>
                <ReactEcharts   option={this.getOption(yAxis, series2)}/>
            </div>
        );
    }
}
