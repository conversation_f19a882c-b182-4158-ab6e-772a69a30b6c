import React from 'react';
import {Button, DatePicker, Form, Input, Select, Switch} from '@alipay/bigfish/antd';

import moment from 'moment';


import styles from './Styles.less';

const {Option} = Select;
const FormItem = Form.Item;


const dateFormat = 'YYYY-MM-DD';

export default class QueryForm extends React.Component {
  render() {
    const {formData, onChange, onRefresh} = this.props;
    const appList = this.props.appList || [];
    const toString = function (key) {
      return '' + key;
    }
    return (
      <div  className={styles.form}>
        <Form layout="inline" className="clearfix">
          <FormItem label="客户端">
            <Select
              showSearch
              value={formData.appKey}
              className={styles.typeSelectormax}

              onChange={(v) => {
                onChange({
                  params: {
                    appKey: v,
                  },
                });
              }}
            >
              {appList.map((item) => {
                return <Option value={item.appKey} key={item.appKey}>{item.appKey}-{item.appName}</Option>;
              })}

            </Select>
          </FormItem>
          <FormItem label="当前">
            <DatePicker
              showTime={false}
              format={dateFormat}
              placeholder="Start"
              value={formData.current ? moment(formData.current, dateFormat) : null}
              onChange={function (value, dateString) {
                onChange({
                  params: {
                    current: dateString
                  },
                });

              }}
            />
          </FormItem>
          <FormItem label="对比">
            <DatePicker
              showTime={false}
              format={dateFormat}
              placeholder="End"
              value={formData.compared ? moment(formData.compared, dateFormat) : null}
              onChange={function (value, dateString) {
                onChange({
                  params: {
                    compared: dateString
                  },
                });

              }}
            />
          </FormItem>
          <FormItem >
            <Button className="st-mr" type="primary" onClick={onRefresh}>
              查询
            </Button>
          </FormItem>
        </Form>
      </div>
    );
  }
}
