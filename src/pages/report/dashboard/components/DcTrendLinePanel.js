import React from 'react';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import {getFirstValues, toDateValues} from '@/utils/ReportUtils';
import {ConvertUtils} from '@/utils/ConvertUtils';


export default class DcTrendLinePanel extends React.Component {
  getOption = (xAxis, legend, series) => {
    return  {
      title: {
        //text: 'DC请求成功率'
      },
      color: ['#4d6fb6', '#57b3ee', '#a9d8f5', '#c8e7f9', '#eaf6fc', '#f2f2f4'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      legend: {
        show: true,
        orient: 'horizontal',
        top: 5,
        right: 15,
        textStyle: {
          fontSize: 14,
          fontWeight: 100,
          color: '#bbb',
        },
        data: legend
      },
      grid: {
        left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
      },
      toolbox: {
        feature: {
          // saveAsImage: {}
        }
      },
      xAxis: {
        type: 'category',
          boundaryGap: false,
          data: xAxis,
        axisTick: {
          show: true,
          length: 8,
          lineStyle: {
            width: 1,
            color: '#dedede',
          },
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          // formatter: lab => (`${lab}:00`),
        },
      },
      yAxis: {
        type: 'value',
        scale: true,
        axisTick: {show: false},
        axisLine: {show: false},
        axisLabel: {
          margin: 16,
          fontSize: 12,
          fontWeight: 100,
          color: '#bbb',
          formatter: lab => (`${lab}%`),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#dedede',
            width: 1,
            type: 'dashed',
          },
        },
      },
      series: series
    }
  }

  render() {
    const dcHHTrend = this.props.dcHHTrend || [];
    function unique (arr) {
      return Array.from(new Set(arr))
    }
    let trendData = dcHHTrend.filter((data) => {
      return data.name === null || data.name === "";
    });

    let legend = unique(trendData.map((data) => {
      return data.code;
    }));

    let xAxis = unique(trendData.map((data) => {
      return data.ds;
    }));

    let series = [];
    legend.forEach(function (item, index, array) {
      let s = {};
      s.name = item;
      s.type = 'line';
      s.data = trendData.filter((data) => {
        return data.code === item;
      }).map((data) => {
          return data.data;
      });
      series.push(s);
    })

    let totalCnt = [];
    series.forEach(function(d, index, array) {
      for (let i = 0; i < d.data.length; i++) {
        if (totalCnt[i] == null) {
          totalCnt[i] = 0;
        }
        totalCnt[i] = parseInt(totalCnt[i]) + parseInt(d.data[i]);
      }
    })
    //deep copy
    let objString = JSON.stringify(series);
    let series2 = JSON.parse(objString);
    series2.forEach(function(d, index, array) {
      for (let i = 0; i < d.data.length; i++) {
        d.name = ConvertUtils.getReportDCFailName(d.name);
        d.data[i] = (parseInt(d.data[i])/parseInt(totalCnt[i]) * 100).toFixed(2);
      }
    });
    let newXAxis = [];
    if ((xAxis[0] + "").length > 8) {
      xAxis.forEach(function (item, index, array){
        newXAxis.push((item+"").substring(8) + ":00");
      });
    } else {
      xAxis.forEach(function (item, index, array){
        newXAxis.push((item+"").substring(4,6) + "-" + (item+"").substring(6));
      });
    }
    let newLegend = [];
    legend.forEach(function (item, index, array) {
      newLegend.push(ConvertUtils.getReportDCFailName(item));
    })
    //const {notifyNum, updateNum} = this.props;
    return (
      <div>
        <ReactEcharts   option={this.getOption(newXAxis, newLegend, series2)}/>
      </div>
    );
  }
}
