import React from 'react';
import {connect} from 'dva';
import {getFuncName} from '@/utils/utils';
import {getDefaultPercentValue,getDefaultLongValue} from '@/utils/LangUtils';


import {Link} from 'dva/router';
import {LinkUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from '@/utils/ConvertUtils';
import namespace from '@/config/namespace';
import QueryForm from './components/QueryForm';
import {Breadcrumb, Col, Row, Statistic} from '@alipay/bigfish/antd';
import baseStyles from '@/styles/base.less';
import dashStyles from './index.less';
import DcTrendLinePanel from "./components/DcTrendLinePanel";
import DcTrendBarPanel from "./components/DcTrendBarPanel";
import ConsoleCountRankPanel from "./components/console/ConsoleCountRankPanel";
import ConsoleTrendLinePanel from "./components/console/ConsoleTrendLinePanel";
import ConfigUpdatePanel from "./components/app/ConfigUpdatePanel";
import ConfigExceptionPanel from "./components/app/ConfigExceptionPanel";
import DashboardGaugePanel from "./components/dashboard/DashboardGaugePanel";
import DashboardMetrixPanel from "./components/dashboard/DashboardMetrixPanel";
import DashboardMetrixColPanel from "./components/dashboard/DashboardMetrixColPanel";


const NAME_SPACE = namespace.report.dashboard;

class DashboardReport extends React.Component {
  constructor(props) {
    super(props);
  }

  componentWillMount() {
    this.setState({dcTrendView: 'dcTrendRate'});
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }


  _renderDashBoard(currentDcDash, compareDcDash, currentAppDash, compareAppDash, params) {

    const _computeScore = function (arr) {
      if (!arr) {
        return 0;
      }
      let count = 0;
      let score = 0;
      arr.forEach(function (item, index, array) {
        if (item) {
          count++;
          score += (item >100?100:item);
        }
      });
      return count != 0 ? parseInt(score / count) : 0;
    }

    const _computeProbeRate = function (apiJson) {
      if (!apiJson) {
        return 0;
      }
      let total = 0;
      let success = 0;
      for (let item in apiJson) {
        if (item !== 'downloadResource' && currentDcDash && currentDcDash.apiDetailList && currentDcDash.apiDetailList[item]) {
          currentDcDash.apiDetailList[item].forEach(function (item1, index1, array1) {
            total += item1.value;
            if (item1.name === '200') {
              success += item1.value;
            }

          });
        }
      }
      return (total === 0 ? 0 : success / total * 100).toFixed(2);
    }

    const {
      configUpdateRateFields: compareConfigUpdateRateFields,
      configUseFields: compareConfigUseFields,
      bizCrashFields: compareBizCrashFields,
      orangeCrashFields: compareOrangeCrashFields,
      indexReqFields: compareIndexReqFields,
      configReqFields: compareConfigReqFields
    } = compareAppDash || {};
    const {
      configUpdateRateFields: currentConfigUpdateRateFields,
      configUseFields: currentConfigUseFields,
      bizCrashFields: currentBizCrashFields,
      orangeCrashFields: currentOrangeCrashFields,
      indexReqFields: currentIndexReqFields,
      configReqFields: currentConfigReqFields
    } = currentAppDash || {};

    let updateRateDetailList = [{
      key: 'configUpdateRate',
      title: '更新率',
      suffix: '%',
      current: getDefaultPercentValue(currentConfigUpdateRateFields, 'meanUpdateRate'),
      compared: getDefaultPercentValue(compareConfigUpdateRateFields, 'meanUpdateRate'),
    },
      {
        key: 'configUseRate',
        title: '使用率',
        suffix: '%',
        current: getDefaultPercentValue(currentConfigUseFields, 'meanUseRate'),
        compared: getDefaultPercentValue(compareConfigUseFields, 'meanUseRate'),
      }];
    const updateDash = {
      title: "全局更新",
      current: _computeScore([updateRateDetailList[0].current, updateRateDetailList[1].current]),
      compared: _computeScore([updateRateDetailList[0].compared, updateRateDetailList[1].compared]),
      suffix: "分",
      detailList: updateRateDetailList
    }
    let dcDashDetailList = [
      {
        title: "请求数",
        current: getDefaultLongValue(currentDcDash, 'totalCnt'),
        compared: getDefaultLongValue(compareDcDash, 'totalCnt'),
        suffix: ""
      },
      {
        title: "探针更新率",
        current: _computeProbeRate(currentDcDash.apiDetailList),
        compared: _computeProbeRate(compareDcDash.apiDetailList),
        suffix: "%"
      }
    ]
    const dcDash = {
      title: "云稳定性",
      current: _computeScore([dcDashDetailList[1].current]),
      compared: _computeScore([dcDashDetailList[1].compared]),
      suffix: "分",
      detailList: dcDashDetailList
    }
    let updateDetailList = [{
      key: 'indexReq',
      title: '索引成功率',
      suffix: '%',
      current: getDefaultPercentValue(currentIndexReqFields, 'successRate'),
      compared: getDefaultPercentValue(compareIndexReqFields, 'successRate'),
    }, {
      key: 'configReq',
      title: '配置成功率',
      suffix: '%',
      current: getDefaultPercentValue(currentConfigReqFields, 'successRate'),
      compared: getDefaultPercentValue(compareConfigReqFields, 'successRate'),
    }];
    const clientDash = {
      title: "端稳定性",
      current: _computeScore([updateDetailList[0].current, updateDetailList[1].current]),
      compared: _computeScore([updateDetailList[0].compared, updateDetailList[1].compared]),
      suffix: "分",
      detailList: updateDetailList
    }
    const globalScore = _computeScore([updateDash.current, dcDash.current, clientDash.current]);
    return <div className={baseStyles.holder}>

      <div style={{"display": " -webkit-box"}}>

        <div style={{"width": "18%"}}>
          <DashboardGaugePanel score={globalScore}/>
        </div>
        <DashboardMetrixPanel  {...clientDash}/>
        <DashboardMetrixPanel {...dcDash}/>
      </div>
    </div>
  }

  _renderDc(currentDcDash, compareDcDash, dcHHTrend) {
    const errorCodeList = [];
    const requestCountList = [];
    const requestRateList = [];
    currentDcDash.errorCodeDetailList = currentDcDash.errorCodeDetailList || [];
    currentDcDash.apiDetailList = currentDcDash.apiDetailList || {};
    const totalErrorCnt = currentDcDash.totalCnt - currentDcDash.successCnt || 0;
    const successCnt = currentDcDash.successCnt || 0;
    const totalCnt = currentDcDash.totalCnt || 0;
    let successRate = totalCnt === 0 ? 0 : successCnt / totalCnt;
    errorCodeList.push(<Col key='totalFail' span={2}><Statistic title='总失败数'
                                                                value={totalErrorCnt}
                                                                valueStyle={{fontSize: "18px"}}/></Col>);
    currentDcDash.errorCodeDetailList.forEach(function (item, index, array) {
      if (item.name !== "200") {
        errorCodeList.push(<Col key={'failCode-' + index} span={2}><Statistic
          title={ConvertUtils.getReportDCFailName(item.name) + '占比'}
          value={(item.value / totalErrorCnt * 100).toFixed(2) + '%'}
          valueStyle={{fontSize: "14px"}}/></Col>);
      }
    });
    requestCountList.push(<Col key='totalCnt' span={2}><Statistic title='总请求数' value={totalCnt}
                                                                  valueStyle={{fontSize: "18px"}}/></Col>);
    requestRateList.push(<Col key='totalRate' span={2}><Statistic title='总成功率'
                                                                  value={(successRate * 100).toFixed(2) + '%'}
                                                                  valueStyle={{fontSize: "20px"}}/></Col>);
    for (let item in currentDcDash.apiDetailList) {
      let total = 0;
      let success = 0;
      currentDcDash.apiDetailList[item].forEach(function (item1, index1, array1) {
        total += item1.value;
        if (item1.name === '200') {
          success += item1.value;
        }
      });
      //这里需要统计requestCount和requestRate
      requestCountList.push(<Col key={item} span={2}><Statistic title={ConvertUtils.getReportServletName(item) + "数"}
                                                                value={total}
                                                                valueStyle={{fontSize: "12px"}}/></Col>);
      requestRateList.push(<Col key={item} span={2}><Statistic title={ConvertUtils.getReportServletName(item) + "成功率"}
                                                               value={(success / total * 100).toFixed(2) + '%'}
                                                               valueStyle={{fontSize: "14px"}}/></Col>);
    }


    return (
      <div className={dashStyles.newDiv}>
        <h2>云稳定性</h2>
        <div style={{"display": " -webkit-box", marginTop: "20px"}}>
          <div className={baseStyles.holder} style={{"width": "100%"}}>
            <Row key='dc'>
              {requestCountList}
              <Col span={1}>
                <div className={dashStyles.statisticSlice}></div>
              </Col>
              {requestRateList}
              <Col span={1}>
                <div className={dashStyles.statisticSlice}></div>
              </Col>
              {errorCodeList}
            </Row>
          </div>
        </div>
        <div style={{"display": " -webkit-box", marginTop: "20px"}}>
          <div className={baseStyles.holder} style={{"width": "49%"}}>
            <h4>成功率趋势</h4>
            <DcTrendLinePanel dcHHTrend={dcHHTrend}/>
          </div>
          <div className={baseStyles.holder} style={{"width": "49%", marginLeft: "5px"}}>
            <h4>请求数趋势</h4>
            <DcTrendBarPanel dcHHTrend={dcHHTrend}/>
          </div>
        </div>
      </div>)

  }

  _renderConsole(currentConsoleDash, compareConsoleDash, params) {
    const appKey = params && params.appKey || '';
    const sourcePublishList = [];
    if (currentConsoleDash.sourcePublishList) {
      currentConsoleDash.sourcePublishList.forEach(function (item, index, array) {
        sourcePublishList.push(<Col key={index} span={2}><Statistic title={ConvertUtils.getVersionSourceName(item.item)}
                                                                    value={item.count}/></Col>);
      });
    }
    let nsTotalCnt = 0;
    const nsLoadLevelList = [];
    if (currentConsoleDash.nsLoadLevelList) {
      currentConsoleDash.nsLoadLevelList.forEach(function (item, index, array) {
        nsLoadLevelList.push(<Col key={index} span={2}><Statistic title={ConvertUtils.getLoadLevelName(item.item)}
                                                                  value={item.count}/></Col>);
        nsTotalCnt += item.count;
      });
    }
    const pubProps = {
      title: '发布数',
      current: currentConsoleDash.totalPublishCount || 0,
      compared: compareConsoleDash.totalPublishCount || 0,
      fontSize: '32px',
      margin: "0px 60px 0px 0px",
      suffix: ''
    };
    const nsProps = {
      title: '配置数',
      current: nsTotalCnt,
      fontSize: '32px',
      margin: "0px 60px 0px 0px",
      suffix: ''
    };
    return (
      <div className={dashStyles.newDiv}>
        <h2>控制台</h2>
        <div>
          <div className={baseStyles.holder}>
            <Row key='console2'>
              <Col span={3}>
                <DashboardMetrixColPanel {...pubProps}/>
              </Col>
              {sourcePublishList}
              <Col span={1}>

                <div className={dashStyles.statisticSlice}></div>
              </Col>
              <Col span={3}>
                <DashboardMetrixColPanel {...nsProps}/>
              </Col>
              {nsLoadLevelList}
            </Row>
          </div>
          <div style={{"display": " -webkit-box", marginTop: "20px"}}>
            <div className={baseStyles.holder} style={{"width": "68%"}}>
              <h4>发布趋势</h4>
              <ConsoleTrendLinePanel current={currentConsoleDash.hhPublishList}
                                     compared={compareConsoleDash.hhPublishList} params={params}/>
            </div>
            <div className={baseStyles.holder} style={{"width": "30%", marginLeft: "10px"}}>
              <h4>发布排行</h4>
              < ConsoleCountRankPanel current={currentConsoleDash.nsPublishList}
                                      compared={compareConsoleDash.nsPublishList} appKey={appKey}/>
            </div>

          </div>
        </div>
      </div>)

  }

  _renderUpdateRatePanel(appIntervalDetails, appDayTrend, compareAppDash, currentAppDash, params) {
    return (
      <div className={dashStyles.newDiv}>
        <h2>全局更新</h2>
        <ConfigUpdatePanel appIntervalDetails={appIntervalDetails} appDayTrend={appDayTrend}
                           compareAppDash={compareAppDash} currentAppDash={currentAppDash} params={params}/>
      </div>
    )
  }

  _renderConfigExceptionPanel(currentAppDash, compareAppDash, appHHTrend, params) {
    return (
      <div className={dashStyles.newDiv}>
        <h2>端稳定性</h2>
        <ConfigExceptionPanel compareAppDash={compareAppDash} currentAppDash={currentAppDash} appHHTrend={appHHTrend}
                              params={params}/>
      </div>
    )
  }

  render() {
    const {dispatch, pageData} = this.props;
    const {formData, params} = pageData || {};
    const appList = pageData.appList || [];
    const currentAppDash = pageData.currentAppDash || {};
    const compareAppDash = pageData.compareAppDash || {};
    const currentDcDash = pageData.currentDcDash || {};
    const compareDcDash = pageData.compareDcDash || {};
    const currentConsoleDash = pageData.currentConsoleDash || {};
    const compareConsoleDash = pageData.compareConsoleDash || {};
    const appHHTrend = pageData.appHHTrend || {};
    const dcHHTrend = pageData.dcHHTrend || [];
    const appDayTrend = pageData.appDayTrend || [];
    const appIntervalDetails = pageData.appIntervalDetails || [];
    const onRefresh = (payload) => {
      dispatch({
        type: getFuncName(NAME_SPACE, 'getData'),
        payload,
      });
    };
    const changeFormData = (payload) => {
      dispatch({
        type: getFuncName(NAME_SPACE, 'changeFormData'),
        payload,
      });
    };
    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>
            <Link to={LinkUtils.getAppList()}>应用列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getAppDashboard()}>应用大盘</Link>
          </Breadcrumb.Item>
        </Breadcrumb>

        <div style={{margin: '20px 5px'}}>
          <QueryForm formData={formData} appList={appList} onChange={changeFormData} onRefresh={onRefresh}/>
        </div>
        {this._renderDashBoard(currentDcDash, compareDcDash, currentAppDash, compareAppDash, params)}
        {this._renderConfigExceptionPanel(currentAppDash, compareAppDash, appHHTrend, params)}
        {this._renderDc(currentDcDash, compareDcDash, dcHHTrend)}
        {this._renderConsole(currentConsoleDash, compareConsoleDash, params)}

      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(DashboardReport);
