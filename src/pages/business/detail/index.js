import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';

import {Breadcrumb, Checkbox, Col, Form, Icon, Row} from '@alipay/bigfish/antd';
import {PERMISSION_ROLE_ARR} from '@/constants/business';


import {getFuncName} from '@/utils/utils';
import EditModal from './components/EditModal';

import {LinkUtils, UrlUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {isSystemAdmin} from "@/utils/EnvUtils";
import styles from './index.less';

import namespace from '@/config/namespace';

const CheckboxGroup = Checkbox.Group;


const FormItem = Form.Item;


const NAME_SPACE = namespace.business.detail;

class TypeDetail extends React.Component {
  componentWillMount() {
    const {dispatch, location, match} = this.props;
    const name = (this.props.match && this.props.match.params && this.props.match.params.name) ? this.props.match.params.name : '';
    let payload = {params: {'name': name}};

    dispatch({
      type: getFuncName(NAME_SPACE, 'getDetail'),
      payload,
    });
  }

  _renderTips() {
  }

  _renderDetail(detailDO) {
    const {dispatch} = this.props;
    const isAdmin = isSystemAdmin();
    const onEditSubmit = (formData) => {
      let params = {
        type: 'update',
        permissions: formData.permissionList.join(','),
        name: formData.name,
        token: formData.token,
        owners: formData.owners,
        status: "" + formData.status,
        memo: formData.memo,
      };
      let payload = {params: params, reparams: {name: formData.name}};
      dispatch({
        type: getFuncName(NAME_SPACE, 'updateBusiness'),
        payload,
      });

    };
    return (
      <div>
        <h2>
          基础信息
          <Icon className="hf-ml" type="edit" onClick={() => {
            this.refs.editModal.show();
          }}
          ></Icon>

        </h2>
        <Form className={styles.form}>
          <Row gutter={24}></Row>
          <Row>
            <Col span={12}>
              <FormItem label="id">{detailDO.id}  </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="状态">{ConvertUtils.getBusinessStatusName(detailDO.status)} </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="businessName">{detailDO.name}
                <a href={UrlUtils.getHelpUrl("api")} target="_blank"> API 接入文档</a>
              </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="businessToken">{detailDO.token}  </FormItem>
            </Col>

          </Row>
          <Row>
            <Col span={12}>
              <FormItem label="创建人">{detailDO.creatorNick} </FormItem>
            </Col>
            <Col span={12}>
              <FormItem label="负责人">{detailDO.ownerNick} </FormItem>
            </Col>

          </Row>

          <Row>
            <Col span={24}>
              <FormItem label="权限列表">
                <CheckboxGroup disabled={true} options={PERMISSION_ROLE_ARR} value={detailDO.permissionList}/>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <FormItem label="描述">{detailDO.memo} </FormItem>
            </Col>
          </Row>
        </Form>
        <EditModal ref="editModal" detailDO={detailDO}
                   onHandleOk={onEditSubmit}/>
      </div>);

  }


  render() {
    let that = this;
    const {dispatch, pageData} = this.props;
    const {loading, detail} = pageData;


    const _onDoStatusClick = (appKey, toStatus) => {
      let payload = {
        params: {
          appKey: appKey,
          name: detail.code,
          status: toStatus

        }
      };
      dispatch({
        type: getFuncName(NAME_SPACE, 'updateConnStatus'),
        payload,
      });
    }
    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>管理中心</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getBusinessList()}>Business列表</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>Business详情</Breadcrumb.Item>
        </Breadcrumb>
        <div>
          {that._renderDetail(detail)}
        </div>


      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(TypeDetail);
