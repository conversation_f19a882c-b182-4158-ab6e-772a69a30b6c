import React from 'react';
import {Checkbox, Form, Input, Modal, Select, Spin} from '@alipay/bigfish/antd';

import {BUSINESS_STATUS_ARR, PERMISSION_ROLE_ARR} from '@/constants/business';
import {checkForm} from '@/utils/utils';
import {isSystemAdmin} from "@/utils/EnvUtils";

const CheckboxGroup = Checkbox.Group;
const {Option} = Select;

const FormItem = Form.Item;

const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};

export default class EditAppModal extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    let {detailDO} = this.props;
    let initForm = {};
    if (detailDO) {
      initForm = {
        permissionList: detailDO.permissionList,
        name: detailDO.name,
        token: detailDO.token,
        owners: detailDO.owners,
        status: "" + detailDO.status,
        memo: detailDO.memo,
      }
    }
    return {
      formData: initForm,
      visible: false,
      checking: false,
      loading: false,
    };

  };

  show = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    let that = this;

    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        const {formData} = this.state;
        let {onHandleOk} = this.props;
        onHandleOk && onHandleOk(formData);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    this.setState(this.getInitState());
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);
    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'type': {
          if (!value) {
            help = '必选';
          }
          break;
        }

      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  componentWillReceiveProps(nextProps) {

    if (nextProps.detailDO && JSON.stringify(nextProps.detailDO) != JSON.stringify(this.props.detailDO)) {
      this.changeForm({
        permissionList: nextProps.detailDO.permissionList,
        name: nextProps.detailDO.name,
        token: nextProps.detailDO.token,
        owners: nextProps.detailDO.owners,
        status: "" + nextProps.detailDO.status,
        memo: nextProps.detailDO.memo,
      });
    }

  }


  render() {
    let {getFormProps, changeForm} = this;
    const {visible, formData, loading} = this.state;
    const isAdmin = isSystemAdmin();

    const toString = function (key) {
      return '' + key;
    }
    return (
      <Modal title="修改应用信息" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}>
        <Spin spinning={loading}>
          <Form ref="form" layout="horizontal">
            <FormItem label="name" {...getFormProps('name')}>
              <Input
                value={formData.name}
                disabled={true}
                onChange={(e) => {
                  changeForm({title: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="负责人" {...getFormProps('owners')} help="工号，一人">
              <Input
                value={formData.owners}
                onChange={(e) => {
                  changeForm({owners: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="token" {...getFormProps('token')}>
              <Input
                value={formData.token}
                disabled={!isAdmin}
                onChange={(e) => {
                  changeForm({token: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="描述" {...getFormProps('memo')} help="描述">
              <Input.TextArea
                rows={5}
                value={formData.memo}
                onChange={(e) => {
                  changeForm({memo: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="状态" {...getFormProps('status')} required help="仅管理员可操作">
              <Select
                value={formData.status}
                onChange={(v) => {
                  changeForm({status: v});
                }}
                disabled={!isAdmin}
              >
                {BUSINESS_STATUS_ARR.map((item) => {
                  return <Option key={toString(item.value)} value={toString(item.value)}>{item.label}</Option>;
                })}
              </Select>
            </FormItem>
            <FormItem label="权限" {...getFormProps('permissionList')} required help="接口权限申请">
              <CheckboxGroup
                disabled={!isAdmin}
                options={PERMISSION_ROLE_ARR}
                value={formData.permissionList}
                onChange={(v) => {
                  changeForm({permissionList: v});
                }}
              >
              </CheckboxGroup>
            </FormItem>
          </Form>
        </Spin>
      </Modal>
    );
  }
}
