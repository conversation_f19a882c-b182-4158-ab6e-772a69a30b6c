import React from 'react';
import {connect} from 'dva';
import {Link} from 'dva/router';
import {Breadcrumb} from '@alipay/bigfish/antd';
import {getFuncName} from '@/utils/utils';
import {LinkUtils} from '@/utils/LinkUtils';

import QueryForm from './components/QueryForm';

import List from './components/List';


import namespace from '@/config/namespace';

const NAME_SPACE = namespace.business.list;

class BusinessList extends React.Component {
  componentWillMount() {
    this.props.dispatch({
      type: getFuncName(NAME_SPACE, 'init'),
    });
  }

  render() {
    const {dispatch, pageData} = this.props;
    const {list, formData, pagination, loading, userMap} = pageData;
    const onRefresh = (payload) => {
      dispatch({
        type: getFuncName(NAME_SPACE, 'getData'),
        payload,
      });
    };
    const changeFormData = (payload) => {
      dispatch({
        type: getFuncName(NAME_SPACE, 'changeFormData'),
        payload,
      });
    };


    const onCreateOK = (formData) => {
      let params = {
        type: 'insert',
        name: formData.name,
        memo: formData.memo,
        owners: formData.owners,
        permissions: formData.permissionArr.join(','),
      };
      console.log('params', params);
      let payload = {
        params: params, reparams: {
          name: formData.name
        }
      };
      dispatch({
        type: getFuncName(NAME_SPACE, 'createBusiness'),
        payload,
      });

    };

    if (!pagination.onChange) {
      pagination.onChange = (current) => {
        onRefresh({
          pageNo: current,
        });
      };
    }


    return (
      <div>
        <Breadcrumb className="bread-nav">
          <Breadcrumb.Item>管理中心</Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={LinkUtils.getBusinessList()}>API业务方列表</Link>
          </Breadcrumb.Item>
        </Breadcrumb>
        <QueryForm formData={formData} onChange={changeFormData} onRefresh={onRefresh} onCreateOK={onCreateOK}/>

        <List data={list} userMap={userMap} pagination={pagination} loading={loading}/>
      </div>
    );
  }
}

function mapStateToProps(global) {
  return {
    pageData: global[NAME_SPACE],
  };
}

export default connect(mapStateToProps)(BusinessList);
