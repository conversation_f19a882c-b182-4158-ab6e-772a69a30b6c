import React from 'react';
import {Button, Form, Input, Select} from '@alipay/bigfish/antd';

import {UrlUtils} from '@/utils/LinkUtils';

import CreateModal from './CreateModal';
import styles from './QueryForm.less';

const {Option} = Select;
const FormItem = Form.Item;


export default class QueryForm extends React.Component {
  render() {
    const {formData, onChange, onRefresh, onCreateOK} = this.props;

    return (
      <div className={styles.holder}>
        <Form layout="inline" className="clearfix">
          <FormItem label="name">
            <Input
              value={formData.name}
              className={styles.search}
              placeholder="根据name精确搜索"
              onChange={(e) => {
                onChange({
                  params: {
                    name: e.target.value,
                  },
                });
              }}
            />
          </FormItem>
          <FormItem>
            <Button className="st-mr" onClick={onRefresh}>
              查询
            </Button>
          </FormItem>
          <FormItem>
            <a href={UrlUtils.getHelpUrl("api")} target="_blank"> API 接入文档</a>
          </FormItem>
          <FormItem className="pull-right">
            <Button id="applyButton" style={{'float': 'right'}} type="primary"
                    onClick={() => {
                      this.refs.createModal.show();
                    }}
            >API业务方注册</Button>
          </FormItem>
        </Form>
        <CreateModal ref="createModal" onSubmit={onCreateOK}/>
      </div>
    );
  }
}
