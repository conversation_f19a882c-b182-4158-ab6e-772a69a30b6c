import React from 'react';
import {Table} from '@alipay/bigfish/antd';
import {Link} from 'dva/router';


import {formatTime} from '@/utils/TimeUtils';
import {LinkUtils} from '@/utils/LinkUtils';
import {ConvertUtils} from "@/utils/ConvertUtils";
import {UserUtils} from "@/utils/UserUtils";


import styles from './List.less';


export default class List extends React.Component {
  getColumns = (userMap) => {

    const self = this;
    const columns = [
      {
        title: 'id',
        dataIndex: 'id',
        render(text) {
          return text;
        },
      }, {
        title: 'name',
        dataIndex: 'name',
      },
      {
        dataIndex: 'creator',
        title: '创建者',
        render(text) {
          return <span>{UserUtils.getUserDisplayName(text, userMap)}</span>;
        },
      },  {
        dataIndex: 'owners',
        title: '负责人',
        render(text) {
          return <span>{UserUtils.getUsersDisplayName(text, userMap)}</span>;
        },
      }, {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        render(text) {
          return formatTime(text);
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        render(text) {
          return ConvertUtils.getBusinessStatusName(text);
        }

      },
      {
        title: '操作',
        render(text, record) {
          return (
            <span>
               <Link to={LinkUtils.getBusinessDetail(record.name)}>详情</Link>
            </span>
          );
        },
      }

    ];
    return columns;
  };

  removeItem = () => {
  };

  render() {
    const {loading} = this.props;
    let {data, pagination,userMap} = this.props;
    if (data && data[0]) {
    } else {
      data = [];
    }
    if (pagination && !pagination.showTotal) {
      pagination.showTotal = function(total) {return `总共：${total}`;};
    }
    return (
      <div className={styles.holder}>
        <Table
          loading={loading}
          columns={this.getColumns(userMap)}
          dataSource={data}
          rowKey={record => record.id}
          pagination={pagination}
        />
      </div>
    );
  }
}
