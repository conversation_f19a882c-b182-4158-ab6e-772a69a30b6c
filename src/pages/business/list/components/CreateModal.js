import React from 'react';
import {Checkbox, Form, Input, Modal, Select, Spin} from '@alipay/bigfish/antd';
import {checkForm} from '@/utils/utils';

import {PERMISSION_ROLE_ARR} from '@/constants/business'

const CheckboxGroup = Checkbox.Group;
const {TextArea} = Input;

const {Option} = Select;
const FormItem = Form.Item;
const formLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 17,
  },
};

export default class CreateModal extends React.Component {
  static propTypes = {

    // appList: React.PropTypes.Array,
    //packageDO: React.PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = this.getInitState();
  }

  getInitState = () => {
    return {
      formData: {},
      visible: false,
      checking: false,
      loading: false,
    };
  };

  show = () => {
    this.setState({
      visible: true,
    });
  };

  handleOk = () => {
    let that = this;


    this.setState(
      {
        checking: true,
      },
      () => {
        if (!checkForm(this.refs.form)) {
          return;
        }

        const {formData} = this.state;
        let {onSubmit} = this.props;
        console.log('formData', formData)
        onSubmit && onSubmit(formData);
        this.setState({
          visible: false,
        });
      },
    );
  };

  handleCancel = () => {
    this.setState(this.getInitState());
  };

  changeForm = (params) => {
    const newData = Object.assign({}, this.state.formData, params);

    this.setState({
      formData: newData,
      checking: false,
    });
  };

  getFormProps = (name) => {
    const {checking, formData} = this.state;
    let help = '';
    const value = formData[name];
    if (checking) {
      switch (name) {
        case 'owners': {
          if (!value) {
            help = '必选';
          }
          break;
        }
        case 'name': {
          if (!value) {
            help = '必填';
          }
          break;
        }
        case 'memo': {
          if (!value) {
            help = '必填';
          }
          break;
        }
      }
    }
    return {
      ...formLayout,
      help,
      validateStatus: help ? 'error' : '',
    };
  };

  render() {
    let {getFormProps, changeForm} = this;
    const {visible, formData, loading} = this.state;
    const  memoValue = formData.memo || '业务场景：\n目标APP:\n调用频次:\n附CF审核单：';

    const toString = function (key) {
      return '' + key;
    }

    return (
      <Modal title="申请Business" visible={visible} onOk={this.handleOk} onCancel={this.handleCancel}>
        <Spin spinning={loading}>
          <Form ref="form" layout="horizontal">

            <FormItem label="name" {...getFormProps('name')} required help="请输入aone应用名">
              <Input
                value={formData.name}
                onChange={(e) => {
                  changeForm({name: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="负责人" {...getFormProps('owners')} required help="请输入负责人工号，一个人">
              <Input
                value={formData.owners}
                onChange={(e) => {
                  changeForm({owners: e.target.value});
                }}
              />
            </FormItem>
            <FormItem label="权限" {...getFormProps('permissionArr')} required help="接入CF后方可申请'发布'，失效的选项可联系管理员操作">
              <CheckboxGroup
                options={PERMISSION_ROLE_ARR}
                value={formData.permissionArr}
                onChange={(v) => {
                  changeForm({permissionArr: v});
                }}
              >
              </CheckboxGroup>
            </FormItem>
            <FormItem label="描述" {...getFormProps('memo')} required help="请描述清楚使用场景、调用频次、是否接入CF、方便审核（256字符以内）">
              <TextArea rows={6} value={memoValue} maxLength={256}
                        onChange={(e) => {
                          changeForm({memo: e.target.value});
                        }}
              />
            </FormItem>
          </Form>
        </Spin>
      </Modal>
    );
  }
}
