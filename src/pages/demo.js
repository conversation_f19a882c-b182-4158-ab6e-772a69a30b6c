import React from 'react';
import { DatePicker, Form, Select, Button, Icon } from '@alipay/bigfish/antd';
import CompareCard from '@/components/CompareCard';
import { getDisplayInfo } from '@/utils/utils';
import BaseForm from '@/components/BaseForm';

const FormItem = Form.Item;
const { Option } = Select;

export default class Demo extends React.Component {
  render() {
    const cards = [
      {
        title: '指标1',
        value: 123132,
        valueType: '',
        weekCompare: 0.21,
        dayCompare: -0.34,
        tip: '这是指标1的说明',
      },
      {
        title: '指标2',
        value: 32,
        valueType: 'percent',
        weekCompare: 0.21,
        dayCompare: -0.34,
        tip: '这是指标2的说明',
      },
    ];
    return (
      <div>
        <QueryForm />
        {cards.map(({ value, valueType, ...others }, i) => {
          const info = getDisplayInfo({ type: valueType, value });
          return <CompareCard key={i} value={info.value} suffix={info.suffix} {...others} />;
        })}
      </div>
    );
  }
}

class QueryForm extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showExtra: false,
    };
  }

  render() {
    const { showExtra } = this.state;
    const opt = (
      <div>
        <Button className="mr10" type="primary">
          查询
        </Button>
        <a
          className="expend-btn"
          onClick={() => {
            this.setState({ showExtra: !showExtra });
          }}
        >
          {showExtra ? (
            <span>
              收起 <Icon type="up" />
            </span>
          ) : (
            <span>
              更多 <Icon type="down" />
            </span>
          )}
        </a>
      </div>
    );

    const extra = (
      <div>
        <FormItem label="网络">
          <Select>
            <Option value="WiFi">WiFi</Option>
            <Option value="4G">4G</Option>
            <Option value="3G">3G</Option>
            <Option value="2G">2G</Option>
          </Select>
        </FormItem>
        <FormItem label="命中zcache">
          <Select>
            <Option value="WiFi">WiFi</Option>
            <Option value="4G">4G</Option>
            <Option value="3G">3G</Option>
            <Option value="2G">2G</Option>
          </Select>
        </FormItem>
        <FormItem label="jsBundle分桶大小">
          <Select>
            <Option value="WiFi">WiFi</Option>
            <Option value="4G">4G</Option>
            <Option value="3G">3G</Option>
            <Option value="2G">2G</Option>
          </Select>
        </FormItem>
      </div>
    );
    return (
      <BaseForm className="hf-mb" showExtra={showExtra} extra={extra} opt={opt}>
        <FormItem label="日期">
          <DatePicker />
        </FormItem>
        <FormItem label="版本">
          <Select>
            <Option value="1">1</Option>
            <Option value="2">2</Option>
            <Option value="3">3</Option>
          </Select>
        </FormItem>
      </BaseForm>
    );
  }
}
