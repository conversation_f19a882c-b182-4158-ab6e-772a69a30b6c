import axios from 'axios';
import {message} from 'antd';
import qs from 'qs';
import {getEnv, getObj,} from '@/utils/utils';

import {getBucAppName, isTestEnv} from "@/utils/EnvUtils";


export default async function request(options, handleError) {
  if (options.method.toUpperCase() === 'GET') {
    options.params = Object.assign({}, options.data, options.params);
  } else {
    //post使用form提交，而不是用json
    options.data = qs.stringify(options.data);
  }
  return axios(options).then((response) => {
    let res = {
      success: false,
    };
    try {
      res = response.data;
      if (typeof res == 'string') {
        res = JSON.parse(res);
      }
    } catch (e) {
      message.error('请求异常');
      console.log("request exception", e);
      return;
    }
    if (res.errorCode + "" == "302" || res.errors) {
      if (res.errorMsg && res.errorMsg.indexOf("//") > -1) {
        window.location.href = res.errorMsg;
        return;
      }
      const orangeAppName = getBucAppName();
      let backURL = encodeURIComponent(window.location.href);
      const protocol = window.location.href.indexOf("https://") > -1 ? "https" : "http";
      let loginHost = (isTestEnv()) ? "login-test.alibaba-inc.com" : "login.alibaba-inc.com";
      let url = `https://${loginHost}/ssoLogin.htm?APP_NAME=${orangeAppName}&BACK_URL=${backURL}`;
      console.log('url', url);
      window.location.href = url;
      return;

    }

    if (res.success != true) {
      console.log(res);
      if (handleError) {
        handleError(res);
      } else {
        let msg = res.errorMsg || '请求异常了';
        message.error(`${msg}(${res.errorCode})`);
      }
      return;
    }

    const module = getObj(getObj(res).model);
    return module;
  }).catch(function (error) {
    // handle error
    console.log(error);
    if (handleError) {
      handleError({"errorCode": "NETWORK_ERROR", "errorMsg": "网络异常了" + error});

    } else {
      let msg = error || '未知原因';
      message.error(`请求异常了：${msg}`);
    }
  })
    ;

}
