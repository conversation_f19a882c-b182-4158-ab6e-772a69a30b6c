import {
  addNamespace,
  getNamespace,
  loadLevelSubmitAuditing,
  offlineNamespace,
  queryKnockoutForOfflineNamespace,
  updateNamespace,
  availableNamespace
} from '@/services/namespace';
import {getArr, getObj} from "./utils";
import {LinkUtils} from '@/utils/LinkUtils';


export const Edit = {
  effects: {
    * createOrUpdateNamespace({payload: {data}}, {call, put}) {
      if (data.needAuditing) {
        const res = yield call(loadLevelSubmitAuditing, data);
        yield put({
          type: 'updateState',
          name: 'namespace',
          payload: {
            namespace: getObj(res),
            ...res
          },
        });
      } else {
        if (data.namespaceId) {
          yield call(updateNamespace, data);
        } else {
          yield call(addNamespace, data);
        }
      }
      yield put({type: 'reload'});
    },
    * getNamespace({payload}, {call, put}) {
      const res = yield call(getNamespace, payload);
      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          namespace: getObj(res),
          ...payload
        },
      });
    },
    * offlineNamespace({payload}, {call, put}) {
      const res = yield call(offlineNamespace, payload);
      const hash = LinkUtils.getMyNamespaceList();
      if (res && ('#'+hash) !=  location.hash) {
        location.hash = hash;
      }
      yield put({type: 'reload'});
    },
    * availableNamespace({payload}, {call, put}) {
      yield call(availableNamespace, payload);
      yield put({type: 'reload'});
    },
    * queryKnockoutForOfflineNamespace({payload}, {call, put}) {
      const res = yield call(queryKnockoutForOfflineNamespace, payload);
      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          knockoutNamespaceVersion: getArr(res),
          ...payload
        },
      });
    },
    * reload() {
    }
  },
};
