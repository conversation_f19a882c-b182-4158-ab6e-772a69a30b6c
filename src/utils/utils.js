/* global window */
/* global location */
import ReactDOM from 'react-dom';
import qs from 'qs';
import moment from 'moment';
import {getTimestamps} from '@/utils/TimeUtils';
import {Form} from 'antd';

const FormItem = Form.Item;


const DEFAULT_PAGE_SIZE = 10;


export function getValidStr(origin) {
  if (origin !== undefined && origin !== null) {
    if (typeof origin !== 'string') {
      origin = String(origin);
    }
    return origin.trim();
  }
  return '';
}

export function getArr(origin) {
  if (Array.isArray(origin)) {
    return origin;
  }
  return [];
}

export function getObj(origin) {
  return origin || {};
}

export function getFuncName(namespace, type) {
  return `${namespace}/${type}`;
}

export function getValidArr(origin) {
  let values = [];
  if (Array.isArray(origin)) {
    if (origin.length !== 1 || origin[0] !== '') {
      values = origin;
    }
  }
  return values;
}

export function getValidObj(origin) {
  try {
    if (typeof origin == 'string') {
      return origin ? JSON.parse(origin) : {};
    }
    return origin;
  } catch (e) {
    console.log('getValidObj error', origin);
    return {};
  }
}

function getHandleParams({target, cfg, handler}) {
  const params = {};
  Object.keys(cfg).forEach((key) => {
    params[key] = handler({value: target[key], type: cfg[key]});
  });
  return params;
}

export function getEncodeParams({target, cfg}) {
  return getHandleParams({target, cfg, handler: getEncodeValue});
}

export function getEncodeValue({value, type}) {
  switch (type) {
    case 'array': {
      return getValidArr(value).join(',');
    }
    case 'time': {
      return getTimestamps(value);
    }
    case 'object': {
      if (value && typeof value !== 'string') {
        return JSON.stringify(value);
      }
      return '{}';
    }
    default: {
      return value;
    }
  }
}

export function getDecodeParams({target, cfg}) {
  return getHandleParams({target, cfg, handler: getDecodeValue});
}

export function getDecodeValue({value, type}) {
  switch (type) {
    case 'array': {
      return getValidArr(getValidStr(value).split(','));
    }
    case 'time': {
      const v = moment(Number(value));
      if (value && v.isValid()) {
        return v;
      }
      return null;
    }
    case 'object':
      return getValidObj(value);
    case 'number':
      return Number(value);
    default: {
      return value;
    }
  }
}

export function getHashPath() {
  let url = getValidStr(window.location.hash);
  const qIndex = url.indexOf('?');
  if (qIndex > -1) {
    url = url.slice(0, qIndex);
  }
  if (url[0] === '#') {
    url = url.slice(1);
  }
  return url;
}

export function getSearch(origin) {
  const str = getValidStr(origin);
  const index = str.indexOf('?');
  if (index > -1) {
    return str.slice(index + 1);
  }
  return str;
}

export function getUrlParams(url) {
  const search = getSearch(url || global.window.location.href);
  return qs.parse(search);
}

export function getHashQueryParams(hashUrl) {
  const url = hashUrl || global.window.location.hash;
  return getUrlParams(url);
}

export function getCheckedFormData({cfg, defaultData}) {
  const target = getDecodeParams({
    target: getHashQueryParams(),
    cfg,
  });

  let needRefresh = false;
  const newFormData = {};
  Object.keys(cfg).forEach((key) => {
    const type = cfg[key];
    let v = target[key];
    switch (type) {
      case 'array': {
        if (!getArr(v).length) {
          v = getArr(defaultData[key]);
          needRefresh = true;
        }
        break;
      }
      case 'object': {
        if (!v || !Object.keys(v).length) {
          v = defaultData[key];
          needRefresh = true;
        }
        break;
      }
      default: {
        if (!v) {
          v = defaultData[key];
          needRefresh = true;
        }
      }
    }
    newFormData[key] = v;
  });
  return {
    needRefresh,
    newFormData,
  };
}

export function checkForm(ref) {
  const form = ReactDOM.findDOMNode(ref);
  if (form && !form.querySelector('.has-error')) {
    return true;
  }
  return false;
}

export function getResolvedListData(data, query) {
  const json = getObj(data);
  const state = {
    loading: false,
    list: getArr(json.content || json.list),
    pagination: getPagination(json, query),
  };
  return state;
}

export function getResolvedData(data) {
  const state = {
    loading: false,
    detail: getObj(data),
  };
  return state;
}

export function getResolvedDimensions(data) {
  return {
    dimensions: data || {},
  };
}

export function getPagination(res = {}, query = {}) {
  return {
    current: res.curPage || 1,
    pageSize: res.pageSize || DEFAULT_PAGE_SIZE,
    total: res.total || 0
  };
}

export function getListReqParams(origin) {
  const params = Object.assign({}, origin);
  if (!params.pageNo) {
    params.pageNo = 1;
  }
  if (!params.pageSize) {
    params.pageSize = DEFAULT_PAGE_SIZE;
  }
  return params;
}

export function getFileUrl(ossKey) {
  const env = getEnv();
  if (env === 'LOCAL' || env === 'DAILY') {
    return `http://fregata-open-daily.oss-cn-shanghai.aliyuncs.com/${ossKey}`;
  }
  return `https://ossgw.alicdn.com/fregata-open/${ossKey}`;
}

export function getContentFormat(namespaceBO) {
  return namespaceBO.type === 3 && namespaceBO.subType === 1 ? 'json' : 'other';
}

export function myParseJSON(str) {
  if (typeof str == 'string') {
    try {
      return str ? JSON.parse(str) : {};
    } catch (e) {
      return null;
    }
  }
  return {};

}

export function getApproximateNumberStr(count) {
  if (count === undefined || count == null || typeof count !== 'number') {
    return '未知';
  }

  if (count >= 100000000) {
    return `${(count / 100000000).toFixed(0)} 亿+`;
  } else if (count >= 10000) {
    return `${(count / 10000).toFixed(0)} 万+`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(0)} 千+`;
  } else if (count >= 100) {
    return `${count / 100} 百+`;
  }
  return `${count}`;
}

export function isSubstring(keywords, value) {
  const lowerKeywords = keywords.toLowerCase();
  const lowerValue = value.toLowerCase();

  if (lowerValue.includes(lowerKeywords)) {
    return true;
  }

  let i = 0;
  let j = 0;

  while (i < lowerKeywords.length && j < lowerValue.length) {
    if (lowerKeywords[i] === lowerValue[j]) {
      i++;
    }
    j++;
  }

  return i === lowerKeywords.length;
}
