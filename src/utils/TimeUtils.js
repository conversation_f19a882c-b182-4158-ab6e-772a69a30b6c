import moment from 'moment';

const DS_FORMAT_REPORT = "yyyyMMdd";
const DS_FORMAT_TIME = "yyyy-MM-dd hh:mm:ss";


export function dateFormat(date, fmt) {
  var o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    "S": date.getMilliseconds() //毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  return fmt;
}


export function getTimestamps(origin) {
  let time = moment(origin);

  // 是数字
  if (!isNaN(Number(origin))) {
    time = moment(Number(origin));
  }
  if (!origin || !time.isValid()) {
    return 0;
  }
  return new Date(time).getTime();
}

export function formatDateWithGap(gap, format = DS_FORMAT_REPORT) {
  var time = new Date().getTime() + gap * 24 * 60 * 60 * 1000;
  return (dateFormat(new Date(time), format));
}


export function formatDateWithDate(date, format = DS_FORMAT_REPORT) {
  var time = date.getTime();
  return dateFormat(time, format);
}


export function formatTime(time, format = DS_FORMAT_TIME) {
  var d = new Date(time);
  return dateFormat(d, format);
}

export function getTrendRange(range) {
  const begin = formatDateWithGap(-8)
  const end = formatDateWithGap(-1)
  return [begin, end];

}

export function getTrendRangeDays() {
  var days = [];
  for (var i = -8; i < 0; i++) {
    var dateStr = formatDateWithGap(i)
    days.push(dateStr);
  }
  return days;
}

export function getTrendRangeHours(dayStr) {
  let hhs = [];
  for (let i = 0; i < 23; i++) {
    hhs.push(`${('' + i).padStart(2, '0')}:00`)
  }
  return hhs;
}

export function getDayOfMills(date) {
  return date.getHours() * 3600 * 1000
    + date.getMinutes() * 60 * 1000
    + date.getSeconds() * 1000
    * date.getMilliseconds();
}