'use strict';
import {UrlUtils} from '@/utils/LinkUtils';


export const UserUtils = {

  getUserSelectOption: (userList, appendOperator) => {
    const {userName, userId} = window._DATA_;
    if (!userList || !userList[0]) {
      return appendOperator ? [{
        empId: userId,
        displayName: userName
      }] : [];
    }
    return userList;
  },
  getUserInitValue: (userList, appendOperator) => {
    const {userName, userId} = window._DATA_;
    if (!userList || !userList[0]) {
      return appendOperator ? [userId] : [];
    }
    const ret = userList.map(user => user.empId);
    return ret;
  },

  getUsersDisplayName: (users, userMap) => {
    if (!users) {
      return '-';
    }
    if (!userMap) {
      return users;
    }
    const userArray = users.split(",");
    const listItems = userArray.map((item) =>
      <span key={'user_' + item}> {UserUtils.getUserDisplayName(item, userMap)}</span>
    );

    return <div>
      {listItems}
    </div>;
  },
  getUserDisplayName: (user, userMap) => {
    if (!user) {
      return '-'
    }
    if (user == 'undefined') {
      return '-'
    }
    if (userMap && userMap[user]) {
      return <a href={UrlUtils.getWorkUrl(user)} target="_blank">{userMap[user].displayName}</a>;
    }
    return user;
  },
  hasNsEditPermission: (owners) => {

    const {isAdmin, emailPrefix, userName, userId} = window._DATA_;
    if (isAdmin && isAdmin === 'true') {
      return true;
    }
    if (!owners) {
      return false;
    }

    if (userId && owners.indexOf(userId) > -1 || userName && owners.indexOf(userName) > -1 || emailPrefix && owners.indexOf(emailPrefix) > -1) {
      return true
    }
    return false;
  },
  hasNsTestPermission: (testers) => {

    const {isAdmin, emailPrefix, userName, userId} = window._DATA_;
    if (isAdmin && isAdmin === 'true') {
      return true;
    }
    if (!testers) {
      return false;
    }

    if (userId && testers.indexOf(userId) > -1 || userName && testers.indexOf(userName) > -1 || emailPrefix && testers.indexOf(emailPrefix) > -1) {
      return true
    }
    return false;
  },
  findUserSetOfBusinessList(bsList) {
    if (bsList && bsList[0]) {
      let users = new Set();

      bsList.forEach(item => {
        if (item.owners) {
          item.owners.split(',').forEach(item => {
            users.add(item);
          })
        }
        if (item.creator) {
          users.add(item.creator);
        }
      });
      const empIds = [...users].join(",");
      return empIds;
    } else {
      return null;
    }
  }
};
