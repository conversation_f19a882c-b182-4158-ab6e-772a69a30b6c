'use strict';

export const VersionUtils = {

    getVersion: (list, version) => {
        if (!list || !list[0]) {
            return null;
        }
        let result = list.filter((item) => {
            return item.version == version;
        });
        return result[0];

    },
    getDefaultVersion: (list) => {
        if (!list || !list[0]) {
            return null;
        }
        let result = list.filter((item) => {
            return item.appVersion == "*" && !item.strategy;
        });
        return result[0];

    },
    sortVersionList: (list) => {
        if (!list || list.length <= 1) {
            return list;
        }
        list.sort(function (a, b) {
            if (a.appVersion == "*" && b.appVersion != "*") {
                return 1;

            } else if (a.appVersion != "*" && b.appVersion == "*") {
                return -1;
            } else if (a.strategy && !b.strategy) {
                return -1;
            } else if (!a.strategy && b.strategy) {
                return 1;
            } else {
                return b.id - (a.id);
            }

        });
    },
    sortXXVersions(versions) {
        versions = versions || [];
        const flatV = (s) => s.split(/\W+/).map((s) => s.padStart(10, 0)).join('-');
        versions.sort((a, b) => flatV(a).localeCompare(flatV(b)));
        return versions;
    },
    getAppName(appKey, appList) {
        if (!appKey || !appList) {
            return appKey;
        }
        let appName = appKey;
        appList.forEach(item => {
            if (item.appKey == appKey) {
                appName = item.appName;
                return appName;
            }
        })
        return appName;
    },
    findUserOfVersionList(versionList) {
        if (versionList) {
            let users = new Set();
            versionList.forEach(item => {
                users.add(item.creator);
                if (item.reviewer) {
                    users.add(item.reviewer);
                }
            });
            const empIds = [...users].join(",");
            return empIds;
        } else {
            return null;
        }
    },

    getVersionApiBusinessName: (sourceData) => {
        if (!sourceData || sourceData.indexOf("{") < 0) {
            return "system";
        }
        const data = JSON.parse(sourceData);
        return data && data.businessName ? data.businessName : "system";
    }


};
