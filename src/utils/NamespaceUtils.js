'use strict';


export const NamespaceUtils = {
    getContentFormat(namespaceBO) {
        return namespaceBO.type === 3 && namespaceBO.subType === 1 ? 'json' : 'other';
    },
    findUserOfMyNsList(myNsView) {
        if (myNsView && myNsView[0]) {
            let users = new Set();

            myNsView.forEach(appNsList => {
                const nsList = appNsList.namespaceList;
                if (nsList && nsList[0]) {
                    const each = NamespaceUtils.findUserSetOfNsList(nsList);
                    each.forEach(item => {
                        users.add(item);
                    })
                }
            });
            return [...users];
        } else {
            return null;
        }
    },
    findModuleOfMyNsList(myNsView) {
        if (myNsView && myNsView[0]) {
            let modules = [];
            myNsView.forEach(appNsList => {
                modules = modules.concat(NamespaceUtils.findModuleListOfNsList(appNsList.namespaceList));
            });
            return Array.from(new Set(modules));
        } else {
            return null;
        }
    },
    findModuleListOfNsList(nsList) {
        let modules = [];
        if (nsList && nsList[0]) {
          nsList.forEach(item => {
              if (item.modules) {
                modules = modules.concat(item.modules.split(','));
              }
          });
        }
        return Array.from(new Set(modules));
    },
    findUseListOfNsList(nsList) {
        const users = NamespaceUtils.findUserSetOfNsList(nsList);
        return users ? [...users] : null;
    },
    findUserSetOfNsList(nsList) {
        if (nsList && nsList[0]) {
            let users = new Set();

            nsList.forEach(item => {
                if (item.owners) {
                    item.owners.split(',').forEach(item => {
                        users.add(item);
                    })
                }
                if (item.testers) {
                    item.testers.split(',').forEach(item => {
                        users.add(item);
                    })
                }
            });
            return users;
        } else {
            return null;
        }
    }
}
