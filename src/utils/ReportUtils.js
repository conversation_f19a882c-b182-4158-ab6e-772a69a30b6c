import {getObj} from '@/utils/utils';
import moment from 'moment';

const dateFormat = 'YYYY-MM-DD HH:mm:ss';

export function addTimeUnitAndClear(dataQuery) {
  dataQuery.dimValues = (dataQuery.dimValues || [])
    .filter((dim) => dim.dimValues && dim.dimValues.length > 0);
  let offset = dataQuery.endTime - dataQuery.startTime;
  if (offset <= 12 * 3600 * 1000) {
    dataQuery.unit = 'MINUTES';
  } else if (offset <= 15 * 24 * 3600 * 1000) {
    dataQuery.unit = 'HOURS';
  } else {
    dataQuery.unit = 'DAYS';
  }
}

export function getMotuObj(res) {
  return getObj(res).success ? getObj(res) : {};
}

export function toDateValues(dpData) {
  dpData = dpData || {};
  return Object.entries(dpData).map(v => {
    let dateStr = moment(parseInt(v[0])).format(dateFormat);
    return {value: [dateStr, parseInt(v[1])]};
  });
}

export function dateValuesToMap(dateValues) {
  dateValues = dateValues || [];
  return dateValues.reduce((m, {value}) => {
    m[value[0]] = value[1];
    return m;
  }, {});
}

export function getFirstValues(res) {
  if (
    res &&
    (res.data && res.data.length > 0 && res.data[0].values && res.data[0].values.length > 0)
  ) {
    return res.data[0].values[0].values || {};
  }
  return {};
}

export function deepCopy(obj) {
  return JSON.parse(JSON.stringify(obj));
}