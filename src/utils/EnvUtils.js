'use strict';

const ENV_ONLINE = "ONLINE";
const ENV_PRE = "PRE";
const ENV_DAILY = "DAILY";
const ENV_LOCAL = "LOCAL";


const ENV_ONLINE_NAME = "线上";
const ENV_PRE_NAME = "预发";
const ENV_DAILY_NAME = "日常";
const ENV_LOCAL_NAME = "本地";


const GROUP_ALI = "ALI";
const GROUP_YOUKU = "YOUKU";
const GROUP_LZD = "LZD";
const GROUP_AE = "AE";


export const ENV_GROUP_HOSTS = [
  {
    host: "orange-console-daily.alibaba.net",
    env: ENV_DAILY,
    envName: ENV_DAILY_NAME,
    group: GROUP_ALI,
    label: '阿里集群-日常环境',
    protocol: 'https',
    appKey:'4272'
  }, {
    host: "orange-console-pre.alibaba-inc.com",
    env: ENV_PRE,
    envName: ENV_PRE_NAME,
    group: GROUP_ALI,
    label: '阿里集群-预发环境',
    protocol: 'https',
    appKey:'21646297'
  }, {
    host: "orange-console.alibaba-inc.com",
    env: ENV_ONLINE,
    envName: ENV_ONLINE_NAME,
    group: GROUP_ALI,
    label: '阿里集群-线上环境',
    protocol: 'https',
    appKey:'21646297'
  },
  {
    host: "orange-console-daily.heyi.test",
    env: ENV_DAILY,
    envName: ENV_DAILY_NAME,
    group: GROUP_YOUKU,
    label: 'YOUKU集群-日常环境',
    protocol: 'https',
    appKey:'60032872'
  }, {
    host: "orange-console-pre.youku.com",
    env: ENV_PRE,
    envName: ENV_PRE_NAME,
    group: GROUP_YOUKU,
    label: 'YOUKU集群-预发环境',
    protocol: 'https',
    appKey:'23570660'
  }, {
    host: "orange-console.youku.com",
    env: ENV_ONLINE,
    envName: ENV_ONLINE_NAME,
    group: GROUP_YOUKU,
    label: 'YOUKU集群-线上环境',
    protocol: 'https',
    appKey:'23570660'
  },
  {
    host: "global-orange-console-daily.lazada.test",
    env: ENV_DAILY,
    envName: ENV_DAILY_NAME,
    group: GROUP_LZD,
    label: 'Lazada集群-日常环境',
    protocol: 'https',
    appKey:'60039086'
  }, {
    host: "pre-global-orange-console-lazada.alibaba-inc.com",
    env: ENV_PRE,
    envName: ENV_PRE_NAME,
    group: GROUP_LZD,
    label: 'Lazada集群-预发环境',
    protocol: 'https',
    appKey:'23867946'
  }, {
    host: "global-orange-console-lazada.alibaba-inc.com",
    env: ENV_ONLINE,
    envName: ENV_ONLINE_NAME,
    group: GROUP_LZD,
    label: 'Lazada集群-线上环境',
    protocol: 'https',
    appKey:'23867946'
  },
  {
    host: "global-orange-console-daily.alibaba.net",
    env: ENV_DAILY,
    envName: ENV_DAILY_NAME,
    group: GROUP_AE,
    label: '国际化集群-日常环境',
    protocol: 'https',
    appKey:'60039086'
  }, {
    host: "pre-global-orange-console.alibaba-inc.com",
    env: ENV_PRE,
    envName: ENV_PRE_NAME,
    group: GROUP_AE,
    label: '国际化集群-预发环境',
    protocol: 'https',
    appKey:'21371581'
  }, {
    host: "global-orange-console.alibaba-inc.com",
    env: ENV_ONLINE,
    envName: ENV_ONLINE_NAME,
    group: GROUP_AE,
    label: '国际化集群-线上环境',
    protocol: 'https',
    appKey:'21371581'
  }
];

export function getConfig() {
  const host = location.hostname;

  for (let i = 0; i < ENV_GROUP_HOSTS.length; i++) {
    if (ENV_GROUP_HOSTS[i].host == host) {
      return ENV_GROUP_HOSTS[i];
    }
  }
  if (host == 'orange.m.alibaba-inc.com') {
    return ENV_GROUP_HOSTS[2];
  }
  if (host == 'orange-console-daily.lazada.test' || host == 'orange-console-daily.lazada.alibaba.net'  || host == 'global-orange-console-daily.lazada.test' ) {
    return ENV_GROUP_HOSTS[6];
  }
  if (host == 'orange-console-lazada-pre.alibaba-inc.com' || host == 'orange-console-pre.lazada.alibaba.net' || host=='pre-global-orange-console-lazada.alibaba-inc.com') {
    return ENV_GROUP_HOSTS[7];
  }
  if (host == 'orange-console-lazada.alibaba-inc.com' || host == 'orange-console.lazada.alibaba-inc.com' || host == 'global-orange-console-lazada.alibaba-inc.com') {
    return ENV_GROUP_HOSTS[8];
  }
  return {
    host: "orange-console-daily.alibaba.net",
    env: ENV_LOCAL,
    envName: ENV_LOCAL_NAME,
    group: GROUP_ALI,
    label: '阿里集群-本地环境',
    protocol: 'http',
    appKey:'4272'
  }

}

/**
 * 获取当前环境
 *  PRD 线上
 *  PRE 预发
 *  TEST 日常
 *  LOCAL 本地
 * @returns {string}
 */
export function getEnv() {
  const config = getConfig();
  return config.env;
}

export function getGroup() {
  const config = getConfig();
  return config.group;
}

export function isPreEnv() {
  const env = getEnv();
  return (env == ENV_PRE);
}

export function isOnlineEnv() {
  const env = getEnv();
  return (env == ENV_ONLINE);
}

export function isTestEnv() {
  const env = getEnv();
  return (env == ENV_DAILY || env == ENV_LOCAL);
}


export function isAliGroup() {
  const group = getGroup();
  return (group == GROUP_ALI);
}


export function isYoukuGroup() {
  const group = getGroup();
  return (group == GROUP_YOUKU);
}


export function isLazadaGroup() {
  const group = getGroup();
  return (group == GROUP_LZD);
}

export function getBucAppName() {
  const group = getGroup();
  if (group == GROUP_LZD) {
    return "orange-console-lazada";
  }
  return "orange-console";
}

export function getEnvName() {
  const config = getConfig();
  return config.envName;
}

export function getLabel() {
  const config = getConfig();
  return config.label;
}
export function getDefaultAppKey() {
  const config = getConfig();
  return config.appKey;
}

export function isSystemAdmin() {
  const {isAdmin} = window._DATA_;
  return isAdmin && isAdmin === 'true';
}


export function getUrl(config) {
  let url = config.protocol + "://" + config.host + "/index.htm";
  const hash = location.hash;
  if (hash.indexOf('list') > -1) {
    return url + hash;
  }
  return url;
}
