'use strict';

import {getValidStr} from "./utils";

export function getFixedNum(origin, dicimal = 2) {
  const num = Number(origin).toFixed(dicimal);
  return num;
}

export function getDisFixed(origin, dicimal = 2) {
  if (origin == null) {
    return '-';
  }
  const num = Number(origin).toFixed(dicimal);
  return num;
}

// 三位分隔
export function getDisplayNum(origin) {
  if (isNaN(Number(origin))) {
    return '-';
  }
  let displayNum = '';
  let str = getValidStr(origin);
  let decimal = '';
  const decimalIndex = str.indexOf('.');
  if (decimalIndex > -1) {
    decimal = str.slice(decimalIndex);
    str = str.slice(0, decimalIndex);
  }
  const {length} = str;
  const lastIndex = length - 1;
  for (let i = 0; i < length; i += 1) {
    displayNum += str[i];
    if (i !== lastIndex && (lastIndex - i) % 3 === 0) {
      displayNum += ',';
    }
  }
  return displayNum + decimal;
}

const DEFAULT_DISPLAY_VALUE_HANDLER = ({value}) => {
  return {
    value: getDisplayNum(value),
    suffix: '',
  };
};

const DISPLAY_VALUES_HANLDERS = {
  num({value}) {
    const num = ((value == null) ? '—' : value);
    return {
      value: num,
      suffix: '',
    };
  },
  ms({value}) {
    const num = getDisplayNum(value);
    let suffix = 'ms';
    if (num === '-') {
      suffix = '';
    }
    return {
      value: num,
      suffix,
    };
  },
  kb({value}) {
    const num = getDisplayNum(value);
    let suffix = 'KB';
    if (num === '-') {
      suffix = '';
    }
    return {
      value: num,
      suffix,
    };
  },
  percent({value}) {
    let num = Math.round(parseFloat(value) * 10000) / 100;
    let suffix = '%';
    if (isNaN(num)) {
      num = '-';
      suffix = '';
    }
    return {
      value: num,
      suffix,
    };
  },
  percentStr(params) {
    let percent = DISPLAY_VALUES_HANLDERS.percent(params);
    return `${percent.value}${percent.suffix}`;
  },
  default: DEFAULT_DISPLAY_VALUE_HANDLER,
};

export function getDisplayInfo(params) {
  let {type} = params;
  if (!(type in DISPLAY_VALUES_HANLDERS)) {
    type = 'default';
  }
  return DISPLAY_VALUES_HANLDERS[type](params);
}

export function showBooleanValue(value) {
  if (value == undefined || value == null) {
    return ''
  }
  return '' + value;
}

export function getDefaultValue(obj, name) {
  return obj ? obj[name] : 0;
}

export function getDefaultPercentValue(obj, name) {
  let rate = obj ? obj[name] : 0;
  return parseInt(rate * 10000) / 100;
}

export function getDefaultLongValue(obj, name) {
  let value = obj ? obj[name] : 0;
  return value || 0;
}


export function isEmpty(obj) {
  if (typeof obj == "undefined" || obj == null || obj.trim() === "" ) {
    return true;
  } else {
    return false;
  }
}
