'use strict';
import namespace from '@/config/namespace';
import { getEnv, isLazadaGroup, isOnlineEnv, isPreEnv, isTestEnv, isYoukuGroup } from '@/utils/EnvUtils';


export const LinkUtils = {
    getHome: () => {
        return namespace.home.index;
    },
    getBusinessList: () => {
        return namespace.business.list;
    },
    getBusinessDetail: (name) => {
        if (name == undefined) {
            return "";
        }
        return namespace.business.detail + "" + name;
    },
    getAppList: () => {
        return namespace.app.list;
    },
    getAppDetail: (appKey) => {
        if (appKey == undefined) {
            return "";
        }
        return namespace.app.detail + "" + appKey;
    },
    getVersionList: () => {
        return namespace.version.list;
    },
    getVersionListOfNs: (name, appKey) => {
        return namespace.version.list + "?source=-1&name=" + name + "&appKey=" + appKey;
    },
    getNamespaceList: (appKey) => {
        return namespace.namespace.list + "?appKey=" + appKey;
    },
    getNamespaceDetail: (namespaceId, version) => {
        if (namespaceId == undefined) {
            return "";
        }
        if (!version) {
            return namespace.namespace.detail + "" + namespaceId;
        }

        return namespace.namespace.detail + "" + namespaceId + "-" + (version || '');
    },
    getNamespaceRevise: (namespaceId) => {
        if (namespaceId == undefined) {
            return "";
        }
        return namespace.namespace.revise + "" + namespaceId;
    },
    getMyNamespaceList: () => {
        return namespace.namespace.me;
    },
    getVersionDetail: (namespaceId, version) => {
        if (namespaceId == undefined || version == undefined) {
            return "";
        }
        return namespace.version.detail + "" + namespaceId + "-" + version;
    },
    getDebugDetail: (namespaceId) => {
        if (namespaceId == undefined) {
            return "";
        }
        return namespace.version.debug + "" + namespaceId;
    },
    getPublishTrace: (namespaceId, version) => {
        return namespace.report.trace + `?namespaceId=${namespaceId}&version=${version}`;
    },
    getNamespaceReport: (namespaceId) => {
        if (namespaceId == undefined) {
            return "";
        }
        return namespace.report.namespace + `?namespaceId=${namespaceId}`;
    },
    getAppDashboard: (appKey) => {
        if (appKey) {
            return namespace.report.dashboard + `?appKey=${appKey}`;
        } else {
            return namespace.report.dashboard;
        }

    },
};

const OLD_PATH_2_NEW_PATH = {
    '#/namespace/namespace/me': '#/workspace/namespace/me',
    '#/namespace/namespace/list': '#/workspace/namespace/list',
    '#/namespace/version/list': '#/workspace/version/list',
    '#/setting/app/list': '#/workspace/setting/app',
    '#/setting/business/list': '#/workspace/setting/business',
    '#/index': '#/',
};

function getTigaReleaseHost() {
  if (isOnlineEnv()) {
    return 'tiga-release.alibaba-inc.com';
  }
  if (isPreEnv()) {
    return 'tiga-release-pre.alibaba-inc.com';
  }
  return 'tiga-release-daily.alibaba.net';
}

export const UrlUtils = {
    getNewPlatformUrl: () => {
        const { hash } = location;
        const oldPath = hash.split('?')[0].replace(/^\/+|\/+$/g, '');
        let newPath = OLD_PATH_2_NEW_PATH[oldPath];
        if (!newPath) {
            let match = oldPath.match(/\/namespace\/namespace\/detail\/([a-f0-9]+)$/);
            if (match && match[1]) {
                newPath = `#/workspace/namespace/detail?namespaceId=${match[1]}`;
            }
            match = oldPath.match(/\/namespace\/version\/detail\/([a-f0-9]+)-([0-9]+)$/);
            if (match && match[1] && match[2]) {
                newPath = `#/workspace/version/detail?namespaceId=${match[1]}&version=${match[2]}`;
            }
        }
        return newPath;
    },
    getResourceUrl: (resourceId) => {
        if (isTestEnv()) {
            return `https://orange.waptest.taobao.com/${resourceId}`;
        }
        if (isLazadaGroup()) {
            return `https://laz-dorangesource.alicdn.com/${resourceId}`;
        }
        if (isYoukuGroup()) {
            return `https://dorangesource.youku.com/${resourceId}`;
        }
        if (isPreEnv()) {
            return `https://dorangesource-pre.alicdn.com/${resourceId}`;

        }
        return `https://dorangesource.alicdn.com/${resourceId}`;
    },
    getNamespaceUrl: (namespaceId, version) => {
        namespaceId = namespaceId || '';
        version = version || '';
        return "/namespace.htm?namespaceId=" + namespaceId + '&version=' + version;
        //return `http://dorangesource.alicdn.com//${resourceId}`;
    },
    getOldDebugUrl: (appKey) => {
        appKey = appKey || '';
        return '/indexList.htm?appKey=' + appKey;
        //return `http://dorangesource.alicdn.com//${resourceId}`;
    },
    getCrashUrl(appId) {
        if(!appId){
            return 'https://ha.emas.alibaba-inc.com/#/page/crash';
        }
        return `https://ha.emas.alibaba-inc.com/#/page/crash?r_=/overview/:appId&g_={"appId":"${appId}"}`;
    },
    getDp2Url() {
        return 'https://dp2.motu.alibaba-inc.com/#/dashboard';
    },
    getTlogUrl() {
        return 'https://ha.emas.alibaba-inc.com/#/page/tlog';
    },
    getAcccsUrl() {
        return 'https://max.m.taobao.com/accs/view/mainPage#/logs?_k=bnzhvj';
    },
    getWmccUrl: (wmccId) => {
        if (isPreEnv()) {
            return `https://pre-wmcc.alibaba-inc.com/diamond/beta-publish#/dashboard/monitor/${wmccId}`;
        }
        return `https://wmcc.alibaba-inc.com/diamond/beta-publish#/dashboard/monitor/${wmccId}`;
    },
    getDmInsightUrl(appId) {

        return `https://dm-insight.alibaba-inc.com/#/publish/config/${appId}?configType=orange`;

    },
    getMtlUrl(mtlId) {
        //mtl.alibaba.net
        return `https://mtl3.alibaba-inc.com/product/product_apps.htm?productId=${mtlId}`;

    },
    getQrUrl(name, appKey, appVersion, version, appIndexVersion, indexInfo) {
        const env = getEnv();
        name = name || '';
        appKey = appKey || '';
        appVersion = appVersion || '';
        version = version || '';
        if (appIndexVersion && appIndexVersion.indexOf("16") == 0) {
            appIndexVersion = "11" + appIndexVersion.slice(2, appIndexVersion.length);
        } else {
            appIndexVersion = appIndexVersion || '';
        }
        const domain = isOnlineEnv() ? 'pages.tmall.com' : 'pre-wormhole.tmall.com';
        const uri = `https://${domain}/wow/z/app/orange/debug/home`;
        // const uri = "https://orange.waptest.taobao.com/debug/index.html";
        // const uri="https://wapp.m.taobao.com/orange/index.html";
        return `${uri}?env=${env}&name=${name}&appKey=${appKey}&appVersion=${appVersion}&version=${version}&appIndexVersion=${appIndexVersion}&accsIndexInfo=${indexInfo ? encodeURIComponent(JSON.stringify(indexInfo)) : ''}`;

    },
    getChangeFreeUrl(orderId) {
        if (orderId) {

            if (isTestEnv()) {
                return "http://changesafe.alibaba.net/pages/order_detail/index.html?orderId=" + orderId;

            } else {
                return "https://aicf.alibaba-inc.com/aicf/approval/detail/" + orderId;

            }
        }
        return null;
    },
    getHelpUrl(name) {
        if (!name) {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki";

        } else if (name == 'beta') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/bazf52#dWABu";

        } else if (name == 'publish') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/bazf52#e1RFr";

        } else if (name == 'version') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/ggvqdb";

        } else if (name == 'strategy') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/pi9wvg";

        } else if (name == 'api') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/vidunm";

        } else if (name == 'namespace') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/vgtkbb";
        } else if (name === 'report') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/yksqhp";
        } else if (name === 'delete') {
            return "https://yuque.antfin-inc.com/wireless-orange/wiki/vgtkbb#0TS3U";
        }
        return "https://yuque.antfin-inc.com/wireless-orange/wiki";

    },
    getBpmsUrl(bpmsId) {
        if (bpmsId) {

            if (isTestEnv()) {
                return "http://bpms-test.alibaba-inc.com/workdesk/instDetail?procInsId=" + bpmsId;

            } else {
                return "http://bpms.alibaba-inc.com/workdesk/instDetail?procInsId=" + bpmsId;

            }
        }
        return null;
    },
    getMappcenterHomeUrl() {
        if (isTestEnv()) {
            //return "https://mappcenter.alibaba.net/";
            return "http://mtl4.alibaba.net/#/appKey/"
        }
        //return "https://mappcenter.alibaba-inc.com/";
        return "https://mtl4.alibaba-inc.com/#/appKey/";

    },
    getMappcenterAppUrl(appId) {
        if (!appId) {
            return null;
        }
        //https://mtl4.alibaba-inc.com/#/appKey/2022053198096/detail/overview
        return this.getMappcenterHomeUrl() + appId + "/detail/overview";
        // return this.getMappcenterHomeUrl() + "manage/detail?appID=" + appId;

    },
    getWorkUrl(empId) {
        if (!empId) {
            return null;
        }
        return "https://work.alibaba-inc.com/nwpipe/u/" + empId;

    },
    getNamespaceFbiUrl(appKey, namespace) {
        if (!appKey || !namespace) {
            return null;
        }
        return `https://fbi.alibaba-inc.com/dashboard/view/page.htm?id=1069463&appKey=${appKey}&namespace=${namespace}`;
    },
    getTigaTaskDetailUrl(taskId) {
        if (!taskId) return '';

        return `https://${getTigaReleaseHost()}/task/detail?taskId=${taskId}&fullscreen=Y&minimum=Y&size=small`;
    },
    getTigaTemplateCenterUrl() {
        return `https://${getTigaReleaseHost()}/template/list?tab=ALL`;
    },
    getAppFbiUrl(appKey) {
      if (!appKey) {
          return null;
      }
      return `https://fbi.alibaba-inc.com/dashboard/view/page.htm?id=1066235&appKey=${appKey}`;
    },
}
