'use strict';
import StatusSpan from '@/components/StatusSpan';

import {BUSINESS_STATUS_ARR} from '@/constants/business';
import {
    AVAILABLEL_ARR,
    EMERGENT_ARR,
    OFFLINE_REASONS,
    VERSION_SOURCE_API,
    VERSION_SOURCE_ARR,
    VERSION_SOURCE_PREPLAN,
    VERSION_STATUS_ARR
} from '@/constants/version';
import {
    LOADLEVEL_ARR,
    NS_DEFAULT_VALUE,
    NS_DEFAULT_CUSTOM_VALUE,
    NS_TYPE_ARR,
    NS_TYPE_STANDARD
} from '@/constants/namespace';

import {SETTING_BOOLEAN_ARR} from '@/constants/setting';
import {REPORT_DC_FAIL_CODE_ARR, REPORT_DC_SERVLET_ARR} from '@/constants/report';
import {NS_SUB_TYPE_ARR} from "@/constants/namespace";
import { MASS_RESULT_STATUS
    , RECORD_STATUS_ARR, RECORD_TYPE_ARR
    , CHEC<PERSON>_PROVIDER_ARR, CHECK_STATUS_ARR, CHECK_STATUS_PASS
    , SKIP_SUPPORT_ARR
} from "@/constants/record";
import React from "react";


export const ConvertUtils = {

    getAvailableName: (isAvailable) => {
        if (isAvailable === undefined) {
            return null;
        }
        for (let i = 0; i < AVAILABLEL_ARR.length; i++) {
            if (AVAILABLEL_ARR[i].value == isAvailable) {
                return AVAILABLEL_ARR[i].label;
            }
        }
    },
    getEmergentName: (isEmergent) => {
        if (isEmergent === undefined) {
            return null;
        }
        for (let i = 0; i < EMERGENT_ARR.length; i++) {
            if (EMERGENT_ARR[i].value == isEmergent) {
                return EMERGENT_ARR[i].label;
            }
        }
    },

    getBusinessStatusName: (status) => {
        if (status === undefined) {
            return null;
        }
        for (let i = 0; i < BUSINESS_STATUS_ARR.length; i++) {
            if (BUSINESS_STATUS_ARR[i].value == status) {
                return BUSINESS_STATUS_ARR[i].label;
            }
        }
    },

    getVersionStatusName: (status) => {
        if (status === undefined) {
            return null;
        }
        for (let i = 0; i < VERSION_STATUS_ARR.length; i++) {
            if (VERSION_STATUS_ARR[i].value == status) {
                return VERSION_STATUS_ARR[i].label;
            }
        }
    },

    getVersionSourceName: (source) => {
        if (source === undefined) {
            return null;
        }
        for (let i = 0; i < VERSION_STATUS_ARR.length; i++) {
            if (VERSION_SOURCE_ARR[i].value == source) {
                return VERSION_SOURCE_ARR[i].label;
            }
        }
    },
    getVersionOfflineReason: (reason) => {
        if (reason === undefined) {
            return null;
        }
        for (let i = 0; i < OFFLINE_REASONS.length; i++) {
            if (OFFLINE_REASONS[i].value == reason) {
                return OFFLINE_REASONS[i].label;
            }
        }
        return reason;
    },

    getVersionSourceDesc: (source, sourceData) => {
        if (source === undefined || !sourceData) {
            return null;
        }
        const data = JSON.parse(sourceData);

        if (data) {
            if (source === VERSION_SOURCE_API) {
                return (data.businessName ? `(${data.businessName})` : '');
            } else if (source === VERSION_SOURCE_PREPLAN) {
                return (data.preplanId ? `(${data.preplanId})` : '');
            } else if (data && data.rollback) {
                if (data.lossy) {
                    return '有损回滚';
                }
                return '回滚';
            }

        }
        return null;
    },
    getLoadLevelName: (loadLevel) => {
        if (loadLevel === undefined) {
            return null;
        }
        for (let i = 0; i < LOADLEVEL_ARR.length; i++) {
            if (LOADLEVEL_ARR[i].value == loadLevel) {
                return LOADLEVEL_ARR[i].label;
            }
        }
    },
    getNsTypeName: (type) => {
        if (type === undefined) {
            return null;
        }
        for (let i = 0; i < NS_TYPE_ARR.length; i++) {
            if (NS_TYPE_ARR[i].value == type) {
                return NS_TYPE_ARR[i].label;
            }
        }
        return type;
    },
    getNsSubTypeName: (type, subType) => {
        if (!type || !subType || !NS_SUB_TYPE_ARR[type]) {
            return null;
        }
        for (let i = 0; i < NS_SUB_TYPE_ARR[type].length; i++) {
            if (NS_SUB_TYPE_ARR[type][i].value === subType) {
                return NS_SUB_TYPE_ARR[type][i].desc;
            }
        }
        return subType;
    },
    getNsTypeMode: (type) => {
        if (type === undefined) {
            return null;
        }
        for (let i = 0; i < NS_TYPE_ARR.length; i++) {
            if (NS_TYPE_ARR[i].value == type) {
                return NS_TYPE_ARR[i].model;
            }
        }
        return "properties";
    },
    getNsDefaultValue: (type) => {
        if (!type) {
            return "";
        }
        if (type == NS_TYPE_STANDARD) {
            return NS_DEFAULT_VALUE;
        }
        return NS_DEFAULT_CUSTOM_VALUE;
    },
    getRecordStatusName: (status) => {
        if (status === undefined) {
            return null;
        }
        for (let i = 0; i < RECORD_STATUS_ARR.length; i++) {
            if (RECORD_STATUS_ARR[i].value == status) {
                return RECORD_STATUS_ARR[i].label;
            }
        }
    },
    getRecordTypeName: (type) => {
        if (type === undefined) {
            return null;
        }
        for (let i = 0; i < RECORD_TYPE_ARR.length; i++) {
            if (RECORD_TYPE_ARR[i].value == type) {
                return RECORD_TYPE_ARR[i].label;
            }
        }
    },
    getMassResultStatus: (status) => {
        if (status === undefined) {
            return null;
        }
        for (let i = 0; i < MASS_RESULT_STATUS.length; i++) {
            if (MASS_RESULT_STATUS[i].value == status) {
                return MASS_RESULT_STATUS[i].label;
            }
        }
    },
    getSettingBooleanValue: (value) => {
        if (value === undefined) {
            return null;
        }
        for (let i = 0; i < SETTING_BOOLEAN_ARR.length; i++) {
            if (SETTING_BOOLEAN_ARR[i].value === value) {
                return SETTING_BOOLEAN_ARR[i].label;
            }
        }
    },
    getReportDCFailName: (value) => {
        if (value === undefined) {
            return null;
        }
        for (let i = 0; i < REPORT_DC_FAIL_CODE_ARR.length; i++) {
            if (REPORT_DC_FAIL_CODE_ARR[i].value === value) {
                return REPORT_DC_FAIL_CODE_ARR[i].label;
            }
        }
        return value;
    },
    getReportServletName: (value) => {
        return ConvertUtils.getLabelInArray(value, REPORT_DC_SERVLET_ARR);
    },
    getCheckStatusName: (value) => {
        return ConvertUtils.getLabelInArray(value, CHECK_STATUS_ARR);
    },
    getCheckStatusShow: (value) => {
        if (value === undefined) {
            return null;
        }
        return <StatusSpan title={ConvertUtils.getCheckStatusName(value)}
                           type={value == CHECK_STATUS_PASS ? 'success' : 'fail'}/>;
    },
    getCheckSkipSupportName: (value) => {
        return ConvertUtils.getLabelInArray(value, SKIP_SUPPORT_ARR);
    },
    getCheckProviderName: (value) => {
        return ConvertUtils.getLabelInArray(value, CHECK_PROVIDER_ARR);
    },

    getLabelInArray(value, arr) {
        if (value === undefined) {
            return null;
        }
        for (let i = 0; i < arr.length; i++) {
            if (arr[i].value === value) {
                return arr[i].label;
            }
        }
        return value;
    },
    getModulesName: (modules, moduleMap) => {
      if (!modules) {
        return '-';
      }
      moduleMap = moduleMap || {};
      const list = modules.split(',').map((moduleId) => (
        <span key={'module_' + moduleId}>
          {' '}
          {moduleMap[moduleId]?.mtlAddress ? (
            <a href={moduleMap[moduleId]?.mtlAddress} target="_blank">
              {moduleMap[moduleId]?.name || moduleId}
            </a>
          ) : (
            moduleMap[moduleId]?.name || moduleId
          )}
        </span>
      ));

      return <div>{list}</div>;
    },
}
