'use strict';
import {isSystemAdmin} from "@/utils/EnvUtils";


export const BUSINESS_STATUS_ARR = [
  {
    value: 0,
    label: '正常',
  }, {
    value: 1,
    label: '申请中',
  }, {
    value: 2,
    label: '已拒绝',
  }, {
    value: 3,
    label: '已下线',
  }];
const isAdmin=isSystemAdmin();

export const PERMISSION_ROLE_ARR = [
  {
    key: 'query',
    value: 'query',
    label: '查询',
    disabled: false,
  },
  {
    key: 'pushDevice',
    value: 'pushDevice',
    label: 'Beta灰度',
    disabled: false,
  },
  {
    key: 'pushCircle',
    value: 'pushCircle',
    label: '定量灰度',
    disabled: false,
  },
  {
    key: 'createVersion',
    value: 'createVersion',
    label: '版本创建',
    disabled: false,
  },
  {
    key: 'publish',
    value: 'publish',
    label: '版本发布',
    disabled: false,
  },
  {
    key: 'closeVersion',
    value: 'closeVersion',
    label: '版本关闭',
    disabled: false,
  },
  {
    key: 'pushContent',
    value: 'pushContent',
    label: '无版本推送(慎重)',
    disabled: !isAdmin,
  },
  {
    key: 'create',
    value: 'create',
    label: 'namespace创建',
    disabled: !isAdmin,
  },
  {
    key: 'update',
    value: 'update',
    label: 'namespace更新',
    disabled: !isAdmin,
  },
  {
    key: 'online',
    value: 'online',
    label: 'namespace上线',
    disabled: !isAdmin,
  },
  {
    key: 'offline',
    value: 'offline',
    label: 'namespace下线',
    disabled: !isAdmin,
  },
  {
    key: 'preplan',
    value: 'preplan',
    label: '预案执行(不开放)',
    disabled: !isAdmin,
  },
  {
    key: 'emergentPublish',
    value: 'emergentPublish',
    label: '紧急发布(不开放)',
    disabled: !isAdmin,
  },
  {
    key: 'push',
    value: 'push',
    label: '灰度(待下线)',
    disabled: !isAdmin,
  },
  {
    key: 'version',
    value: 'version',
    label: '版本创建(待下线)',
    disabled: !isAdmin,
  },
  {
    key: 'review',
    value: 'review',
    label: '版本审核/关闭(待下线)',
    disabled: !isAdmin,
  },
];
