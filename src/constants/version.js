'use strict';

export const AVAILABLEL_YES = 'y';
export const AVAILABLEL_NO = 'n';

export const AVAILABLEL_ARR = [
    {
        value: AVAILABLEL_YES,
        label: '是',
    }, {
        value: AVAILABLEL_NO,
        label: '否',
    }];

export const EMERGENT_ARR = [
    {
        value: 'y',
        label: '立即生效',
    }, {
        value: 'n',
        label: '普通生效',
    }];

export const VERSION_STATUS_SUCCESS = 200;
export const VERSION_STATUS_CANCEL = 250;
export const VERSION_STATUS_DELETE = 255;

export const VERSION_STATUS_CREATED = 0;

export const VERSION_STATUS_GRAY_DONE = 2;
export const VERSION_STATUS_REVIEWD = 4;

export const VERSION_STATUS_ARR = [
    {
        value: VERSION_STATUS_CREATED,
        label: '新创建',
    }, {
        value: VERSION_STATUS_GRAY_DONE,
        label: '灰度已验证',
    }, {
        value: VERSION_STATUS_REVIEWD,
        label: '已审核',
    }, {
        value: 50,
        label: '灰度中',
    }, {
        value: VERSION_STATUS_SUCCESS,
        label: '发布成功',
    }, {
        value: VERSION_STATUS_CANCEL,
        label: '取消发布',
    }, {
        value: VERSION_STATUS_DELETE,
        label: '已删除',
    }];
export const VERSION_SOURCE_ORANGE = 0;
export const VERSION_SOURCE_PREPLAN = 1;
export const VERSION_SOURCE_API = 2;

export const VERSION_SOURCE_ARR = [
    {
        value: VERSION_SOURCE_ORANGE,
        label: '平台',
    }, {
        value: VERSION_SOURCE_PREPLAN,
        label: '预案',
    }, {
        value: VERSION_SOURCE_API,
        label: 'API',
    }];


export const VERSION_STAGE_APPLY = "APPLY";
export const VERSION_STAGE_BETA = "BETA";
export const VERSION_STAGE_GRAY = "GRAY";
export const VERSION_STAGE_RATIO_GRAY = "RATIO_GRAY";
export const VERSION_STAGE_REVIEW = "REVIEW";
export const VERSION_STAGE_SKIP = "SKIP";
export const VERSION_STAGE_CLOSE = "CLOSE";
export const VERSION_STAGE_PUBLISH = "PUBLISH";



export const OFFLINE_REASONS = [{
    value: '1',
    label: '常规版本只能有一个生效，旧的被覆盖',
}, {
    value: '2',
    label: '策略版本发布超过限制版本，删除最旧的策略版本',
}, {
    value: '3',
    label: '发布出现相同策略,覆盖旧版本',
}, {
    value: '4',
    label: '发布[*]指定要覆盖的版本',
}, {
    value: '5',
    label: '删除配置项下线所有生效的发布单',
}, {
    value: '6',
    label: '回滚引发的版本覆盖',
}];
