'use strict';


export const RECORD_TYPE_APPLY = 1;
export const RECORD_TYPE_BETA = 2;
export const RECORD_TYPE_GRAY = 3;
export const RECORD_TYPE_REVIEW = 4;
export const RECORD_TYPE_SKIP = 5;
export const RECORD_TYPE_RATIO_GRAY = 6;


export const RECORD_TYPE_ARR = [
    {
        value: RECORD_TYPE_APPLY,
        label: '申请发布',
    }, {
        value: RECORD_TYPE_BETA,
        label: 'BETA灰度',
    }, {
        value: RECORD_TYPE_GRAY,
        label: '定量灰度',
    }, {
        value: RECORD_TYPE_RATIO_GRAY,
        label: '百分比灰度',
    }, {
        value: RECORD_TYPE_REVIEW,
        label: '审核',
    }, {
        value: RECORD_TYPE_SKIP,
        label: '跳过',
    }, {
        value: 10,
        label: '发布',
    }, {
        value: 20,
        label: '下线',
    }, {
        value: 30,
        label: '关闭',
    }];

export const RECORD_STATUS_SUCCESS = 200;
export const RECORD_STATUS_FAIL = 400;

export const RECORD_STATUS_INIT = 11;
export const RECORD_STATUS_CHECKING = 21;
export const RECORD_STATUS_PREPARING = 21;
export const RECORD_STATUS_PROCESSING = 41;
export const RECORD_STATUS_CANCEL = 300;


export const RECORD_STATUS_ARR = [
    {
        value: RECORD_STATUS_SUCCESS,
        label: '成功',
    }, {
        value: RECORD_STATUS_FAIL,
        label: '失败',
    }, {
        value: RECORD_STATUS_INIT,
        label: '初始化',
    }, {
        value: RECORD_STATUS_CHECKING,
        label: '检测中',
    }, {
        value: RECORD_STATUS_PREPARING,
        label: '准备中',
    }, {
        value: RECORD_STATUS_PROCESSING,
        label: '流程中',
    }, {
        value: RECORD_STATUS_CANCEL,
        label: '取消',
    },];

export const RECORD_PARAMS_SHOWS = [
    {
        value: 'emergent',
        label: '立即生效',
    }, {
        value: 'deviceCnt',
        label: '目标设备数',
    }, {
        value: 'attachedGrayStrategy',
        label: '追加策略',
    }, {
        value: 'specifiedGrayStrategy',
        label: '指定策略',
    }, {
        value: 'skipStage',
        label: '跳过流程',
    }
]
export const RECORD_RESULT_SHOWS = [
    {
        value: 'changefreeStatus',
        label: 'CF状态',
    }, {
        value: 'massStatus',
        label: '任务状态',
    }, {
        value: 'massTaskId',
        label: '任务',
    }, {
        value: 'ackCnt',
        label: 'ACK设备数',
    }, {
        value: 'grayIndex',
        label: '序号',
    }, {
        value: 'approvalStatus',
        label: '审批状态',
    }, {
        value: 'message',
        label: '提示',
    }, {
        value: 'skipped',
        label: '跳过',
    }
]
export const MASS_RESULT_STATUS = [
    {
        value: 'INIT_STATUS',
        code: '0',
        label: '初始化中',
    }, {
        value: 'SPLIT_STATUS',
        code: '1',
        label: '已拆分任务',
    }, {
        value: 'PROCESSING_STATUS',
        code: '2',
        label: '执行中',
    }, {
        value: 'PAUSE_STATUS',
        code: '3',
        label: '暂停中',
    }, {
        value: 'FINISH_STATUS',
        code: '4',
        label: '已完成',
    }, {
        value: 'OVER_RETRY_ENDTIME_STATUS',
        code: '6',
        label: '超时未完成',
    }, {
        value: 'SUSPEND_STATUS',
        code: '7',
        label: '取消',
    }, {
        value: 'BIZ_PAUSE_STATUS',
        code: '9',
        label: '暂停',
    },];

export const GRAY_TYPE_MASS_CIRCLE = 'massCircle'
export const GRAY_CIRCLE_DIMENSIONS = ['app_ver', 'os_ver', 'm_brand', 'm_model'];

export const CHECK_PROVIDER_WOP = 'WOP';
export const CHECK_PROVIDER_CF_STEP = 'CF_STEP';
export const CHECK_PROVIDER_ARR = [
    {
        value: CHECK_PROVIDER_WOP,
        label: 'WOP'
    }, {
        value: CHECK_PROVIDER_CF_STEP,
        label: 'Changefree管控'
    }];

export const CHECK_STATUS_HOLD = 'HOLD';
export const CHECK_STATUS_PASS = 'PASSED';

export const CHECK_STATUS_ARR = [
    {
        value: CHECK_STATUS_HOLD,
        label: '阻断'
    }, {
        value: CHECK_STATUS_PASS,
        label: '通过'
    }, {
        value: 'EXCEPTION',
        label: '异常了'
    }, {
        value: 'UNKNOWN',
        label: '未知'
    }, {
        value: 'INIT',
        label: '初始化'
    }, {
        value: 'CHECKING',
        label: '检测中'
    }];

export const SKIP_SUPPORT_NONE = 'NONE';
export const SKIP_SUPPORT_ANY = 'ANY';
export const SKIP_SUPPORT_BPMS = 'BPMS';

export const SKIP_SUPPORT_ARR = [
    {
        value: SKIP_SUPPORT_NONE,
        label: '不可跳过'
    }, {
        value: SKIP_SUPPORT_ANY,
        label: '可跳过'
    }, {
        value: SKIP_SUPPORT_BPMS,
        label: '需审批'
    }];


export const SKIP_PROCESS_BETA = 'BETA';
export const SKIP_PROCESS_APPLY = 'APPLY';
export const SKIP_PROCESS_GRAY = 'GRAY';
export const SKIP_PROCESS_CHECK = 'CHECK';
export const SKIP_PROCESS_ALL = 'ALL';


export const SKIP_PROCESS_ARR = [
    {
        value: SKIP_PROCESS_BETA,
        label: 'BETA'
    }, {
        value: SKIP_PROCESS_APPLY,
        label: '审批',
        params: ['noApproval', 'emergent']
    }, {
        value: SKIP_PROCESS_GRAY,
        label: '灰度',
        params: ['noApproval']
    }, {
        value: SKIP_PROCESS_CHECK,
        label: '阻断卡口',
        params: ['noApproval']
    }, {
        value: SKIP_PROCESS_ALL,
        label: '全部'
    }];

export const RECORD_OPER_REFRESH = "REFRESH";
export const RECORD_OPER_CANCEL = "CANCEL";
