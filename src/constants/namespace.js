'use strict';


export const LOADLEVEL_ARR = [
  {
    value: 0,
    label: 'DEFAULT',
    desc: '默认加载'
  }, {
    value: 5,
    label: 'highLazy',
    desc: '闲时加载'
  }, {
    value: 10,
    label: 'highInit',
    desc: '启动加载'
  }];
//['text', 'properties', 'json']
export const NS_TYPE_STANDARD = 1;
export const NS_TYPE_CUSTOM = 3;
export const NS_TYPE_ARR = [
  {
    value: NS_TYPE_STANDARD,
    label: 'STANDARD',
    mode: 'properties',
    desc: 'KV配置'
  }, {
    value: NS_TYPE_CUSTOM,
    label: 'CUSTOM',
    mode: 'json',
    desc: '自定义配置'
  }];

export const NS_SUB_TYPE_JSON = 1;
export const NS_SUB_TYPE_ARR = {
  3: [{
    value: NS_SUB_TYPE_JSON,
    desc: 'JSON'
  }, {
    value: 99,
    desc: '其他类型'
  }]
};

export const NS_DEFAULT_VALUE = "# 以下是一个配置例子,content是一个标准的.properties格式文件,UTF-8编码\n" +
  "# 这是一行注释\n" +
  "key1=value1\n" +
  "\n" +
  "# 值为空的配置\n" +
  "key2=\n" +
  "\n" +
  "\n" +
  "# 有值的配置\n" +
  "key3=value3\n" +
  "\n" +
  "# 中文的key 中文的value\n" +
  "中文key=中文value\n" +
  "\n" +
  "# 值是一个json串的例子\n" +
  "key10={\"a\":\"a1\",\"中文\":\"中文\"}"

export const NS_DEFAULT_CUSTOM_VALUE = '{' +
  '"label":"这是个自定义类型配置示例，可以清除后重新配置。可以是JSON类型，也可以是其它任意文本。"' +
  '}';
