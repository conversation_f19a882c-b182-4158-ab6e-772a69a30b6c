import qs from 'qs';
import moment from 'moment';
import modelExtend from 'dva-model-extend';
import {routerRedux} from 'dva/router';

import {queryAppList} from '@/services/setting';

import {commonModel} from '@/utils/CommonModel';
import {getArr} from "@/utils/utils";
import namespace from '@/config/namespace';

const NAME_SPACE = namespace.app.list;


function getInitState() {
  return {
    formData: {},
    appList: [],
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {
      yield helper.put({
        type: 'getData',
      });

    },

    * getData({payload = {}}, {select, call, put}) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });
      const query = {};

      const res = yield call(queryAppList, query);
      yield put({
        type: 'updateState',
        payload: {loading: false, appList: getArr(res)},
      });

    },
  }
});
