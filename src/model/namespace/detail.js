import modelExtend from 'dva-model-extend';
import {getObj, getResolvedData,} from '@/utils/utils';
import {createVersion, getResourceDetail, rollbackVersion} from '@/services/version';
import {getNamespaceDetail, namespaceTools} from '@/services/namespace';

import {VersionUtils} from "@/utils/VersionUtils";
import {LinkUtils} from '@/utils/LinkUtils';
import {Edit} from '@/utils/NsEditModel';
import {commonModel} from '@/utils/CommonModel';
import namespace from '@/config/namespace';

import {
  addNamespace,
  loadLevelSubmitAuditing,
  updateNamespace,
} from '@/services/namespace';

const NAME_SPACE = namespace.namespace.detail;


function getInitState() {
  return {
    ...getResolvedData(),
  };
}


export default modelExtend(commonModel, Edit, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {

    },

    * createOrUpdateNamespace({payload: {data}}, {call, put}) {
      if (data.needAuditing) {
        const res = yield call(loadLevelSubmitAuditing, data);
        yield put({
          type: 'updateState',
          name: 'namespace',
          payload: {
            namespace: getObj(res),
            ...res
          },
        });
      } else {
        if (data.namespaceId) {
          yield call(updateNamespace, data);
        } else {
          yield call(addNamespace, data);
        }
      }

      if (data.namespaceId) {
        let getDetailData = {params: {namespaceId: data.namespaceId}}
        yield put({type: 'getDetail', payload: getDetailData});
      }
    },

    * getDetail({payload = {}}, {select, call, put}) {
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(getNamespaceDetail, payload.params);

      yield put({
        type: 'updateState',
        payload: getResolvedData(res),
      });

      if (res && res.list && res.list[0]) {
        const versionBO = VersionUtils.getVersion(res.list, payload.params.version) || VersionUtils.getDefaultVersion(res.list);

        if (versionBO) {
          yield put({
            type: 'getVersionResource',
            payload: {
              params: {'resourceId': versionBO.resourceId},
              versionBO: versionBO
            },
          });
          /* const res2 = yield call(getResourceDetail, {'resourceId':versionBO.resourceId});
           yield put({
             type: 'updateState',
             payload: {
               versionBO: versionBO,
               resourceBO: getObj(res2),
             }
           });*/
        }
      }

    },
    * getVersionResource({payload = {}}, {select, call, put}) {

      const res = yield call(getResourceDetail, payload.params);

      yield put({
        type: 'updateState',
        payload: {
          versionBO: payload.versionBO,
          resourceBO: getObj(res),
        },
      });
    },
    * createVersion({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(createVersion, payload.params);
      if (res) {
        location.hash = LinkUtils.getVersionDetail(res.namespaceId, res.version);
      } else {
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          },
        });
      }

    },
    * namespaceTools({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(namespaceTools, payload.params);

      yield put({
        type: 'updateState',
        payload: {
          loading: false,
        },
      });

      if (res) {
        const res2 = yield call(getNamespaceDetail, payload.reparams);

        yield put({
          type: 'updateState',
          payload: getResolvedData(res2),
        });
      }

    },
    * rollbackVersion({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(rollbackVersion, payload.params);
      if (res) {
        location.hash = LinkUtils.getVersionDetail(res.namespaceId, res.version);
      } else {
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          },
        });
      }

    },
  }
});
