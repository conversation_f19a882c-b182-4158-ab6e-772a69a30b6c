import modelExtend from 'dva-model-extend';
import {getObj, getResolvedData,} from '@/utils/utils';
import {getNamespace,auditingNamespace,cancelAuditingNamespace} from '@/services/namespace';

import {commonModel} from '@/utils/CommonModel';
import {LinkUtils} from '@/utils/LinkUtils';


import namespace from '@/config/namespace';

const NAME_SPACE = namespace.namespace.revise;


function getInitState() {
  return {
    ...getResolvedData(),
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {

    },
    * getDetail({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(getNamespace, payload.params);

      yield put({
        type: 'updateState',
        payload: getResolvedData(res),
      });
    },
    * auditingNamespace({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(auditingNamespace, payload.params);

      yield put({
        type: 'updateState',
        payload: {
          loading: false,
        },
      });

      if (res) {
        const res2 = yield call(getNamespace, payload.reparams);

        yield put({
          type: 'updateState',
          payload: getResolvedData(res2),
        });
      }

    },
  * cancelAuditingNamespace({payload = {}}, {select, call, put}) {

    yield put({
      type: 'updateState',
      payload: {
        loading: true,
      },
    });

    const res = yield call(cancelAuditingNamespace, payload.params);

    yield put({
      type: 'updateState',
      payload: {
        loading: false,
      },
    });

    if (res) {
      const res2 = yield call(getNamespace, payload.reparams);

      yield put({
        type: 'updateState',
        payload: getResolvedData(res2),
      });
    }

  }
  }
});
