import qs from 'qs';
import moment from 'moment';
import modelExtend from 'dva-model-extend';
import {routerRedux} from 'dva/router';

import {getAppNamespaceList, getNamespace} from '@/services/namespace';
import {queryAppList, queryUserByEmpIds, queryModuleByModuleIds} from '@/services/search';
import {NamespaceUtils} from "@/utils/NamespaceUtils";


import {Edit} from '@/utils/NsEditModel';

import {commonModel} from '@/utils/CommonModel';
import {getArr, getCheckedFormData, getEncodeParams, getHashPath, getObj} from "@/utils/utils";

import namespace from '@/config/namespace';

const NAME_SPACE = namespace.namespace.list;


const QUERY_KEY_CFG = {
    appKey: 'string',
    creator: 'string',
    gps: 'string',
    gpe: 'string',
    date: 'time',
    // users: 'array',
};

function getInitState() {
    return {
        formData: {
            appKey: '',
            appName: '',
            namespaceId: '',
            name: '',
            detail: '',
            owners: '',
            type: '',
            loadLevel: '',
            gmtCreate: '',
            gps: null,
            gpe: null,
            date: moment(),
            // users: [],
        },
        app: {},
        namespaceList: [],
        params: {},
    };
}

export default modelExtend(commonModel, Edit, {
    namespace: NAME_SPACE,

    state: getInitState(),
    effects: {
        * init(action, {put, call}) {
            const res = yield call(queryAppList, {});
            const {needRefresh, newFormData} = getCheckedFormData({
                cfg: QUERY_KEY_CFG,
                defaultData: {},
            });
            yield put({
                type: 'updateState',
                payload: {
                    loading: false,
                    appList: getArr(res),
                    params: newFormData,
                },
            });
            yield put({
                type: 'queryList',
                payload: {
                    params: newFormData
                },
            });
        },
        * reload({}, {put}) {
            const {needRefresh, newFormData} = getCheckedFormData({
                cfg: QUERY_KEY_CFG,
                defaultData: {},
            });
            yield put({type: 'queryList', payload: {params: newFormData}})
        },
        * queryList({payload: {params}}, {call, put}) {
            yield put({
                type: 'changeUrl',
                payload: {params},
            });
            if (params.appKey) {
                const res = yield call(getAppNamespaceList, params);
                yield put({
                    type: 'updateState',
                    payload: getObj(res),
                });
                if (res && res.namespaceList && res.namespaceList[0]) {
                    const empIds = NamespaceUtils.findUseListOfNsList(res.namespaceList);
                    if (empIds && empIds[0]) {
                        //empId 过大，分多次获取
                        let map = {};
                        const sliceSize = 100;
                        for (let i = 0; i < empIds.length; i +=sliceSize) {
                            const max = i  > empIds.length ? empIds.length : i + sliceSize;
                            const eachIds = empIds.slice(i , max);
                            const res2 = yield call(queryUserByEmpIds, {empIds: eachIds.join(",")});
                            if (res2) {
                                map = Object.assign(res2, map);
                                yield put({
                                    type: 'updateState',
                                    payload: {
                                        userMap: map,
                                    },
                                });
                            }
                        }
                    }
                    const moduleIds = NamespaceUtils.findModuleListOfNsList(res.namespaceList);
                    if (moduleIds && moduleIds[0]) {
                      const moduleMap = yield call(queryModuleByModuleIds, {moduleIds: moduleIds.join(",")});
                      if (moduleMap) {
                        yield put({
                          type: 'updateState',
                          payload: {
                            moduleMap,
                          },
                        });
                      }
                    }
                }
            } else {
                yield put({
                    type: 'updateState',
                    payload: {
                        app: {},
                        namespaceList: [],
                    },
                });
            }
        },
        * changeUrl({payload: {params}}, {put}) {
            const encodedParams = getEncodeParams({
                target: params,
                cfg: QUERY_KEY_CFG,
            });
            yield put(
                routerRedux.replace({
                    pathname: getHashPath(),
                    search: `?${qs.stringify(encodedParams)}`,
                }),
            );
        },
        * getDetail({payload: {params}}, {call, put}) {
            const res = yield call(getNamespace, params);
            yield put({
                type: 'updateState',
                payload: {formData: res},
            });
        },
    }
});
