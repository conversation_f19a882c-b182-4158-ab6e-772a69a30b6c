import modelExtend from 'dva-model-extend';

import {myNamespaceList} from '@/services/namespace';
import {queryAppList, queryUserByEmpIds, queryModuleByModuleIds} from '@/services/search';


import {commonModel} from '@/utils/CommonModel';
import {getArr} from "@/utils/utils";
import {Edit} from '@/utils/NsEditModel';
import {NamespaceUtils} from "@/utils/NamespaceUtils";


import namespace from '@/config/namespace';

const NAME_SPACE = namespace.namespace.me;

function getInitState() {
  return {
    list: [],
    formData: {},
    loading: false,
  };
}

export default modelExtend(commonModel, Edit, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, {call, put}) {
      const res = yield call(queryAppList, {});
      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          appList: getArr(res),
        },
      });
      yield put({
        type: 'queryMyAppList',
      });
    },
    * queryMyAppList({payload = {}}, {select, call, put}) {
      const {formData} = yield select(global => global[NAME_SPACE]);
      const query = {
        gps: formData.gps || null,
        gpe: formData.gpe || null,
        creator: formData.creator || null,
        appKey: formData.appKey || null,
      };

      const res = yield call(myNamespaceList, query);
      yield put({
        type: 'updateState',
        payload: {list: getArr(res)},
      });
      if (res) {
        const empIds = NamespaceUtils.findUserOfMyNsList(res);
        if (empIds && empIds[0]) {
          if (empIds && empIds[0]) {
            //empId 过大，分多次获取
            let map = {};
            const sliceSize = 100;
            for (let i = 0; i < empIds.length; i +=sliceSize) {
              const max = i  > empIds.length ? empIds.length : i + sliceSize;
              const eachIds = empIds.slice(i , max);
              const res2 = yield call(queryUserByEmpIds, {empIds: eachIds.join(",")});
              if (res2) {
                map = Object.assign(res2, map);
                yield put({
                  type: 'updateState',
                  payload: {
                    userMap: map,
                  },
                });
              }
            }
          }
        }
        const moduleIds = NamespaceUtils.findModuleOfMyNsList(res);
        if (moduleIds && moduleIds[0]) {
          const moduleMap = yield call(queryModuleByModuleIds, {moduleIds: moduleIds.join(",")});
          if (moduleMap) {
            yield put({
              type: 'updateState',
              payload: {
                moduleMap,
              },
            });
          }
        }
      }
    },
    * reload({}, {put}) {
      yield put({type: 'queryMyAppList'});
    },
  }
});
