import qs from 'qs';
import moment from 'moment';
import modelExtend from 'dva-model-extend';
import {routerRedux} from 'dva/router';

import {createUpdateBusiness, getBusinessList} from '@/services/business';
import {queryUserByEmpIds} from '@/services/search';

import {commonModel} from '@/utils/CommonModel';
import {getCheckedFormData, getEncodeParams, getHashPath, getResolvedListData} from "@/utils/utils";
import {UserUtils} from "@/utils/UserUtils";

import namespace from '@/config/namespace';

const NAME_SPACE = namespace.business.list;


const QUERY_KEY_CFG = {
  name: 'string',
  title: 'string',
  date: 'time',
  // users: 'array',
};

// 获取表单默认值
function getInitFormData() {
  return {
    name: '',
    title: '',
    date: moment(),
    // users: [],
  };
}


function getInitState() {
  return {
    formData: getInitFormData(),
    ...getResolvedListData(),
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {
      yield helper.put({
        type: 'initFormData',
      });

    },
    // 从url中读数据&存入当前state
    * initFormData(action, {put}) {
      const {newFormData} = getCheckedFormData({
        cfg: QUERY_KEY_CFG,
        defaultData: getInitFormData(),
      });

      yield put({
        type: 'changeFormData',
        payload: {
          params: newFormData,
          needRefresh: true,
        },
      });
    },

    * getData({payload = {}}, {select, call, put}) {
      const {formData, pagination} = yield select(global => global[NAME_SPACE]);

      const query = {
        pageNo: payload.pageNo || pagination.pageNo || 1,
        pageSize: payload.pageSize || pagination.pageSize || 10,
        name: formData.name || null,
      };

      const res = yield call(getBusinessList, query);
      yield put({
        type: 'updateState',
        payload: getResolvedListData(res, query),
      });
      if (res && res.content && res.content[0]) {
        const empIds = UserUtils.findUserSetOfBusinessList(res.content);
        const re2 = yield call(queryUserByEmpIds, {empIds: empIds});
        yield put({
          type: 'updateState',
          payload: {
            userMap: re2 || {},
          },
        });


      }

    },
    * createBusiness({payload = {}}, {select, call, put}) {
      const res = yield call(createUpdateBusiness, payload.params);
      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });
      if (res) {
        const res2 = yield call(getBusinessList, payload.reparams);

        yield put({
          type: 'updateState',
          payload: getResolvedListData(res2, {}),
        });
      }

    },
    * changeFormData({payload: {params, needRefresh}}, {put}) {
      yield put({
        type: 'changeState',
        name: 'formData',
        payload: params,
      });
      yield put({
        type: 'changeUrl',
      });

      if (needRefresh) {
        yield put({
          type: 'getData',
        });
      }
    },
    * changeUrl(action, {select, put}) {
      const {formData} = yield select(global => global[NAME_SPACE]);

      const params = getEncodeParams({
        target: formData,
        cfg: QUERY_KEY_CFG,
      });

      yield put(
        routerRedux.replace({
          pathname: getHashPath(),
          search: `?${qs.stringify(params)}`,
        }),
      );
    },
  }
});
