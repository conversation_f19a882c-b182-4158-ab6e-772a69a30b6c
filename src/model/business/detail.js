import modelExtend from 'dva-model-extend';
import {getResolvedData,} from '@/utils/utils';
import {createUpdateBusiness, getBusinessDetail} from '@/services/business';
import {commonModel} from '@/utils/CommonModel';
import namespace from '@/config/namespace';

const NAME_SPACE = namespace.business.detail;


function getInitState() {
  return {
    ...getResolvedData(),
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {

    },
    // 从url中读数据&存入当前state
    * getDetail({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(getBusinessDetail, payload.params);

      yield put({
        type: 'updateState',
        payload: getResolvedData(res),
      });
    },
    * updateBusiness({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(createUpdateBusiness, payload.params);

      if (res) {
        const res2 = yield call(getBusinessDetail, payload.reparams);

        yield put({
          type: 'updateState',
          payload: getResolvedData(res2),
        });
      }

    },
  },
});
