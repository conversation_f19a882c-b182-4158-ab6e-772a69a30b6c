import modelExtend from 'dva-model-extend';
import {getResolvedData,} from '@/utils/utils';
import {debugVersion} from '@/services/version';
import {commonModel} from '@/utils/CommonModel';
import namespace from '@/config/namespace';

const NAME_SPACE = namespace.version.debug;

function getInitState() {
  return {
    ...getResolvedData(),
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {

    },
    * getDetail({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(debugVersion, payload.params);

      yield put({
        type: 'updateState',
        payload: getResolvedData(res),
      });
    },

  },
});
