import modelExtend from 'dva-model-extend';
import {getObj, getResolvedData,} from '@/utils/utils';
import {
  getMassPushResult,
  getVersionDetail,
  rollbackVersion,
  versionStage
} from '@/services/version';
import {commonModel} from '@/utils/CommonModel';
import {LinkUtils} from '@/utils/LinkUtils';
import {queryAppDetail} from '@/services/setting';
import {recordOperate, getVersionDeviceCnt, getLatestGrayRecord} from "@/services/version";


import namespace from '@/config/namespace';

const NAME_SPACE = namespace.version.detail;


function getInitState() {
  return {
    ...getResolvedData(),
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {

    },
    * getDetail({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(getVersionDetail, payload.params);

      yield put({
        type: 'updateState',
        payload: getResolvedData(res),
      });

      const configRes = yield call(queryAppDetail, {appKey: res.appBO.appKey});
      yield put({
        type: 'updateState',
        payload: {
          appConfigSetting: getObj(configRes)
        },
      });

      const deviceCntRes = yield call(getVersionDeviceCnt, payload.params);
      yield put({
        type: 'updateState',
        payload: {
          versionDeviceCnt: deviceCntRes
        },
      });
    },
    * doVersionStage({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(versionStage, payload.params);

      const res2 = yield call(getVersionDetail, payload.reparams);

      yield put({
        type: 'updateState',
        payload: getResolvedData(res2),
      });

    },
    * recordOperate({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(recordOperate, payload.params);

      yield put({
        type: 'updateState',
        payload: {
          loading: false,
        },
      });

      if (res) {
        const res2 = yield call(getVersionDetail, payload.reparams);

        yield put({
          type: 'updateState',
          payload: getResolvedData(res2),
        });
      } else {
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          },
        });
      }

    },
    * getMassPushResult({payload = {}}, {select, call, put}) {

      const res = yield call(getMassPushResult, payload.params);

      yield put({
        type: 'updateState',
        payload: {
          loading: false,
        },
      });

    },
    * rollbackVersion({payload = {}}, {select, call, put}) {

      yield put({
        type: 'updateState',
        payload: {
          loading: true,
        },
      });

      const res = yield call(rollbackVersion, payload.params);
      if (res) {
        location.hash = LinkUtils.getVersionDetail(res.namespaceId, res.version);
      } else {
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
          },
        });
      }

    },
  }
});
