import qs from 'qs';
import moment from 'moment';
import modelExtend from 'dva-model-extend';
import {routerRedux} from 'dva/router';
import {getVersionList} from '@/services/version';
import {queryAppList, queryUserByEmpIds} from '@/services/search';
import {commonModel} from '@/utils/CommonModel';
import {getArr, getCheckedFormData, getEncodeParams, getHashPath, getResolvedListData} from "@/utils/utils";
import namespace from '@/config/namespace';
import {VersionUtils} from '@/utils/VersionUtils';


const NAME_SPACE = namespace.version.list;

const QUERY_KEY_CFG = {
  name: 'string',
  namespaceId: 'string',
  status: 'string',
  source: 'string',
  appKey: 'string',
  creator: 'string',
  online: 'string',
  gps: 'string',
  gpe: 'string',
  date: 'time',
  // users: 'array',
};

// 获取表单默认值
function getInitFormData() {
  return {
    name: '',
    namespaceId: '',
    status: '',
    source: '0',
    appKey: '',
    creator: '',
    online: '',
    gps: null,
    gpe: null,
    date: moment(),
    // users: [],
  };
}


function getInitState() {
  return {
    formData: getInitFormData(),
    ...getResolvedListData(),
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {
      yield helper.put({
        type: 'getDimension',
      });
      yield helper.put({
        type: 'initFormData',
      });

    },
    // 从url中读数据&存入当前state
    * initFormData(action, {put}) {
      const {newFormData} = getCheckedFormData({
        cfg: QUERY_KEY_CFG,
        defaultData: getInitFormData(),
      });

      yield put({
        type: 'changeFormData',
        payload: {
          params: newFormData,
          needRefresh: true,
        },
      });
    },

    * getDimension({payload = {}}, {select, call, put}) {

      const query = {};
      const res = yield call(queryAppList, query);
      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          appList: getArr(res),
        },
      });

    },

    * getData({payload = {}}, {select, call, put}) {
      const {formData, pagination} = yield select(global => global[NAME_SPACE]);
      const query = {
        pageNo: payload.pageNo || pagination.pageNo,
        pageSize: payload.pageSize || pagination.pageSize,
        name: formData.name || null,
        namespaceId: formData.namespaceId || null,
        status: formData.status != "" ? formData.status : null,
        source: formData.source != "" ? formData.source : null,
        online: formData.online ? 'true' : null,
        gps: formData.gps || null,
        gpe: formData.gpe || null,
        creator: formData.creator || null,
        appKey: formData.appKey || null,
      };

      const res = yield call(getVersionList, query);
      yield put({
        type: 'updateState',
        payload: getResolvedListData(res, query),
      });
      if (res && res.content && res.content[0]) {
        const empIds = VersionUtils.findUserOfVersionList(res.content);
        const re2 = yield call(queryUserByEmpIds, {empIds: empIds});
        yield put({
          type: 'updateState',
          payload: {
            userMap: re2,
          },
        });

      }

    },
    * changeFormData({payload: {params, needRefresh}}, {put}) {
      yield put({
        type: 'changeState',
        name: 'formData',
        payload: params,
      });
      yield put({
        type: 'changeUrl',
      });

      if (needRefresh) {
        yield put({
          type: 'getData',
        });
      }
    },
    * changeUrl(action, {select, put}) {
      const {formData} = yield select(global => global[NAME_SPACE]);

      const params = getEncodeParams({
        target: formData,
        cfg: QUERY_KEY_CFG,
      });

      yield put(
        routerRedux.replace({
          pathname: getHashPath(),
          search: `?${qs.stringify(params)}`,
        }),
      );
    },
  }
});
