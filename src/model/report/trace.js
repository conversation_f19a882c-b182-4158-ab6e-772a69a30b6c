import qs from 'qs';
import {message} from 'antd';
import modelExtend from 'dva-model-extend';
import {routerRedux} from 'dva/router';

import {commonModel} from '@/utils/CommonModel';
import {reportVersionDetail} from '@/services/version';
import {
  configNotifyNumList,
  configUpdateNumList,
  configUpdateRateList,
  configUseNumList,
  queryDimensions,
} from '@/services/report';
import {getCheckedFormData, getEncodeParams, getHashPath, getObj} from '@/utils/utils';
import {addTimeUnitAndClear, deepCopy, getMotuObj} from '@/utils/ReportUtils';

import namespace from '@/config/namespace';

const NAME_SPACE = namespace.report.trace;

const QUERY_KEY_CFG = {
  appKey: 'string',
  namespaceId: 'string',
  version: 'string',
  startTime: 'number',
  endTime: 'number',
  dataQuery: 'object',
  viewType: 'string',
};

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,
  state: {
    viewType: 'publish',
    params: {},
    app: {},
    namespace: {},
    versionDetail: {},
    cfgUpdateRateView: {},
    notifyNum: {},
    updateNum: {},
    nsUseNum: {},
    nsVersionUseNum: {},
    dims: [],
  },
  effects: {
    * init(action, {put}) {
      const {needRefresh, newFormData} = getCheckedFormData({
        cfg: QUERY_KEY_CFG,
        defaultData: {
          viewType: 'publish',
          dataQuery: {
            metricProperties: [
              {
                metricName: 'count',
                needAccumulatorUv: false,
              },
            ],
          },
        },
      });
      yield put({
        type: 'initTraceData',
        payload: {
          params: newFormData,
        },
      });
    },
    * initTraceData({payload: {params},}, {select, call, put},) {
      const reportDetails = yield call(reportVersionDetail, params);
      yield put({
        type: 'baseInit',
        payload: {params, reportDetails},
      });
    },
    * baseInit({payload: {params, reportDetails}}, {put},) {
      yield put({
        type: 'updateState',
        payload: {...getObj(reportDetails)},
      });
      let {versionDetail} = reportDetails;
      if (!params.startTime || params.viewType === 'publish') {
        params.startTime = versionDetail.gmtOnline || versionDetail.gmtPublish || versionDetail.gmtCreate;
        let day30Mills = 30 * 24 * 3600 * 1000;
        if (Date.now() - params.startTime > day30Mills) {
          params.startTime = Date.now() - day30Mills;
        }
      }
      if (!params.endTime) {
        params.endTime = Date.now() - 120 * 1000;
        if (params.endTime < params.startTime) {
          params.endTime = Date.now();
        }
      }
      params.dataQuery = {
        ...params.dataQuery,
        startTime: params.startTime,
        endTime: params.endTime,
        dimValues: [
          {dimName: 'configName', dimValues: [versionDetail.name]},
          {dimName: 'configVersion', dimValues: [versionDetail.version]},
        ],
      };
      yield put({
        type: 'queryData',
        payload: {
          params: params,
        },
      });
    },
    * queryData({payload: {params},}, {put},) {
      addTimeUnitAndClear(params.dataQuery);
      yield put({
        type: 'updateState',
        payload: {params},
      });
      yield put({
        type: 'changeUrl',
        payload: {params},
      });
      yield put({
        type: 'loadReports',
        payload: {params},
      });
    },
    * loadReports({payload: {params},}, {put},) {
      yield put({
        type: 'queryDimensions',
        payload: {params},
      });
      let useParams = deepCopy(params);
      useParams.dataQuery.metricProperties = [
        {
          metricName: 'uvCount',
          needAccumulatorUv: false,
        },
      ];
      yield put({
        type: 'getNsUseNumList',
        payload: {params: useParams},
      });
      yield put({
        type: 'getNsVersionUseNumList',
        payload: {params: useParams},
      });
      let uvParams = deepCopy(params);
      uvParams.dataQuery.metricProperties = [
        {
          metricName: 'uvCount',
          needAccumulatorUv: uvParams.viewType === 'publish',
        },
      ];
      // yield put({
      //   type: 'getNotifyNum',
      //   payload: {params: uvParams},
      // });
      yield put({
        type: 'getUpdateNum',
        payload: {params: uvParams},
      });
    },
    * getUpdateRate({payload: {params},}, {select, call, put},) {
      const res = yield call(configUpdateRateList, params);
      yield put({
        type: 'updateState',
        payload: {cfgUpdateRateView: getObj(res)},
      });
    },
    * getNotifyNum({payload: {params},}, {select, call, put},) {
      const res = yield call(configNotifyNumList, params);
      yield put({
        type: 'updateState',
        payload: {notifyNum: getMotuObj(res)},
      });
    },
    * getUpdateNum({payload: {params},}, {select, call, put},) {
      const res = yield call(configUpdateNumList, params);
      yield put({
        type: 'updateState',
        payload: {updateNum: getMotuObj(res)},
      });
    },
    * getNsUseNumList({payload: {params},}, {select, call, put},) {
      let newParams = {...params};
      newParams.dataQuery = {...newParams.dataQuery};
      newParams.dataQuery.dimValues = newParams.dataQuery.dimValues.filter(
        dim => dim.dimName !== 'configVersion',
      );
      const res = yield call(configUseNumList, {
        namespaceId: newParams.namespaceId,
        version: newParams.version,
        dataQuery: JSON.stringify(newParams.dataQuery)
      });
      yield put({
        type: 'updateState',
        payload: {nsUseNum: getMotuObj(res)},
      });
    },
    * getNsVersionUseNumList({payload: {params},}, {select, call, put},) {
      const res = yield call(configUseNumList, {
        namespaceId: params.namespaceId,
        version: params.version,
        dataQuery: JSON.stringify(params.dataQuery)
      });
      yield put({
        type: 'updateState',
        payload: {nsVersionUseNum: getMotuObj(res)},
      });
    },
    * queryDimensions({payload: {params},}, {select, call, put},) {
      const dimsRes = yield call(queryDimensions, {
        ...params,
        dimNames: JSON.stringify(['osVersion', 'appVersion', 'brand', 'deviceModel', 'country']),
      });
      yield put({
        type: 'initQueryForm',
        payload: {params, dimsRes},
      });
    },
    * initQueryForm({payload: {params, dimsRes},}, {select, call, put},) {
      if (!dimsRes.success) {
        message.error(dimsRes.errorMessage);
        return;
      }
      yield put({
        type: 'updateState',
        payload: {dims: getMotuObj(dimsRes)},
      });
    },
    * changeUrl({payload: {params},}, {put},) {
      const encodedParams = getEncodeParams({
        target: params,
        cfg: QUERY_KEY_CFG,
      });
      yield put(
        routerRedux.replace({
          pathname: getHashPath(),
          search: `?${qs.stringify(encodedParams)}`,
        }),
      );
    },
  },
});
