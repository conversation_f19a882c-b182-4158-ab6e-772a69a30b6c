import qs from 'qs';
import moment from 'moment';
import {message} from 'antd';

import modelExtend from 'dva-model-extend';
import {routerRedux} from 'dva/router';
import {queryAppList} from '@/services/search';
import {
  getAppDashboardOfApp,
  getAppDashboardOfConsole,
  getAppDashboardOfDc,
  getAppTrendOfApp,
  getAppTrendOfDc
} from '@/services/report';

import {commonModel} from '@/utils/CommonModel';
import {getArr, getCheckedFormData, getEncodeParams, getHashPath, getObj} from "@/utils/utils";
import namespace from '@/config/namespace';
import {formatDateWithGap, getTrendRange} from "@/utils/TimeUtils";
import {getDefaultAppKey} from "@/utils/EnvUtils";

import {getAppIntervalDetailsOfApp} from "../../services/report";

const NAME_SPACE = namespace.report.dashboard;

const QUERY_KEY_CFG = {
  appKey: 'string',
  current: 'string',
  compared: 'string',
  date: 'time',
  // users: 'array',
};

// 获取表单默认值
function getInitFormData() {
  return {
    appKey: getDefaultAppKey(),
    current: formatDateWithGap(-1),
    compared: formatDateWithGap(-2),
    date: moment(),
    // users: [],
  };
}


function getInitState() {
  return {
    formData: getInitFormData(),
    params: {},
    currentAppDash: {},
    compareAppDash: {},
    currentDcDash: {},
    compareDcDash: {},
    currentConsoleDash: {},
    compareConsoleDash: {},
    appDayTrend: [],
    appHHTrend: [],
    dcHHTrend: [],
    appIntervalDetails: []
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {
      yield helper.put({
        type: 'getDimension',
      });
      yield helper.put({
        type: 'initFormData',
      });

    },
    // 从url中读数据&存入当前state
    * initFormData(action, {put}) {
      const {newFormData} = getCheckedFormData({
        cfg: QUERY_KEY_CFG,
        defaultData: getInitFormData(),
      });

      yield put({
        type: 'changeFormData',
        payload: {
          params: newFormData,
          needRefresh: true,
        },
      });
    },

    * getDimension({payload = {}}, {select, call, put}) {

      const query = {};
      const res = yield call(queryAppList, query);
      yield put({
        type: 'updateState',
        payload: {
          loading: false,
          appList: getArr(res),
        },
      });

    },

    * getData({payload = {}}, {select, call, put}) {
      const {formData, pagination, params} = yield select(global => global[NAME_SPACE]);
      const ds1 = formData.current.replace(/\s/g, "").replace(/-/g, "") || formatDateWithGap(-1);
      const ds2 = formData.compared.replace(/\s/g, "").replace(/-/g, "") || formatDateWithGap(-2);
      const range = getTrendRange();

      if (ds1 == ds2) {
        message.error("当前日期和对比日期不能相等");
        return;
      }
      const toParams = {
        appKey: formData.appKey,
        current: ds1,
        compared: ds2,
      }
      let changeApp = false;
      let changeCurrent = false;
      let changeCompared = false;

      if (!params || !params.appKey) {
        changeApp = true;
        changeCurrent = true;
        changeCompared = true;

      } else {
        if (params.appKey != toParams.appKey) {
          changeApp = true;
        }
        if (params.current != toParams.current) {
          changeCurrent = true;
        }
        if (params.compared != toParams.compared) {
          changeCompared = true;
        }

      }
      yield put({
        type: 'changeState',
        name: 'params',
        payload: toParams,
      });
      const query1 = {
        ds: ds1,
        appKey: formData.appKey || null,
      };
      const query2 = {
        ds: ds2,
        appKey: formData.appKey || null,
      };


      if (changeApp || changeCurrent) {
        const res11 = yield call(getAppDashboardOfApp, query1);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            currentAppDash: getObj(res11),
          },
        });
        const res12 = yield call(getAppDashboardOfDc, query1);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            currentDcDash: getObj(res12),
          },
        });
        const res13 = yield call(getAppDashboardOfConsole, query1);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            currentConsoleDash: getObj(res13),
          },
        });

      }

      if (changeApp || changeCompared) {

        const res21 = yield call(getAppDashboardOfApp, query2);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            compareAppDash: getObj(res21),
          },
        });
        const res22 = yield call(getAppDashboardOfDc, query2);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            compareDcDash: getObj(res22),
          },
        });
        const res23 = yield call(getAppDashboardOfConsole, query2);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            compareConsoleDash: getObj(res23),
          },
        });
      }

      if (changeApp || changeCurrent) {

        const query3 = {
          begin: ds1 + '00',
          end: ds1 + '24',
          appKey: formData.appKey || null,
        };

        const res31 = yield call(getAppTrendOfApp, query3);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            appHHTrend: getArr(res31),
          },
        });

        const res32 = yield call(getAppTrendOfDc, query3);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            dcHHTrend: getArr(res32),
          },
        });

        const res33 = yield call(getAppIntervalDetailsOfApp, query3);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            appIntervalDetails: getArr(res33),
          },
        });
      }
      if (changeApp) {
        const query4 = {
          begin: range[0],
          end: range[1],
          appKey: formData.appKey || null,
        };
        const res41 = yield call(getAppTrendOfApp, query4);
        yield put({
          type: 'updateState',
          payload: {
            loading: false,
            appDayTrend: getArr(res41),
          },
        });
      }

    },
    * changeFormData({payload: {params, needRefresh}}, {put}) {
      yield put({
        type: 'changeState',
        name: 'formData',
        payload: params,
      });
      yield put({
        type: 'changeUrl',
      });

      if (needRefresh) {
        yield put({
          type: 'getData',
        });
      }
    },
    * changeUrl(action, {select, put}) {
      const {formData} = yield select(global => global[NAME_SPACE]);

      const params = getEncodeParams({
        target: formData,
        cfg: QUERY_KEY_CFG,
      });

      yield put(
        routerRedux.replace({
          pathname: getHashPath(),
          search: `?${qs.stringify(params)}`,
        }),
      );
    },
  }
});
