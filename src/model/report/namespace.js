import qs from 'qs';
import {message} from 'antd';
import modelExtend from 'dva-model-extend';
import {routerRedux} from 'dva/router';

import {commonModel} from '@/utils/CommonModel';
import {configUseNumList, queryDimensions} from '@/services/report';
import {queryVersionDetailList} from '@/services/version';
import {getArr, getCheckedFormData, getEncodeParams, getHashPath, getObj} from '@/utils/utils';
import {addTimeUnitAndClear, deepCopy, getMotuObj} from '@/utils/ReportUtils';

import model_ns from '@/config/namespace';
import moment from 'moment';

const NAME_SPACE = model_ns.report.namespace;
const dateFormat = 'YYYY-MM-DD HH:mm:ss';

const QUERY_KEY_CFG = {
  appKey: 'string',
  namespaceId: 'string',
  startTime: 'number',
  endTime: 'number',
  dataQuery: 'object',
  dimNames: 'string',
  viewType: 'string',
};

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,
  state: {
    viewType: 'publish',
    params: {},
    app: {},
    namespace: {},
    versions: [],
    dimNames: [''],
    nsVersionsUseNum: [],
    dims: {},
  },
  effects: {
    * init(action, {put}) {
      const {needRefresh, newFormData} = getCheckedFormData({
        cfg: QUERY_KEY_CFG,
        defaultData: {
          dataQuery: {
            metricProperties: [
              {
                metricName: 'uvCount',
                needAccumulatorUv: false,
              },
            ],
          },
        },
      });
      yield put({
        type: 'queryVersionsData',
        payload: {params: newFormData},
      });
    },
    * queryVersionsData({payload: {params}}, {call, all, put}) {
      let queryVersionsParams = {
        namespaceId: params.namespaceId,
        endTime: moment(params.endTime || Date.now()).format(dateFormat),
      };
      const versionDetailRes = yield call(queryVersionDetailList, queryVersionsParams);
      const versionDetailList = getObj(versionDetailRes);
      yield put({
        type: 'updateState',
        payload: {...versionDetailList},
      });
      yield put({
        type: 'queryReport',
        payload: {params, versionDetailList},
      });
    },
    * queryReport({payload: {params, versionDetailList}}, {call, put, all}) {
      let gmtCreate = versionDetailList.namespace.gmtCreate;
      if (!params.startTime) {
        let startTime = Date.now() - 24 * 3600 * 1000;
        if (gmtCreate > startTime) {
          startTime = gmtCreate;
        }
        params.startTime = startTime;
      }
      if (!params.endTime) {
        params.endTime = Date.now() - 120 * 1000;
        if (params.endTime < params.startTime) {
          params.endTime = Date.now();
        }
      }
      const dimsRes = yield call(queryDimensions, {
        namespaceId: params.namespaceId,
        startTime: params.startTime,
        endTime: params.endTime,
        dimNames: JSON.stringify(['osVersion', 'appVersion', 'brand', 'deviceModel', 'country'])
      });
      if (!dimsRes.success) {
        message.error(dimsRes.errorMessage);
        return;
      }
      const dims = getMotuObj(dimsRes);
      yield put({
        type: 'updateState',
        payload: {dims},
      });
      // const versionDim = dims.data.values.find(dim => dim.dimName === 'configVersion') || {};
      let versions = getArr(versionDetailList.versions);
      let usedVersions = versions.map(version => version.version);
      // let usedVersions = (versionDim.dimValues || []).filter(dv => versionSet.has(dv));
      let dimValues = params.dataQuery.dimValues || [];
      dimValues = dimValues.filter(
        obj => obj.dimName !== 'configName' && obj.dimName !== 'configVersion',
      );
      dimValues.push({dimName: 'configName', dimValues: [versionDetailList.namespace.name]});
      dimValues.push({dimName: 'configVersion', dimValues: usedVersions});
      params.dataQuery = {
        ...params.dataQuery,
        startTime: params.startTime,
        endTime: params.endTime,
        dimValues,
      };
      addTimeUnitAndClear(params.dataQuery);
      yield put({
        type: 'updateState',
        payload: {params},
      });
      yield put({
        type: 'changeUrl',
        payload: {params},
      });
      if (usedVersions.length === 0) {
        yield put({
          type: 'updateState',
          payload: {nsVersionsUseNum: {}},
        });
      } else {
        let nsParams = deepCopy(params);
        nsParams.dataQuery.dimValues = nsParams.dataQuery.dimValues.filter(
          dim => dim.dimName !== 'configVersion',
        );
        let [nsVersionsUseNumRes, nsUseNumRes] = yield all([
            yield call(configUseNumList, {
                namespaceId: params.namespaceId,
                dataQuery: JSON.stringify(params.dataQuery)
            }),
            yield call(configUseNumList, {
                namespaceId: nsParams.namespaceId,
                dataQuery: JSON.stringify(nsParams.dataQuery)
            })
        ]);
        yield put({
          type: 'updateState',
          payload: {
            nsVersionsUseNum: getMotuObj(nsVersionsUseNumRes),
            nsUseNum: getMotuObj(nsUseNumRes)
          }
        });
      }
    },
    * changeUrl({payload: {params}}, {put}) {
      const encodedParams = getEncodeParams({
        target: params,
        cfg: QUERY_KEY_CFG,
      });
      yield put(
        routerRedux.replace({
          pathname: getHashPath(),
          search: `?${qs.stringify(encodedParams)}`,
        }),
      );
    },
  },
});
