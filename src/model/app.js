import {NAME_SPACE} from '@/constants/app';
import {isSystemAdmin} from "@/utils/EnvUtils";

var menusModel = {
  namespace: NAME_SPACE,
  state: {
    menus: [
      {
        title: '配置管理',
        key: 'namespace',
        icon: 'home',
        children: [
          {
            title: '我的配置',
            key: 'myNamespaceList',
            href: '/namespace/namespace/me',
          },
          {
            title: '全部配置',
            key: 'namespaceList',
            href: '/namespace/namespace/list',
          }
        ],
      }, {
        title: '发布管理',
        key: 'version',
        icon: 'flag',
        children: [
          {
            title: '发布列表',
            key: 'namespaceVersion',
            href: '/namespace/version/list',
          },
        ],
      },
      // {
      //   title: '报表管理',
      //   key: 'report',
      //   icon: 'line-chart',
      //   children: [
      //     {
      //       title: '应用大盘',
      //       key: 'appDashboard',
      //       href: '/report/dashboard/dashboard',
      //     },
      //   ],
      // },
      {
        title: '管理中心',
        key: 'setting',
        icon: 'setting',
        children: [
          {
            title: 'APP列表',
            key: 'appSetting',
            href: '/setting/app/list',
          },
          {
            title: 'business列表',
            key: 'businessSetting',
            href: '/setting/business/list',
          },

        ],
      },
    ],
    defaultOpenMenuKeys: ['namespace', 'version', 'report', 'setting'],
  }
};
const isAdmin = isSystemAdmin();
if (isAdmin) {
  menusModel.state.menus.push({
    title: '报表管理',
    key: 'report',
    icon: 'line-chart',
    children: [
      {
        title: '应用大盘',
        key: 'appDashboard',
        href: '/report/dashboard/dashboard',
      },
    ]
  });
}
export default menusModel;
