import modelExtend from 'dva-model-extend';

import {commonModel} from '@/utils/CommonModel';
import {getResolvedData} from "@/utils/utils";
import namespace from '@/config/namespace';

const NAME_SPACE = namespace.home.index;


function getInitState() {
  return {
    ...getResolvedData(),
  };
}

export default modelExtend(commonModel, {
  namespace: NAME_SPACE,

  state: getInitState(),
  effects: {
    * init(action, helper) {

    },

  },
});
