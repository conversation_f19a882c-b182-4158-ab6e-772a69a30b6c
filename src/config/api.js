import { getTigaSuggestTemplateList } from '@/services/version';

export default {
  search: {
    user: '/api/search/fuzzySearchUser.json',
    aoneApp: '/api/search/fuzzySearchAoneApp.json',
    queryAppList: '/api/search/queryAppList.json',
    queryUserByEmpIds: '/api/search/queryUserByEmpIds.json',
    queryModuleByModuleIds: '/api/search/queryModuleByModuleIds.json',
    module: '/api/search/fuzzySearchModule.json'
  },
  business: {
    getBusinessList: '/api/business/queryBusinessList.json',
    getBusinessDetail: '/api/business/queryBusinessDetail.json',
    createUpdateBusiness: '/api/business/insertOrUpdateBusiness.json',
  },
  setting: {
    queryAppList: '/api/setting/queryAppList.json',
    queryAppDetail: '/api/setting/queryAppDetail.json',
    appConfigSetting: '/api/setting/appConfigSetting.json',
  },
  namespace: {
    appNamespaceList: '/api/namespace/appNamespaceList.json',
    namespaceListForMe: '/api/namespace/namespaceListForMe.json',
    addNamespace: '/api/namespace/addNamespace.json',
    auditingNamespace: '/api/namespace/auditingNamespace.json',
    cancelAuditingNamespace: '/api/namespace/cancelAuditingNamespace.json',
    offlineNamespace: '/api/namespace/offlineNamespace.json',
    availableNamespace: '/api/namespace/availableNamespace.json',
    updateNamespace: '/api/namespace/updateNamespace.json',
    loadLevelSubmitAuditing: '/api/namespace/loadLevelSubmitAuditing.json',
    getNamespace: '/api/namespace/getNamespace.json',
    getNamespaceDetail: '/api/namespace/getNamespaceDetail.json',
    queryKnockoutForOfflineNamespace: '/api/namespace/queryKnockoutForOfflineNamespace.json',
    queryUnAvailableNamespace: '/api/namespace/queryUnAvailableNamespace.json',
    permissionApply: '/api/namespace/permissionApply.json',
  },
  version: {
    getVersionList: '/api/version/queryVersionList.json',
    getVersionDetail: '/api/version/queryVersionDetail.json',
    queryKnockoutVersions: '/api/version/queryKnockoutVersions.json',
    createVersion: '/api/version/createVersion.json',
    getResourceDetail: '/api/version/queryResourceDetail.json',
    debugVersion: '/api/version/debugVersion.json',
    getMassPushResult: '/api/version/getMassPushResult.json',
    getMassDeviceList: '/api/version/getMassDeviceList.json',
    reportVersionDetail: '/api/version/reportVersionDetail.json',
    queryVersionDetailList: '/api/version/queryVersionDetailList.json',
    getRollbackList: '/api/version/queryRollbackList.json',
    getRollbackView: '/api/version/queryRollbackView.json',
    rollbackVersion: '/api/version/rollbackVersion.json',
    getCheckHoldResult: '/api/version/getCheckHoldResult.json',
    versionStage: '/api/version/versionStage.json',
    recordOperate: '/api/version/recordOperate.json',
    getVersionDeviceCnt: '/api/version/getVersionDeviceCnt.json',
    getLatestGrayRecord: '/api/version/getLatestGrayRecord.json',
    getTigaTaskDetail: '/api/version/getTigaTaskDetail.json',
    createTigaTask: '/api/version/createTigaTask.json',
    getTigaSuggestTemplateList: '/api/version/GetTigaSuggestTemplateList.json',
  },
  tools: {
    namespaceTools: '/api/tools/namespaceTools.json',
    versionTools: '/api/tools/versionTools.json',
    strategyGrayTools: '/api/tools/strategyGrayTools.json',
  },
  report: {
    configNotifyNumList: '/api/report/configNotifyNumList.json',
    configUpdateNumList: '/api/report/configUpdateNumList.json',
    configUpdateRateList: '/api/report/configUpdateRateList.json',
    configUseNumList: '/api/report/configUseNumList.json',
    queryDimensions: '/api/report/queryDimensions.json',
    getAppDashboardOfApp: '/api/report/getAppDashboardOfApp.json',
    getAppDashboardOfDc: '/api/report/getAppDashboardOfDc.json',
    getAppDashboardOfConsole: '/api/report/getAppDashboardOfConsole.json',
    getAppTrendOfApp: '/api/report/getAppTrendOfApp.json',
    getAppTrendOfDc: '/api/report/getAppTrendOfDc.json',
    getAppIntervalDetailsOfApp: '/api/report/getAppIntervalDetailsOfApp.json',

  },
};
