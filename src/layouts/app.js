import React from 'react';
import pathToRegexp from 'path-to-regexp';
import {Link} from 'dva/router';
import {Affix, Alert, Icon, Menu} from '@alipay/bigfish/antd';
import {getArr, getEnv} from '@/utils/utils';
import Header from '@/components/Header';
import styles from './app.less';
import {connect} from "@alipay/bigfish/sdk";

const {SubMenu} = Menu;

@connect(({app}) => ({
  app,
}))
class App extends React.PureComponent {
  isDaily = () => {
    const env = getEnv();
    return (env == 'LOCAL' || env == 'DAILY');
  };
  getMenus = (origin) => {
    const menus = getArr(origin).map(({key, title, children, href, icon}) => {
      const subItems = this.getMenus(children);
      const iconWidget = icon ? <Icon type={icon}/> : null;
      const titleWidget = (
        <span>
          {iconWidget}
          {title}
        </span>
      );
      if (subItems.length) {
        return (
          <SubMenu key={key} title={titleWidget}>
            {this.getMenus(children)}
          </SubMenu>
        );
      } else {
        return (
          <Menu.Item key={href}>
            <Link to={href || ''}>{titleWidget}</Link>
          </Menu.Item>
        );
      }
    });
    return menus;
  };

  render() {
    const {app, location: {pathname}} = this.props;
    let defaultKeys = app.defaultOpenMenuKeys;
    let keys = [];
    const reg = pathToRegexp('/:module/:page/:child/:param?');
    const match = reg.exec(pathname) || [];
    const key = match[1] ? `${match[1]}` : '';
    if (keys.indexOf(key) < 0) {
      keys.push(key);
    }
    for (let i = 0; i < defaultKeys.length; i++) {
      if (keys.indexOf(defaultKeys[i]) < 0) {
        keys.push(defaultKeys[i]);
      }
    }
    const {tips} = window._DATA_;
    const _renderTips = function () {
      if (tips) {
        return <Alert className={styles.tips}
                      message="通知"
                      description={tips}
                      type="error"
                      closable
        />;
      }
      return null;
    };
    return (
      <div className={styles.app}>
        {tips ? <div className={styles.notice}>
          <div className={styles["notice-content"]}>{tips}</div>
        </div> : null}
        <Header/>
        <div className={styles.body}>
          <div className={styles['nav-left']}>
            <Affix>
              <Menu
                className={styles.menu}
                mode="inline"
                selectedKeys={[pathname]}
                defaultOpenKeys={keys}
              >
                {this.getMenus(app.menus)}
              </Menu>
            </Affix>
          </div>
          <div className={styles.content}>{this.props.children}</div>
        </div>
      </div>
    );
  }
}

export default App;