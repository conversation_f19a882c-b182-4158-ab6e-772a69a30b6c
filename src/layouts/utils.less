@indent: 16px;
@hfIndent: @indent / 2;

:global {
  .st-mt {
    margin-top: @indent;
  }
  .st-mr {
    margin-right: @indent;
  }
  .st-mb {
    margin-bottom: @indent;
  }
  .st-ml {
    margin-left: @indent;
  }
  .mr10 {
    margin-right: 10px;
  }

  .hf-mt {
    margin-top: @hfIndent;
  }
  .hf-mr {
    margin-right: @hfIndent;
  }
  .hf-mb {
    margin-bottom: @hfIndent;
  }
  .hf-ml {
    margin-left: @hfIndent;
  }

  .bread-nav {
    margin-bottom: @indent;
  }

  .pull-left {
    float: left;
  }
  .pull-right {
    float: right;
  }

  .hBox {
    display: -webkit-box;
    -webkit-box-orient: horizontal;
  }
  .vBox {
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
  .fBox {
    display: -webkit-flex;
    flex-wrap: wrap;
  }

  .auto {
    -webkit-box-flex: 1;
    flex-grow: 1;
    flex-shrink: 1;
  }
  .help {
    margin-left:10px;
    cursor: pointer;
  }
}
