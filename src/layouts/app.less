@import 'utils.less';

@navLeft: 200px;
@height: 100vh;

.app {
}
.body {
  display: flex;
}
.nav-left {
  width: @navLeft;
  flex-shrink: 0;
}
.menu {
  height: @height;
}
.tips {
  position: fixed !important;
  top:10px;
  right:250px;
  width:400px;
  background: #fafafa;
  z-index: 10;
}
.content {
  padding: 15px;
  width: 100%;
  background: #fafafa;
}
.notice {
  background: #e3f2dc;
  text-align: center;
}
.notice-content {
  color: #4f8450;
  width: 100%;
}
