{"private": true, "scripts": {"start": "bigfish dev", "dev": "bigfish dev", "devs": "bigfish dev --no-mock", "test": "bigfish test", "cov": "bigfish cov", "build": "bigfish build", "debug": "bigfish build -- --debug", "pack": "bigfish pack", "lint": "bigfish lint --style", "help": "bigfish help", "version": "bigfish -v", "ci": "bigfish lint && bigfish cov"}, "dependencies": {"@ali/emasd-pro": "1.0.14", "@alipay/bigfish": "^2.24.1", "@ant-design/pro-layout": "^4.8.1", "axios": "^0.19.0", "classnames": "^2.2.5", "codemirror": "^5.48.2", "css-animation": "^2.0.4", "dva-model-extend": "^0.1.2", "echarts": "^4.5.0", "echarts-for-react": "^2.0.14", "lodash-decorators": "^6.0.0", "memoize-one": "^4.0.0", "moment": "^2.19.2", "nzh": "^1.0.3", "path-to-regexp": "^2.1.0", "qrcode.react": "0.7.2", "qs": "^6.5.1", "rc-animate": "^2.4.4"}, "devDependencies": {"prettier": "^3.3.2"}, "repository": "**************************:ali-*********************frontend/orangeview.git"}