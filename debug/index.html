
<!DOCTYPE HTML>
<HTML>
<HEAD>
    <TITLE>Orange Debug Tool</TITLE>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
    <meta name="format-detection" content="telephone=no"/>
    <!-- WindVane meta -->
    <meta id="stopUsePullRefresh" value="true">
    <meta id="urlInLaw" value="true">
    <meta id="WV.Meta.DisableRefresh" value="true"/>
    <meta id="WV.Meta.EnableLongPressEvent" value="true"/>
    <!-- Bootstrap -->
    <link rel="stylesheet" href="//h5.m.taobao.com/orange/twitter-bootstrap/3.0.3/css/bootstrap.min.css">
    <!-- 移除 Bootstrap btn-default 和 btn-primary 的 hover 样式 -->
    <!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
    <script src="//h5.m.taobao.com/orange/jquery/1.10.2/jquery.min.js"></script>
    <!-- Include all compiled plugins (below), or include individual files as needed -->
    <script src="//h5.m.taobao.com/orange/twitter-bootstrap/3.0.3/js/bootstrap.min.js"></script>
    <!-- WindVane -->
    <script src="//g.alicdn.com/mtb/lib-windvane/3.0.0/windvane.js"></script>
    <script src="https://g.alicdn.com/jiantao.qjt/ts-security-lib-fe/0.0.7/security-fe.bundle.js"></script>

    <style type="text/css">
        .btn-default:hover {
            color: #333;
            background-color: #fff;
            border-color: #ccc;
        }

        .btn-default.active:hover {
            color: #333;
            background-color: #ebebeb;
            border-color: #adadad;
        }

        .btn-primary:hover {
            color: #fff;
            background-color: #428bca;
            border-color: #357ebd;
        }

        .btn-primary.active:hover {
            color: #fff;
            background-color: #3276b1;
            border-color: #285e8e;
        }

        table .cur a {
            color: #333;
            font-size: 16px;
        }

        .modal-backdrop {
            bottom: -100px;
        }

        p.thick {
            font-weight: bold
        }
    </style>
    <script type="text/javascript">
        // 成功和失败的提示框
        function success(message) {
            showDialogModal('alert-success', message, 'Success!');
        }

        function failure(message) {
            showDialogModal('alert-danger', message, 'Failure!');
        }

        function info(message, title, marginTop) {
            showDialogModal('alert-info', message, title, marginTop);
        }

        function showDialogModal(styleClass, message, title, marginTop) {
            if (message == undefined) {
                message = "";
            }
            if (title == undefined) {
                title = "";
            }
            if (marginTop == undefined) {
                marginTop = '0';
            }
            $('#dialogTitle').text(title);
            $('#dialogMessage').text(message);
            $('#dialogAlert').removeClass();
            $('#dialogAlert').addClass('alert ' + styleClass);
            $('#dialogModal').css('margin-top', marginTop);
            $('#dialogModal').modal('show');
        }

    </script>
</HEAD>
<BODY data-noaplus>
<div class="container">
    <!-- 标题栏 -->
    <nav class="navbar navbar-inverse" role="navigation">
        <div class="navbar-header">
    <span class="navbar-brand">
      Orange Debug Tool
    </span>
        </div>
    </nav>
    <div class="tab-content">
        <ul class="nav nav-tabs" role="tablist">
            <li class="active"><a role="tab" data-toggle="tab" href="#debugDiv">索引比对</a></li>
            <li><a role="tab" data-toggle="tab" href="#namespaceDiv">内容数据</a></li>

        </ul>
        <div class="tab-pane active list-group list-group-cram" id="debugDiv">
            <div class="list-group-item">
                <h4 class="list-group-item-heading">索引比对</h4>
                <table class="table table-bordered table-striped">
                    <colgroup>
                        <col class="col-xs-1">
                        <col class="col-xs-1">
                        <col class="col-xs-3">
                        <col class="col-xs-3">
                    </colgroup>
                    <thead>
                    <tr>
                        <th>列名</th>
                        <th>相等</th>
                        <th>云输入</th>
                        <th>端索引</th>
                    </tr>
                    </thead>
                    <tbody id="J_indexCompare">

                    </tbody>
                </table>
                <h4 class="list-group-item-heading">NS比对</h4>
                <div class="list-group-item">
                    <div class="input-group">
                        <div class="input-group-addon">namespace</div>
                        <input type="text" class="form-control" id="J_ns" readonly="readonly">

                    </div>
                    <p></p>
                    <p></p>
                    <div class="input-group">
                        <div class="input-group-addon">云版本</div>
                        <input type="text" class="form-control" id="J_version" readonly="readonly">

                    </div>

                </div>

                <table class="table table-bordered table-striped">
                    <colgroup>
                        <col class="col-xs-1">
                        <col class="col-xs-1">
                        <col class="col-xs-3">
                    </colgroup>
                    <thead>
                    <tr>
                        <th>比较</th>
                        <th>version</th>
                        <th>策略</th>
                    </tr>
                    </thead>
                    <tbody id="J_nsCompare">

                    </tbody>
                </table>

            </div>

        </div>

        <div class="tab-pane list-group list-group-cram" id="namespaceDiv">
            <div>
                <div><p></p>
                    <a href="/debug/old/home.html">去旧版页面</a></div>
                <p></p>
                <div>
                    <p></p>
                    <div class="list-group-item">
                        <h4 class="list-group-item-heading">输入ns</h4>
                        <p></p>
                        <div class="input-group">
                            <div class="input-group-addon">namespace</div>
                            <input type="text" class="form-control" id="J_namespace">
                        </div>
                        <p></p>
                        <button type="button" class="btn btn-primary btn-lg active btn-block"
                                onclick="reloadOrangeData()">查询
                        </button>

                    </div>

                    <h4 class="list-group-item-heading">内容</h4>
                    <p></p>


                    <table class="table table-bordered table-striped">
                        <colgroup>
                            <col class="col-xs-1">
                            <col class="col-xs-7">
                        </colgroup>
                        <thead>
                        <tr>
                            <th>namespace</th>
                            <th>content</th>
                        </tr>
                        </thead>
                        <tbody id="J_contentConfig">

                        </tbody>
                    </table>
                </div>


            </div>


            <script type="text/javascript">

                const _showVersion = function (version) {
                    if (!version) {
                        return version;
                    }
                    if (version.length != 19) {
                        return version;
                    }
                    return version.slice(0, 2) + ' ' + version.slice(2, 10) + ' ' + version.slice(10, 16) + ' ' + version.slice(16, 19);
                };

                var globalIndex = {
                    "appIndexVersion": "1120190705144305029",
                    "appKey": "12345678",
                    "appVersion": "*",
                    "cdn": "dorangesource.alicdn.com",
                    "createTime": "1562308985029",
                    "id": "38a788f309a44ae9baf7fa0b60023f4e",
                    "mergedNamespaces": [{
                        "appVersion": "*",
                        "candidates": [{
                            "match": "os_ver=5.10.0",
                            "md5": "f571ccefcfd65852293d6edc8d377032",
                            "resourceId": "ns96efa0441f12415a9e822dc06509b814.json",
                            "version": "2120190705144304903"
                        }],
                        "highLazy": 1,
                        "loadLevel": "DEFAULT",
                        "md5": "52d81c58b31968fa221ed8919e64ed52",
                        "name": "orange_test_v1",
                        "resourceId": "ns3c023a165617482c8050d6ba1330273e.json",
                        "type": "STANDARD",
                        "version": "2120190705143355416"
                    }, {
                        "appVersion": "*",
                        "highLazy": 1,
                        "loadLevel": "DEFAULT",
                        "md5": "4df5282eec61a26290ac42a5a85d9866",
                        "name": "ele_star_smallapp",
                        "resourceId": "ns1e513c3a90da454086e0d27873b33aff.json",
                        "type": "STANDARD",
                        "version": "2120190328113835798"
                    }],
                    "protocol": "https",
                    "version": "1620190705144305029",
                    "versionIndexVersion": "0"
                };

                var globalNamespace = {
                    "orange_test_v1": {
                        "name": "orange_test_v1",
                        "highLazy": 1,
                        "loadLevel": "DEFAULT",
                        "content": "sagagaogagaoug",
                        "version": 2120190705144305029
                    }
                }


                // 获取参数
                var qs = (function (a) {
                    if (a == "") return {};
                    var b = {};
                    for (var i = 0; i < a.length; ++i) {
                        var p = a[i].split('=');
                        if (p.length != 2) continue;
                        b[p[0]] = decodeURIComponent(p[1].replace(/\+/g, " "));
                    }
                    return b;
                })(window.location.search.substr(1).split('&'));

                var UNKOWN = "-";


                const queryConfig = {
                    env: SecurityUtil.escapeHtml(qs.env || UNKOWN),
                    appKey: SecurityUtil.escapeHtml(qs.appKey || UNKOWN),
                    appVersion: SecurityUtil.escapeHtml(qs.appVersion || UNKOWN),
                    namespace: SecurityUtil.escapeHtml(qs.name || UNKOWN),
                    version: SecurityUtil.escapeHtml(qs.version || UNKOWN),
                    appIndexVersion: SecurityUtil.escapeHtml(qs.appIndexVersion || UNKOWN),
                }
                console.log(queryConfig);


                function renderNamespace() {
                    const ns = queryConfig.namespace || '';
                    const version = queryConfig.version || '';
                    $("#J_ns").val(ns);
                    $("#J_version").val(_showVersion(version));
                    $("#J_namespace").val(ns);
                }

                renderNamespace();

                function renderCandidates(candidates) {

                    if (candidates && candidates[0]) {
                        var ret = [];
                        for (var index in candidates) {
                            var each = candidates[index];
                            const version = each.version;
                            let show = "云=端";
                            if (queryConfig.version > version) {
                                show = "云>端";
                            } else if (queryConfig.version < version) {
                                show = "云<端";
                            }

                            ret.push('<tr>');
                            ret.push('<td style="width:50px;max-width:50px;word-wrap:break-word;">');
                            ret.push(show);
                            ret.push('</td>');
                            ret.push('<td style="width:200px; max-width:300px; word-wrap:break-word;">');
                            ret.push(_showVersion(each.version));
                            ret.push('</td>');
                            ret.push('<td style="width:200px; max-width:300px; word-wrap:break-word;">');
                            ret.push(each.match);
                            ret.push('</td>');
                            ret.push('</tr>');
                        }
                        $("#J_nsCompare").html(ret.join(''));
                    }

                }

                function renderCompareTable(queryConfig, indexConfig) {
                    const keys = ['env', 'appKey', 'appVersion', 'appIndexVersion']

                    var ret = [];
                    for (var index in keys) {
                        var key = keys[index]
                        var sValue = queryConfig[key];
                        var cValue = indexConfig[key];
                        let show = sValue == cValue;
                        if (key == 'appIndexVersion') {
                            show = "云=端";
                            if (sValue > cValue) {
                                show = "云>端";
                            } else if (sValue < cValue) {
                                show = "云<端";
                            }
                        }

                        ret.push('<tr>');
                        ret.push('<td style="width:50px;max-width:50px;word-wrap:break-word;">');
                        ret.push(key);
                        ret.push('</td>');
                        ret.push('<td style="width:50px;max-width:50px;word-wrap:break-word;">');
                        ret.push(show);
                        ret.push('</td>');
                        ret.push('<td style="width:200px; max-width:300px; word-wrap:break-word;">');
                        ret.push(_showVersion(sValue));
                        ret.push('</td>');
                        ret.push('<td style="width:200px; max-width:300px; word-wrap:break-word;">');
                        ret.push(_showVersion(cValue));
                        ret.push('</td>');
                        ret.push('</tr>');
                    }
                    $("#J_indexCompare").html(ret.join(''));


                }

                function renderContentTable() {
                    const namespace = SecurityUtil.escapeHtml($('#J_namespace').val());
                    console.log('renderContentTable', namespace);
                    if (!namespace) {
                        return;
                    }

                    var ret = [];
                    for (var conf in wvContentConfig) {

                        if (conf.indexOf(namespace) > -1) {
                            var fileValue = wvContentConfig[conf];
                            const loadLevel = fileValue && fileValue['loadLevel'] || '';
                            const version = fileValue && fileValue['version'] || '';
                            ret.push('<tr>');
                            ret.push('<td style="width:50px;max-width:50px;word-wrap:break-word;">');
                            ret.push('<p>name:</p>');
                            ret.push('<p class="thick">');
                            ret.push(conf);
                            ret.push('</p>');
                            ret.push('<p/>');
                            ret.push('<p/>');
                            ret.push('<p>loadLevel:</p>');
                            ret.push('<p class="thick">');
                            ret.push(loadLevel);
                            ret.push('</p>');
                            ret.push('<p/>');
                            ret.push('<p/>');
                            ret.push('<p>version:</p>');
                            ret.push('<p class="thick">');
                            ret.push(_showVersion(version));
                            ret.push('</p>');
                            ret.push('</td>');
                            ret.push('<td style="width:300px; max-width:300px; word-wrap:break-word;">');
                            /* */
                            if (fileValue && fileValue['content']) {
                                const content = fileValue['content'];
                                if (typeof content == 'string') {
                                    ret.push(content);
                                } else {
                                    for (var k in content) {
                                        ret.push(k);
                                        ret.push('=');
                                        ret.push(JSON.stringify(content[k]));
                                        ret.push('<p/>');
                                        ret.push('<p/>');
                                    }
                                }
                            }
                            //ret.push(JSON.stringify(fileValue&&fileValue['content'] || ''));
                            ret.push('</td>');
                            ret.push('</tr>');
                        }


                    }
                    //alert('html>>>'+ret.join(''));
                    $("#J_contentConfig").html(ret.join(''));


                }

                function appendNamespaces() {
                    console.log("appendNamespaces", wvindexConfig, wvContentConfig);
                    const indexConfig = {
                        env: UNKOWN,
                        appKey: wvindexConfig.appKey || UNKOWN,
                        appVersion: wvindexConfig.appVersion || UNKOWN,
                        appIndexVersion: wvindexConfig.appIndexVersion || UNKOWN,

                    }
                    const indexNsArr = wvindexConfig.mergedNamespaces;
                    let candidates = [];

                    for (var i = 0; i < indexNsArr.length; i++) {
                        var each = indexNsArr[i];
                        if (each.name == queryConfig.namespace) {
                            indexConfig.version = each.version;
                            candidates = each.candidates || [];
                            candidates.push(each);
                        }
                    }
                    renderCompareTable(queryConfig, indexConfig);
                    renderCandidates(candidates)
                    renderContentTable();


                }

                wvindexConfig = globalIndex;
                wvContentConfig = globalNamespace;

                function loadOrangeData() {
                    if (window.WindVane) {
                        window.WindVane.call("WVDevelopTool", "configCenterData", "", function (e) {
                            wvContentConfig = JSON.parse(JSON.stringify(e.config));
                            wvindexConfig = JSON.parse(JSON.stringify(e.index));
                            appendNamespaces()
                        }, function (e) {
                            alert('failure:' + JSON.stringify(e) + ", 渲染测试数据~~~");
                            appendNamespaces();
                        });
                    } else {
                        appendNamespaces();
                    }
                }

                function reloadOrangeData() {
                    renderContentTable();
                }

                loadOrangeData()

            </script>


            <!-- 提示框 -->
            <div id="dialogModal" class="modal" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div id="dialogAlert" role="alert">
                        <button type="button" class="close" data-dismiss="modal">
                            <span aria-hidden="true">×</span><span class="sr-only">Close</span>
                        </button>
                        <h4 id="dialogTitle">Success!</h4>
                        <p id="dialogMessage" style="word-break: break-all;"></p>
                    </div>
                </div>
            </div>


            <!-- Demo 内容 结束 -->
        </div>
    </div>
</BODY>
</HTML>
