<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta content="telephone=no" name="format-detection" />
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>Namespace</title>

    <link href="/debug/assets/bootstrap.3.0.3.css" rel="stylesheet">
    <script src="/debug/assets/windvane.3.0.0.js"></script>
    <style>
        .panel-body {
            overflow: scroll;
        }
    </style>
</head>
<body>


<!--<div  style="color:red; font-size:16px; width:100%;text-align:center">-->
<!--  刷新页面，尝试更新配置-->
<!--</div>-->

<br/>

<div class="container-fluid">
    <div id="data" class="row">

        <!--
        <div class="panel panel-default">
          <div onclick="showNamespace('title1')" class="panel-heading">
            <h3 class="panel-title" style="font-weight: bold"><span id="span_title1">+</span>&nbsp;title1</h3>
            <h3 class="panel-title">version:<span style="color:red">11111</span></h3>
            <h3 class="panel-title">loadLevel:<span style="">DEFAULT</span></h3>
          </div>
          <div id="title1" class="panel-body" style="display:none">
              content1
          </div>
        </div>

        <div class="panel panel-default">
          <div onclick="showNamespace('title2')" class="panel-heading">
            <h3 class="panel-title" style="font-weight: bold"><span id="span_title2">+</span>&nbsp;title2</h3>
            <h3 class="panel-title">version:<span style="color:red">22222</span></h3>
            <h3 class="panel-title">loadLevel:<span style="font-weight: bold">HIGH</span></h3>
          </div>
          <div id="title2" class="panel-body" style="display:none">
              content2
          </div>
        </div>

        -->
    </div>
</div>

<script src="/debug/assets/jquery-2.1.4.min.js"></script>
<script src="/debug/assets/bootstrap.3.0.3.js"></script>
<script>




  //     var params = {
  //     "api": "mtop.orange.querydata.debug",
  //     "v":"1.0",
  //     "param":{}
  //   };

  //   window.WindVane.call('MtopWVPlugin', 'send', params, function(e) {
  //     //alert('success' + JSON.stringify(e));
  //   }, function(e) {
  //     alert("mtop.orange.querydata.debug 调用失败:   " + JSON.stringify(e));
  //   });



  function getUrlVars() {
    var vars = [], hash;
    var url = decodeURIComponent(window.location.href);
    var hashes = url.slice(url.indexOf('?')+1).split('&');
    for(var i = 0; i < hashes.length; i++) {
      hash = hashes[i].split('=');
      vars.push(hash[0]);
      vars[hash[0]] = hash[1];
    }
    return vars;
  }

  //var data = getUrlVars()["namespaces"];

  function showNamespace(id){
    //alert("点击了:"+id);
    var value=document.getElementById(id).style.display;
    if("none"===value){
      document.getElementById(id).style.display="block";
      document.getElementById("span_"+id).innerHTML="-";
    }else{
      document.getElementById(id).style.display="none";
      document.getElementById("span_"+id).innerHTML="+";
    }

  }




  window.WindVane.call("WVDevelopTool", "configCenterData", "", function(e) {
    var data = JSON.stringify(e.config);

    var dataObj = JSON.parse(data);


    var container = $("#data").html();
    // 显示 json中的 config
    for (var conf in dataObj) {
      var panel = '<div class="panel panel-default">'+
        '<span class="panel-title">'+dataObj[conf].name+'</span><div onclick=\'showNamespace("'+conf+'")\' class="panel-heading">'+
        '<h3 class="panel-title" style="font-weight: bold"><span id="span_'+conf+'">+</span>&nbsp;'+conf+'</h3>'+
        '<h3 class="panel-title">version:<span style="color:red">'+dataObj[conf].version+'</span></h3>'+
        '<h3 class="panel-title">loadLevel:<span style="';

      if('HIGH'===dataObj[conf].loadLevel)  {
        panel+='font-weight: bold;color:blue';
      }

      panel+='">'+dataObj[conf].loadLevel+'</span></h3>'+
        '</div>'+
        '<div id="'+conf+'" class="panel-body" style="display:none">';

      for (var k in dataObj[conf]) {
        if (k === 'content') {
          panel += 'content: {';
          for (var c in dataObj[conf][k]) {
            panel += '<br/>&nbsp;&nbsp;' + c + ':' + dataObj[conf][k][c];
          }
          panel += '<br/>}<br/>';
        } else {
          panel += k + ':' + dataObj[conf][k] + '<br/>';
        }
      }
      panel += '</div></div>';
      container+=panel;
    }
    //alert(container);
    $("#data").html(container);

  }, function(e) {
    alert('failure:' + JSON.stringify(e));
  });




</script>

</body>
</html>
