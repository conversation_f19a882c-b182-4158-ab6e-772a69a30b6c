<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta content="telephone=no" name="format-detection" />
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>Index</title>

    <link href="/debug/assets/bootstrap.3.0.3.css" rel="stylesheet">
    <script src="/debug/assets/windvane.3.0.0.js"></script>
    <style>
        .panel-body {
            overflow: scroll;
        }
    </style>
</head>
<body>
<!--<div  style="color:red; font-size:16px; width:100%; text-align:center">-->
<!--  刷新页面，尝试更新配置-->

<!--  </div>-->

<div class="container-fluid">
    <div class="row ">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title" id="createTime" style="color:red;font-weight: bold"></h3>
                <h3 class="panel-title" id="appVersion" style="color:red;font-weight: bold"></h3>
            </div>
            <div id="data" class="panel-body">

            </div>
        </div>
        <!--<div id="index" contenteditable="true"></div>-->
    </div>
</div>



<script src="/debug/assets/jquery-2.1.4.min.js"></script>
<script src="/debug/assets/bootstrap.3.0.3.js"></script>

<script>

  //     var params = {
  //     "api": "mtop.orange.querydata.debug",
  //     "v":"1.0",
  //     "param":{}
  //   };

  //   window.WindVane.call('MtopWVPlugin', 'send', params, function(e) {
  //     //alert('success' + JSON.stringify(e));
  //   }, function(e) {
  //     alert("mtop.orange.querydata.debug 调用失败:  " + JSON.stringify(e));
  //   });

  function getUrlVars() {
    var vars = [], hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
    for(var i = 0; i < hashes.length; i++) {
      hash = hashes[i].split('=');
      vars.push(hash[0]);
      vars[hash[0]] = hash[1];
    }
    return vars;
  }

  var index = decodeURIComponent(getUrlVars()["index"]);

  window.WindVane.call("WVDevelopTool", "configCenterData", "", function(e) {
    //window.open("index.html?index="+ JSON.stringify(e.index), '_self');
    index = JSON.stringify(e.index);

    var indexObj = JSON.parse(index);
    var indexCont = $("#data");


    //显示 json中的index
    for (var k in indexObj) {
      var p = "";
      if (k === 'mergedNamespaces') {
        var mns = indexObj[k];
        p += 'mergedNamespaces:<br/>[';
        for (var i=0; i<mns.length; i++) {
          p += '<br/>{';
          var ns = mns[i];
          for (var f in ns) {
            p += '<br/>&nbsp;&nbsp;' + f + ':' + ns[f] + '<br/>';
          }
          p += '}<br/>';
        }
        p += '] </p>';
      } else {

        if(k === 'createTime'){
          $("#createTime").html("createTime : "+indexObj[k]);
        }
        if(k === 'appVersion'){
          var appKey;
          for (var j in indexObj) {
            if(j === 'appKey' || j === 'appkey'){
              appKey=indexObj[j];
              break;
            }
          }
          $("#appVersion").html("发布维度 : "+appKey+"_"+indexObj[k]);
        }
        p = '<p>'+ k  +':' + indexObj[k] + ' </p>';
      }
      indexCont.append(p);
    }

  }, function(e) {
    alert('failure:' + JSON.stringify(e));
  });

  /*
  var indexObj = JSON.parse(index);
  var indexCont = $("#index");

  for (var k in indexObj) {
    var p = "";
    if (k === 'mergedNamespaces') {
      var mns = indexObj[k];
      p += 'mergedNamespaces:</br>[';
      for (var i=0; i<mns.length; i++) {
        p += '</br>{';
        var ns = mns[i];
        for (var f in ns) {
          p += '</br>&nbsp;&nbsp;' + f + ':' + ns[f] + '</br>';
        }
        p += '}</br>';
      }
      p += '] </p>';
    } else {
      p = '<p>'+ k  +':' + indexObj[k] + ' </p>';
    }
    indexCont.append(p);
  }*/
</script>

</body>
</html>
