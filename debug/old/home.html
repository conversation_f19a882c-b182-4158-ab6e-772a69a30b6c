<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>config check</title>

    <link href="/debug/assets/bootstrap.3.0.3.css" rel="stylesheet">
    <script src="/debug/assets/windvane.3.0.0.js"></script>
    <style>
        .container-fluid {
            margin-top: 51px;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row col-xs-6 col-md-4 col-md-offset-4 col-xs-offset-3">
        <p>
            <input class="btn btn-primary" type="button" value="查看索引" onclick="getIndex()" />
        </p>
    </div>
    <div class="row col-xs-6 col-md-4 col-md-offset-4 col-xs-offset-3">
        <p>
            <input class="btn btn-primary" type="button" value="查看Namespace" onclick="getAllConfig()" />
        </p>

    </div>

</div>

<script src="/debug/assets/jquery-2.1.4.min.js"></script>
<script src="/debug/assets/bootstrap.3.0.3.js"></script>

<script>


  function getIndex() {
    window.open("/debug/old/index.html");
    /*
    window.WindVane.call("WVDevelopTool", "configCenterData", "", function(e) {
      //alert(JSON.stringify(e.index));
      window.open("index.html?index="+ JSON.stringify(e.index));
    }, function(e) {
      alert('failure:' + JSON.stringify(e));
    });
    */
  }

  function getAllConfig() {
    window.open("/debug/old/namespace.html");
    /*
    window.WindVane.call("WVDevelopTool", "configCenterData", "", function(e) {
      //alert(JSON.stringify(e.config));
      window.open("namespace.html?namespaces="+encodeURIComponent(JSON.stringify(e.config)));
    }, function(e) {
      alert('failure:' + JSON.stringify(e));
    });
    */
  }
</script>


</body>
</html>
