export default {
  'get /api/business/queryBusinessList.json': (req, res) => {
    res.json(getBusinessListJSON());
  },
  'get /api/business/queryBusinessDetail.json': (req, res) => {
    res.json(getBusinessDetailJSON());
  },
  'get /api/business/insertOrUpdateBusiness.json': (req, res) => {
    res.json(getCommonBooleanJSON());
  }
};
function getCommonJSON(){
  return {
    "errorCode": 1,
    "errorMessage": null,
    "module": {},
    "success": true
  };
}
function getCommonBooleanJSON(){
  return {
    "errorCode": 1,
    "errorMessage": null,
    "module": {result: true},
    "success": true
  };
}

function getBusinessListJSON(){
return {"errorCode":"","errorMsg":"","model":{"content":[{"creator":"87136","creatorNick":"八九","gmtCreate":1564825313000,"gmtModified":1564979965000,"id":13,"isAvailable":"y","memo":"orange操作权限申请 \n使用场景： rex开发平台，需要将diamond配置写入orange \n调用频次： 后台操作使用，<1qps","modifier":"39753","name":"rex-config","ownerNick":"八九","owners":"87136","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"225902","creatorNick":"逸崧","gmtCreate":1564745738000,"gmtModified":1564979976000,"id":12,"isAvailable":"y","memo":"摩天轮数据中心项目，需要调用PublishApiService接口获取发布数据，每分钟查询一次","modifier":"39753","name":"mtl-data-center","ownerNick":"逸崧","owners":"225902","permissionList":["query"],"permissions":"query","status":0,"valid":true},{"creator":"88463","creatorNick":"夜兔","gmtCreate":1564711777000,"gmtModified":1564711870000,"id":11,"isAvailable":"y","memo":"手淘统一降级的客户端配置，业务的后端配置会推到orange，客户端取配置进行策略判断。频次、依赖业务方使用情况，预计上线稳定后每日5次以下","modifier":"39753","name":"kite","ownerNick":"夜兔","owners":"88463","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"165021","creatorNick":"孔捷","gmtCreate":1564390852000,"gmtModified":1564638724000,"id":10,"isAvailable":"y","memo":"sirius项目-搭投域-pageBuilder 配置发布行为仅在特定页面发布时调用，qps小于50","modifier":"165021","name":"global-campaign-pagebuilder-f","ownerNick":"艾锐","owners":"174899","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"74406","creatorNick":"木深","gmtCreate":1564129496000,"gmtModified":1564129645000,"id":9,"isAvailable":"y","memo":"用于埋点可视化配置下发","modifier":"39753","name":"dt-cmp","ownerNick":"木深","owners":"74406","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"38624","creatorNick":"剧辛","gmtCreate":1564024097000,"gmtModified":1564472035000,"id":8,"isAvailable":"y","memo":"盒马塔斯特AB实验平台，推送AB信息到客户端，PD操作后发起推送，调用较少","modifier":"39753","name":"wdk-abtest","ownerNick":"剧辛","owners":"038624","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"39753","creatorNick":"兰茵","gmtCreate":1563438114000,"gmtModified":1563438721000,"id":7,"isAvailable":"y","memo":"UT埋点采样","modifier":"39753","name":"motu-dp","ownerNick":"同宇","owners":"107388","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"58304","creatorNick":"世绩","gmtCreate":1563332244000,"gmtModified":1563332622000,"id":6,"isAvailable":"y","memo":"端计算配置下发, 线上100次量级每天.","modifier":"39753","name":"edge-computer-server","ownerNick":"世绩","owners":"58304","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"26774","creatorNick":"柬之","gmtCreate":1563019313000,"gmtModified":1563019519000,"id":5,"isAvailable":"y","memo":"航旅的北极星访问","modifier":"39753","name":"wctrl","ownerNick":"柬之","owners":"26774","permissionList":["query","push","publish","review"],"permissions":"query,push,publish,review","status":0,"valid":true},{"creator":"65890","creatorNick":"三画","gmtCreate":1562835613000,"gmtModified":1562852982000,"id":4,"isAvailable":"y","memo":"零售通app native使用的部份资源有变更通知app端进行更新，调用频率低，分钟或小时级别才有一次","modifier":"39753","name":"lst-viot","ownerNick":"三画","owners":"65890","permissionList":["query","publish"],"permissions":"query,publish","status":0,"valid":true}],"curPage":1,"pageSize":10,"total":13},"success":true}
}


function getBusinessDetailJSON(){
 return  {"success":true,"errorCode":"","model":{"isAvailable":"y","creator":"167922","gmtModified":1561006150000,"modifier":"39753","memo":"优酷发布中心","permissionList":["query","push","publish","create","update","delete"],"owners":"167922","gmtCreate":1561006150000,"token":"df17724c20cd557f","creatorNick":"167922","permissions":"query,push,publish,create,update,delete","name":"plato-node","id":1,"ownerNick":"167922","status":0},"errorMsg":""}
}
