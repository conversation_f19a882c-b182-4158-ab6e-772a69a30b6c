export default {
    'get /api/namespace/appNamespaceList.json': (req, res) => {
        res.json(getAppNamespaceList());
    },
    'get /api/namespace/namespaceListForMe.json': (req, res) => {
        res.json(getNamespaceListForMe());
    },
    'post /api/namespace/addNamespace.json': (req, res) => {
        res.json(getCommonBooleanJSON());
    },
    'post /api/namespace/updateNamespace.json': (req, res) => {
        res.json(getCommonBooleanJSON());
    },
    'get /api/namespace/queryKnockoutForOfflineNamespace.json': (req, res) => {
        res.json(getQueryKnockoutForOfflineNamespace());
    },
    'post /api/namespace/offlineNamespace.json': (req, res) => {
        res.json(getCommonBooleanJSON());
    },
    'get /api/namespace/auditingNamespace.json': (req, res) => {
        res.json(getCommonBooleanJSON());
    },
    'get /api/namespace/cancelAuditingNamespace.json': (req, res) => {
        res.json(getCommonBooleanJSON());
    },
    'get /api/namespace/getNamespace.json': (req, res) => {
        res.json(getNamespace());
    },
    'get /api/namespace/getNamespaceDetail.json': (req, res) => {
        res.json(getNamespaceDetail());
    },
    'post /api/namespace/loadLevelSubmitAuditing.json': (req, res) => {
        res.json(getLoadLevelAuditingResult());
    },
    'post /api/namespace/availableNamespace.json': (req, res) => {
        res.json(getCommonBooleanJSON());
    },
    'get /api/namespace/queryUnAvailableNamespace.json': (req, res) => {
        res.json(getQueryUnAvailableNamespace());
    },
    'get /api/tools/namespaceTools.json': (req, res) => {
        res.json(getCommonBooleanJSON());
    },
    'get /api/namespace/permissionApply.json': (req, res) => {
        res.json(getPermissionApplyJSON());
    },

};

function notLoginJSON() {
    return {
        "content": false,
        "errors": [{"code": "302", "field": "SSOException", "msg": "JSON request has been redirect "}],
        "hasError": true
    }
}

function getFailJson() {
    return {"errorCode": "STRATEGYS_TOO_MUCH", "errorMsg": "对[*]纬度的策略版本发布不可超过[8]个,请主动覆盖", "success": false}
}

function getCommonJSON() {
    return {
        "errorCode": 1,
        "errorMessage": null,
        "module": {},
        "success": true
    };
}

function getCommonBooleanJSON() {
    return {
        "errorCode": 1,
        "errorMessage": null,
        "module": {result: true},
        "success": true
    };
}

function getAppNamespaceList() {
return {"errorCode":"","errorMsg":"","model":{"app":{"appDetail":"优酷机房和新加坡机房信息。zz","appId":2017032100977,"appKey":"60034114","appName":"兰茵的测试应用-android","appPackageName":"com.taobao.lanyin123214","basicAppKey":"60034114","gmtCreate":1490074235000,"isAvailable":"y","motuAppId":"60034114@android","osType":2,"valid":true},"namespaceList":[{"appKeyOrGroup":"60034114","modules": "415,389","auditingFlag":"free","creator":"system","detail":"ALINN 情景计算","gmtCreate":1557109524000,"gmtCreateTime":"2019-05-06 10:25:24","gmtModified":1571138418000,"gmtModifiedTime":"2019-10-15 19:20:18","id":1813,"isAvailable":"y","loadLevel":0,"name":"android_alinn_contextualComputing_v01_config","namespaceId":"e0f492c1502d4f10b15d890ea8ff8ccd","owners":"83820,85803,67907,83820,辑熙,160112,55556,153970,54723","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"ALINN 人脸识别","gmtCreate":1555406057000,"gmtCreateTime":"2019-04-16 17:14:17","gmtModified":1571138411000,"gmtModifiedTime":"2019-10-15 19:20:11","id":1796,"isAvailable":"y","loadLevel":0,"name":"android_alinn_FaceDetection_v01_config","namespaceId":"1630221035774069b927f4aa54519471","owners":"85803,67907,83820,辑熙,160112,55556,153970,54723","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"ALINN 手势检测","gmtCreate":1557109365000,"gmtCreateTime":"2019-05-06 10:22:45","gmtModified":1571138404000,"gmtModifiedTime":"2019-10-15 19:20:04","id":1811,"isAvailable":"y","loadLevel":0,"name":"android_alinn_HandGesture_v01_config","namespaceId":"31191f9b581d4c6eaef3d2cad44ad957","owners":"85803,67907,83820,辑熙,160112,55556,153970,54723","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"ALINN 列表重排","gmtCreate":1557109475000,"gmtCreateTime":"2019-05-06 10:24:35","gmtModified":1571138397000,"gmtModifiedTime":"2019-10-15 19:19:57","id":1812,"isAvailable":"y","loadLevel":0,"name":"android_alinn_listRerank_v01_config","namespaceId":"402f4515146f4b0dbb8c78da744898ff","owners":"83820,85803,67907,83820,辑熙,160112,55556,153970,54723","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"MNN 宠物识别","gmtCreate":1621217969000,"gmtCreateTime":"2021-05-17 10:19:29","gmtModified":1621217969000,"gmtModifiedTime":"2021-05-17 10:19:29","id":2436,"isAvailable":"y","loadLevel":0,"name":"android_alinn_PetRecognize_v01_config","namespaceId":"a0fe2e9589574176a9ad0a41bdb5e8f3","owners":"83820,54723,83820,153970,58097,100998","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"ALINN 拍立淘","gmtCreate":1583131947000,"gmtCreateTime":"2020-03-02 14:52:27","gmtModified":1583131947000,"gmtModifiedTime":"2020-03-02 14:52:27","id":2046,"isAvailable":"y","loadLevel":0,"name":"android_alinn_pltImageSearch_v01_config","namespaceId":"03c39c53f0444f318636fe76695296c7","owners":"83820,54723,83820,誉阳,153970,58304","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"54723","detail":"发发方式方法","gmtCreate":1578022678000,"gmtCreateTime":"2020-01-03 11:37:58","gmtModified":1578022678000,"gmtModifiedTime":"2020-01-03 11:37:58","id":2016,"isAvailable":"y","loadLevel":0,"name":"android_camerastudio_analycsi-skin_v01_config","namespaceId":"f0d236825af84950b371a7e9fef8d358","owners":"54723,青车","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"CameraStudio camera biztype","gmtCreate":1572946018000,"gmtCreateTime":"2019-11-05 17:26:58","gmtModified":1577080793000,"gmtModifiedTime":"2019-12-23 13:59:53","id":1970,"isAvailable":"y","loadLevel":0,"name":"android_camera_studio_camera_v01_config","namespaceId":"25e6bc468e534f118cdd45554570317f","owners":"83820,54723,153970,58304,83820,誉阳","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"CameraStudio 肌肤检测","gmtCreate":1574935738000,"gmtCreateTime":"2019-11-28 18:08:58","gmtModified":1574935738000,"gmtModifiedTime":"2019-11-28 18:08:58","id":1992,"isAvailable":"y","loadLevel":0,"name":"android_camera_studio_skin_v01_config","namespaceId":"e47186428c5f4af9b5aeea48e4337399","owners":"83820,83820,誉阳,153970,58304","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"CameraStudio 测试","gmtCreate":1575352851000,"gmtCreateTime":"2019-12-03 14:00:51","gmtModified":1575352851000,"gmtModifiedTime":"2019-12-03 14:00:51","id":1994,"isAvailable":"y","loadLevel":0,"name":"android_camera_studio_test_v01_config","namespaceId":"907fe0de67c14aea93650ce6c06f9818","owners":"83820,83820,誉阳,153970,58304","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"CameraStudio 测试","gmtCreate":1579506010000,"gmtCreateTime":"2020-01-20 15:40:10","gmtModified":1579506010000,"gmtModifiedTime":"2020-01-20 15:40:10","id":2025,"isAvailable":"y","loadLevel":0,"name":"android_camera_test_v01","namespaceId":"9c35766c2a67461882b289a732e84019","owners":"83820,54723,83820,誉阳,153970,58304","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"230608","detail":"11233","gmtCreate":1570864035000,"gmtCreateTime":"2019-10-12 15:07:15","gmtModified":1644976828000,"gmtModifiedTime":"2022-02-16 10:00:28","id":1945,"isAvailable":"y","loadLevel":5,"name":"audit_test","namespaceId":"94cbf6de0abf4a0f8ac79992fd6ace85","owners":"169882,39753","testers":"100149","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"CameraStudio 测试","gmtCreate":1583492021000,"gmtCreateTime":"2020-03-06 18:53:41","gmtModified":1583492021000,"gmtModifiedTime":"2020-03-06 18:53:41","id":2051,"isAvailable":"y","loadLevel":0,"name":"camera_studio_test","namespaceId":"ac6d2e31ecf44e66899f91245e32adfc","owners":"54723,83820,誉阳,153970,58304","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"CameraStudio test3","gmtCreate":1583746017000,"gmtCreateTime":"2020-03-09 17:26:57","gmtModified":1583746017000,"gmtModifiedTime":"2020-03-09 17:26:57","id":2053,"isAvailable":"y","loadLevel":0,"name":"camera_studio_test3","namespaceId":"ec15356a082e40b794274f480c4cb9c4","owners":"54723,153970,58304","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"system","detail":"AR配置更新拉取通知","gmtCreate":1618195436000,"gmtCreateTime":"2021-04-12 10:43:56","gmtModified":1618195436000,"gmtModifiedTime":"2021-04-12 10:43:56","id":2421,"isAvailable":"y","loadLevel":0,"name":"edge_computer_update_info","namespaceId":"d42bdd91783b4a72b2ff879268e50780","owners":"54723,83820,153970,58097,100998","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"测试","gmtCreate":1573797068000,"gmtCreateTime":"2019-11-15 13:51:08","gmtModified":1640244353000,"gmtModifiedTime":"2021-12-23 15:25:53","id":1980,"isAvailable":"y","loadLevel":0,"name":"lanyin_customer_config","namespaceId":"444ea8ea588a4fd486e5af9b7f1c7498","owners":"39753,230608,65738","type":3,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"230608","detail":"1432","gmtCreate":1571023695000,"gmtCreateTime":"2019-10-14 11:28:15","gmtModified":1571023695000,"gmtModifiedTime":"2019-10-14 11:28:15","id":1948,"isAvailable":"y","loadLevel":0,"name":"long_test","namespaceId":"642a6736680640fc92b8cb933cff5289","owners":"64508,28727,78396,67596,102537,65892,50715,81205,40039,67772,55601,73777,27745,68116,123171,40405,16533,112296,41622,120331,131844,133847,47399,20202,162430,180422,133380,101991,85803,67907,39753,83820,辑熙,160112,55556,230608","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"88235","detail":"loremtest","gmtCreate":1525929492000,"gmtCreateTime":"2018-05-10 13:18:12","gmtModified":1525929492000,"gmtModifiedTime":"2018-05-10 13:18:12","id":1532,"isAvailable":"y","loadLevel":10,"name":"loremtest","namespaceId":"765ea8d75aaf4cd190145a5f125f8718","owners":"88235,39753","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"Orange 基础配置","gmtCreate":1640337209000,"gmtCreateTime":"2021-12-24 17:13:29","gmtModified":1640337209000,"gmtModifiedTime":"2021-12-24 17:13:29","id":2502,"isAvailable":"y","loadLevel":0,"name":"orange","namespaceId":"39a43a720d094481a52cfc2c8a06eeb0","owners":"39753,177422,60050,100149","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"335967","detail":"test","gmtCreate":1655196286000,"gmtCreateTime":"2022-06-14 16:44:46","gmtModified":1655196286000,"gmtModifiedTime":"2022-06-14 16:44:46","id":2912,"isAvailable":"y","loadLevel":0,"name":"orange-preplan-test","namespaceId":"4d2075a7f8b44e7cae79ae5f6f63b885","owners":"335967,39753","testers":"335967","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"cus test","gmtCreate":1649232844000,"gmtCreateTime":"2022-04-06 16:14:04","gmtModified":1670486297000,"gmtModifiedTime":"2022-12-08 15:58:17","id":2826,"isAvailable":"y","loadLevel":0,"name":"orange_test_custom","namespaceId":"1a4d08f21bcd424186a33d46f72a3340","owners":"177422,60050","subType":99,"testers":"60050","type":3,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"首次发布，不做发布处理","gmtCreate":1641975117000,"gmtCreateTime":"2022-01-12 16:11:57","gmtModified":1670489022000,"gmtModifiedTime":"2022-12-08 16:43:42","id":2509,"isAvailable":"y","loadLevel":0,"name":"orange_test_first","namespaceId":"79b3181ffd5a41adbd9663722dc18cf2","owners":"177422,100149,60050,39753","testers":"60050","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"首次发布自定义处理","gmtCreate":1644480161000,"gmtCreateTime":"2022-02-10 16:02:41","gmtModified":1670489555000,"gmtModifiedTime":"2022-12-08 16:52:35","id":2589,"isAvailable":"y","loadLevel":0,"name":"orange_test_first_custom","namespaceId":"3a1f1a3289f343a79fdd8440f577de72","owners":"177422,60050","subType":1,"testers":"39753","type":3,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"新增配置","gmtCreate":1644571225000,"gmtCreateTime":"2022-02-11 17:20:25","gmtModified":1644995028000,"gmtModifiedTime":"2022-02-16 15:03:48","id":2635,"isAvailable":"y","loadLevel":0,"name":"orange_test_lanyin_stand","namespaceId":"3ba9b23146bc49a7bde39435ffbab01d","owners":"39753,177422","testers":"100149","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"个性化预案测试","gmtCreate":1568029473000,"gmtCreateTime":"2019-09-09 19:44:33","gmtModified":1655195288000,"gmtModifiedTime":"2022-06-14 16:28:08","id":1914,"isAvailable":"y","loadLevel":0,"name":"orange_test_prepan_customer","namespaceId":"0430c551eb9d48728d54fbc875f3fdf6","owners":"177422,100149,335967,39753","subType":1,"testers":"100149,39753","type":3,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"预案中心测试ns","gmtCreate":1566539301000,"gmtCreateTime":"2019-08-23 13:48:21","gmtModified":1655195231000,"gmtModifiedTime":"2022-06-14 16:27:11","id":1901,"isAvailable":"y","loadLevel":0,"name":"orange_test_preplan","namespaceId":"b1e01c7c0df0431c9cd2738ee598b2b3","owners":"39753,177422,77592,335967","testers":"100149","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"39753","detail":"预案中心测试","gmtCreate":1567070568000,"gmtCreateTime":"2019-08-29 17:22:48","gmtModified":1655195246000,"gmtModifiedTime":"2022-06-14 16:27:26","id":1907,"isAvailable":"y","loadLevel":0,"name":"orange_test_preplan2","namespaceId":"abff667e99e44a99b4b254081e38bd32","owners":"77592,39753,106341,335967","testers":"100149","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"169882","detail":"测试namespace配置","gmtCreate":1587716271000,"gmtCreateTime":"2020-04-24 16:17:51","gmtModified":1587716271000,"gmtModifiedTime":"2020-04-24 16:17:51","id":2095,"isAvailable":"y","loadLevel":0,"name":"songxiangtest01","namespaceId":"d42e58e8aac44a68b6a5d388543ec62a","owners":"169882,39753","type":3,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"169882","detail":"测试配置2","gmtCreate":1590137964000,"gmtCreateTime":"2020-05-22 16:59:24","gmtModified":1590137964000,"gmtModifiedTime":"2020-05-22 16:59:24","id":2128,"isAvailable":"y","loadLevel":0,"name":"songxiangtest02","namespaceId":"179cd1a766d2407cb8182314da98a9b0","owners":"169882,39753","type":1,"valid":true},{"appKeyOrGroup":"60034114","auditingFlag":"free","creator":"230608","detail":"33333","gmtCreate":1577073100000,"gmtCreateTime":"2019-12-23 11:51:40","gmtModified":1589445643000,"gmtModifiedTime":"2020-05-14 16:40:43","id":1998,"isAvailable":"y","loadLevel":0,"name":"TS111","namespaceId":"51af830bc9bd4899b4dedc6a146cab0b","owners":"230608,39753","type":1,"valid":true}]},"success":true}
}


function getNamespaceListForMe() {
    return {
        "errorCode": "", "errorMsg": "", "model": [{
            "app": {
                "appDetail": "饿了么",
                "appId": 2018051674581,
                "appKey": "24894833",
                "appName": "饿了么-ios",
                "basicAppKey": "24894833",
                "gmtCreate": 1526467155000,
                "isAvailable": "y",
                "motuAppId": "24894833@iphoneos",
                "osType": 1,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "24894833",
                "auditingFlag": "free",
                "creator": "83994",
                "detail": "哥伦布问卷调研工具配置项",
                "gmtCreate": 1616399315000,
                "gmtCreateTime": "2021-03-22 15:48:35",
                "gmtModified": 1637313930000,
                "gmtModifiedTime": "2021-11-19 17:25:30",
                "id": 6454,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "columbus",
                "namespaceId": "343db4edfbf84a9ba95f6f91fe246e6a",
                "owners": "83994,74151,261481,78266",
                "modules": "415,389",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24894833",
                "auditingFlag": "free",
                "creator": "249012",
                "detail": "饿了么容器通用配置项",
                "gmtCreate": 1602752251000,
                "gmtCreateTime": "2020-10-15 16:57:31",
                "gmtModified": 1645408771000,
                "gmtModifiedTime": "2022-02-21 09:59:31",
                "id": 5721,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "triver_common_config",
                "namespaceId": "af96d2da6dec46d3991009f925c04da4",
                "owners": "249012,78266,261481,83994,74151",
                "testers": "78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24894833",
                "auditingFlag": "free",
                "creator": "249012",
                "detail": "Lriver数据预取配置group（Mtop API请求）",
                "gmtCreate": 1592382456000,
                "gmtCreateTime": "2020-06-17 16:27:36",
                "gmtModified": 1645510696000,
                "gmtModifiedTime": "2022-02-22 14:18:16",
                "id": 5023,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "l_river_prefetch",
                "namespaceId": "0d1498b53bff4343a81d1574414b21a7",
                "owners": "249012,78266,261481,83994,74151,73943",
                "testers": "78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24894833",
                "auditingFlag": "free",
                "creator": "249012",
                "detail": "WindVane导航样式配置group",
                "gmtCreate": 1592382587000,
                "gmtCreateTime": "2020-06-17 16:29:47",
                "gmtModified": 1645510705000,
                "gmtModifiedTime": "2022-02-22 14:18:25",
                "id": 5024,
                "isAvailable": "y",
                "loadLevel": 0,
                "modules": "415,389",
                "name": "ios_ele_h5_ui",
                "namespaceId": "9a509a7e7f474ebfb509ef7f2f000518",
                "owners": "249012,78266,261481,224687",
                "testers": "78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24894833",
                "auditingFlag": "free",
                "creator": "78150",
                "detail": "饿了么Lriver统一切流",
                "gmtCreate": 1584067223000,
                "gmtCreateTime": "2020-03-13 10:40:23",
                "gmtModified": 1645151619000,
                "gmtModifiedTime": "2022-02-18 10:33:39",
                "id": 4541,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "l_river_group",
                "namespaceId": "611546c15fc24693a3ec14ddbe9f391b",
                "owners": "78266,249012,261481,47866,83994,224687",
                "modules": "389",
                "testers": "78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24894833",
                "auditingFlag": "free",
                "creator": "249012",
                "detail": "饿了么容器重要配置项",
                "gmtCreate": 1609146149000,
                "gmtCreateTime": "2020-12-28 17:02:29",
                "gmtModified": 1645510716000,
                "gmtModifiedTime": "2022-02-22 14:18:36",
                "id": 6079,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "triver_importent_config",
                "namespaceId": "017e635a214446a59f81e0b95f6c10f7",
                "owners": "249012,261481,78266,74151,83994",
                "testers": "78266",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "饿了么",
                "appId": 2018051674580,
                "appKey": "24895413",
                "appName": "饿了么-android",
                "appPackageName": "me.ele",
                "basicAppKey": "24895413",
                "gmtCreate": 1526467097000,
                "isAvailable": "y",
                "motuAppId": "24895413@android",
                "osType": 2,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "饿了么路由相关配置项",
                "gmtCreate": 1594094024000,
                "gmtCreateTime": "2020-07-07 11:53:44",
                "gmtModified": 1612770516000,
                "gmtModifiedTime": "2021-02-08 15:48:36",
                "id": 5133,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ele_route_config",
                "namespaceId": "c078b964292b41659fe105e6d82c9bfb",
                "owners": "261481,162154,197522,47866",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "APM自定义配置",
                "gmtCreate": 1625452137000,
                "gmtCreateTime": "2021-07-05 10:28:57",
                "gmtModified": 1625452137000,
                "gmtModifiedTime": "2021-07-05 10:28:57",
                "id": 6925,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "apm_adapter",
                "namespaceId": "292ac47783854a0a90b12c36c714a994",
                "owners": "261481,78266,247077",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "166696",
                "detail": "WindVane配置",
                "gmtCreate": 1576762826000,
                "gmtCreateTime": "2019-12-19 21:40:26",
                "gmtModified": 1649259346000,
                "gmtModifiedTime": "2022-04-06 23:35:46",
                "id": 4123,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "WindVane",
                "namespaceId": "f822f98e2d724207b92e4bb3ee733f5e",
                "owners": "180779,47866,19169,78266,261481,307254",
                "testers": "78266,307254",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "饿了么APM相关配置项",
                "modules": "415",
                "gmtCreate": 1602483127000,
                "gmtCreateTime": "2020-10-12 14:12:07",
                "gmtModified": 1630484818000,
                "gmtModifiedTime": "2021-09-01 16:26:58",
                "id": 5678,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "applicationmonitor",
                "namespaceId": "f1b52ec7dc0e46928869c417ebe0d2c7",
                "owners": "261481,47866,180779,78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "ariver 通用配置",
                "gmtCreate": 1594640221000,
                "gmtCreateTime": "2020-07-13 19:37:01",
                "gmtModified": 1645178895000,
                "gmtModifiedTime": "2022-02-18 18:08:15",
                "id": 5177,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ariver_common_config",
                "namespaceId": "055678d091c8487a83e4b7bbc1970c2f",
                "owners": "261481,249012,78266,307254",
                "testers": "78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "小程序windmill通用配置",
                "gmtCreate": 1594795954000,
                "gmtCreateTime": "2020-07-15 14:52:34",
                "gmtModified": 1640700351000,
                "gmtModifiedTime": "2021-12-28 22:05:51",
                "id": 5184,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "group_windmill_common",
                "namespaceId": "6a64f7048c0347d5ae53cb6881f76aa1",
                "owners": "261481,249012,78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "小程序Triver通用配置项",
                "gmtCreate": 1602743906000,
                "gmtCreateTime": "2020-10-15 14:38:26",
                "gmtModified": 1645178876000,
                "gmtModifiedTime": "2022-02-18 18:07:56",
                "id": 5712,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "triver_common_config",
                "namespaceId": "367c0afd88554c19ad5c632d380f0061",
                "owners": "261481,47866,78266,307254",
                "testers": "78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "166696",
                "detail": "预加载任务配置",
                "gmtCreate": 1582636321000,
                "gmtCreateTime": "2020-02-25 21:12:01",
                "gmtModified": 1646321109000,
                "gmtModifiedTime": "2022-03-03 23:25:09",
                "id": 4441,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "TSchedule",
                "namespaceId": "d0fb8f00484840a8bdf590c3adcb5a66",
                "owners": "47866,261481,307254,78266",
                "testers": "78266,307254",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "triver核心小程序配置项",
                "gmtCreate": 1612672596000,
                "gmtCreateTime": "2021-02-07 12:36:36",
                "gmtModified": 1627008965000,
                "gmtModifiedTime": "2021-07-23 10:56:05",
                "id": 6283,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "triver_important_config",
                "namespaceId": "108025fd13624918a8e66f6d64a6263a",
                "owners": "261481,249012,47866,扇稚,78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "307254",
                "detail": "饿了么PHA容器配置",
                "gmtCreate": 1623322954000,
                "gmtCreateTime": "2021-06-10 19:02:34",
                "gmtModified": 1647263731000,
                "gmtModifiedTime": "2022-03-14 21:15:31",
                "id": 6806,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_pha_config",
                "namespaceId": "7abbbd04b68d4b91993628cadd00217d",
                "owners": "307254,47866,261481,187312,78266",
                "testers": "78266,307254",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "triver通用白名单配置",
                "gmtCreate": 1632452206000,
                "gmtCreateTime": "2021-09-24 10:56:46",
                "gmtModified": 1648620265000,
                "gmtModifiedTime": "2022-03-30 14:04:25",
                "id": 7351,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "triver_white_list_config",
                "namespaceId": "09ef2ed32d804caaace007e77130f82c",
                "owners": "261481,307254,47866,249012,78266",
                "testers": "78266",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "85973",
                "detail": "饿了么Lriver切流配置",
                "gmtCreate": 1584067737000,
                "gmtCreateTime": "2020-03-13 10:48:57",
                "gmtModified": 1634089990000,
                "gmtModifiedTime": "2021-10-13 09:53:10",
                "id": 4542,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "l_river_group",
                "namespaceId": "e22c9abb79604d75b7340b9e85336823",
                "owners": "78266,261481,47866,307254",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "饿了么哥伦布相关配置",
                "gmtCreate": 1637297916000,
                "gmtCreateTime": "2021-11-19 12:58:36",
                "gmtModified": 1637299092000,
                "gmtModifiedTime": "2021-11-19 13:18:12",
                "id": 7628,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "columbus",
                "namespaceId": "0fd27876b5544a02a0d265fedec86ccf",
                "owners": "261481,83994,78266,47866",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "187315",
                "detail": "WindVane_common_config",
                "gmtCreate": 1609314793000,
                "gmtCreateTime": "2020-12-30 15:53:13",
                "gmtModified": 1627361374000,
                "gmtModifiedTime": "2021-07-27 12:49:34",
                "id": 6097,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "WindVane_common_config",
                "namespaceId": "42612512750f4289b46324cab59cfb05",
                "owners": "187315,261481,307254,78266,南决",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "饿了么H5页UI相关配置",
                "gmtCreate": 1593674028000,
                "gmtCreateTime": "2020-07-02 15:13:48",
                "gmtModified": 1606296396000,
                "gmtModifiedTime": "2020-11-25 17:26:36",
                "id": 5112,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ele_h5_ui",
                "namespaceId": "d5c755de301b4bf2bf433f31d59c1f5e",
                "owners": "261481,85973,249012,扇稚,187315",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24895413",
                "auditingFlag": "free",
                "creator": "261481",
                "detail": "小程序预取配置",
                "gmtCreate": 1593674347000,
                "gmtCreateTime": "2020-07-02 15:19:07",
                "gmtModified": 1650249268000,
                "gmtModifiedTime": "2022-04-18 10:34:28",
                "id": 5113,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "l_river_prefetch",
                "namespaceId": "06d9e862e6984d88b5c3f499cc3b6001",
                "owners": "261481,85973,249012,78266,255761",
                "testers": "78266",
                "type": 1,
                "valid": true
            }]
        }], "success": true
    }
    return {
        "errorCode": "", "errorMsg": "", "model": [{
            "app": {
                "appDetail": "手机淘宝",
                "appId": 2017032401903,
                "appKey": "4272",
                "appName": "手机淘宝-android",
                "appPackageName": "com.taobao.taobao",
                "gmtCreate": 1490343869000,
                "isAvailable": "y",
                "motuAppId": "4272@android",
                "osType": 2,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "orange测试11",
                "developers": "",
                "gmtCreate": 1442549095000,
                "gmtCreateTime": "2015-09-18 12:04:55",
                "gmtModified": 1644920904000,
                "gmtModifiedTime": "2022-02-15 18:28:24",
                "id": 63,
                "isAvailable": "y",
                "loadLevel": 5,
                "name": "orange_test",
                "namespaceId": "6834298cd7d142e588232881f154b4ee",
                "owners": "若存,亿刀,游僧,执水,子矜,鸿驹,子晦,兰茵,路叶,孤星,星诀",
                "reviewers": "",
                "testers": "骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "动态配置，能够远程控制整个域名白名单",
                "developers": "",
                "gmtCreate": 1483951414000,
                "gmtCreateTime": "2017-01-09 16:43:34",
                "gmtModified": 1558331298000,
                "gmtModifiedTime": "2019-05-20 13:48:18",
                "id": 835,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "orange",
                "namespaceId": "d65c3533620a4bd2ae3a1fe5cf8b1b03",
                "owners": "悟二,游僧,岽篱,宸銮,寥望,兰茵",
                "reviewers": "",
                "testers": "",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "测试default级别的namespace",
                "developers": "",
                "gmtCreate": 1487146647000,
                "gmtCreateTime": "2017-02-15 16:17:27",
                "gmtModified": 1642758123000,
                "gmtModifiedTime": "2022-01-21 17:42:03",
                "id": 876,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "wuer_test",
                "namespaceId": "f93d5761b9c74f03abd86b3055797b86",
                "owners": "不达,qingchong.fqc,寥望,兰茵",
                "reviewers": "",
                "testers": "",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "hehe=haha",
                "gmtCreate": 1568719085000,
                "gmtCreateTime": "2019-09-17 19:18:05",
                "gmtModified": 1644994800000,
                "gmtModifiedTime": "2022-02-16 15:00:00",
                "id": 1923,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "shareplatform_bizlist1",
                "namespaceId": "eca64c0ae8134d86960011687e75d229",
                "owners": "daikui.dk,玄苏,兰茵",
                "testers": "骊驹,兰茵",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "tst",
                "gmtCreate": 1516182761000,
                "gmtCreateTime": "2018-01-17 17:52:41",
                "gmtModified": 1558331293000,
                "gmtModifiedTime": "2019-05-20 13:48:13",
                "id": 1175,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "test-custom",
                "namespaceId": "1482b7ce79124abda75279b62fe2d0c1",
                "owners": "昊浪,qingchong.fqc,笛墨,hongju.wp,寥望,兰茵",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "首次不发布",
                "gmtCreate": 1641977239000,
                "gmtCreateTime": "2022-01-12 16:47:19",
                "gmtModified": 1641977239000,
                "gmtModifiedTime": "2022-01-12 16:47:19",
                "id": 2511,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_first",
                "namespaceId": "95d45e280f354045ba1bb0c71dc13cfa",
                "owners": "兰茵,玄苏,骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "www",
                "gmtCreate": 1642488532000,
                "gmtCreateTime": "2022-01-18 14:48:52",
                "gmtModified": 1642488532000,
                "gmtModifiedTime": "2022-01-18 14:48:52",
                "id": 2512,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_second",
                "namespaceId": "d9023228244c41429d855cc3908c6685",
                "owners": "兰茵,骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "测试",
                "gmtCreate": 1522039665000,
                "gmtCreateTime": "2018-03-26 12:47:45",
                "gmtModified": 1558331308000,
                "gmtModifiedTime": "2019-05-20 13:48:28",
                "id": 1503,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "json-type-default",
                "namespaceId": "7ae7e4c107504928b0cb19cf422b6703",
                "owners": "悟二,岽篱,寥望,兰茵",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "单元测试灰度",
                "gmtCreate": 1643077845000,
                "gmtCreateTime": "2022-01-25 10:30:45",
                "gmtModified": 1643077845000,
                "gmtModifiedTime": "2022-01-25 10:30:45",
                "id": 2528,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_push",
                "namespaceId": "10c4aa2332744643b036acb2053b7a20",
                "owners": "兰茵,玄苏",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "test_version",
                "gmtCreate": 1524124919000,
                "gmtCreateTime": "2018-04-19 16:01:59",
                "gmtModified": 1562056659000,
                "gmtModifiedTime": "2019-07-02 16:37:39",
                "id": 1524,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "test_version",
                "namespaceId": "a2015c38d6d248f6941e156ae5dd91a3",
                "owners": "悟二,岽篱,寥望,兰茵,君慢",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "星缕测试应用999",
                "appId": 2020021600398,
                "appKey": "60043875",
                "appName": "星缕测试应用999-windows_pc",
                "gmtCreate": 1581855462000,
                "isAvailable": "y",
                "motuAppId": "60043875@",
                "osType": 7,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "60043875",
                "auditingFlag": "free",
                "detail": "test",
                "gmtCreate": 1644807208000,
                "gmtCreateTime": "2022-02-14 10:53:28",
                "gmtModified": 1647311437000,
                "gmtModifiedTime": "2022-03-15 10:30:37",
                "id": 2636,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_xuansu_test",
                "namespaceId": "dc87ad64b23c4d1a9537598aacc1d2ce",
                "owners": "玄苏,兰茵",
                "subType": 1,
                "testers": "兰茵",
                "type": 3,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "优酷机房和新加坡机房信息。zz",
                "appId": 2017032100977,
                "appKey": "60034114",
                "appName": "兰茵的测试应用-android",
                "appPackageName": "com.taobao.lanyin123214",
                "basicAppKey": "60034114",
                "gmtCreate": 1490074235000,
                "isAvailable": "y",
                "motuAppId": "60034114@android",
                "osType": 2,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "首次发布自定义处理",
                "gmtCreate": 1644480161000,
                "gmtCreateTime": "2022-02-10 16:02:41",
                "gmtModified": 1644480238000,
                "gmtModifiedTime": "2022-02-10 16:03:58",
                "id": 2589,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_first_custom",
                "namespaceId": "3a1f1a3289f343a79fdd8440f577de72",
                "owners": "兰茵,玄苏",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "测试namespace配置",
                "gmtCreate": 1587716271000,
                "gmtCreateTime": "2020-04-24 16:17:51",
                "gmtModified": 1587716271000,
                "gmtModifiedTime": "2020-04-24 16:17:51",
                "id": 2095,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "songxiangtest01",
                "namespaceId": "d42e58e8aac44a68b6a5d388543ec62a",
                "owners": "松香,兰茵",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "新增配置",
                "gmtCreate": 1644571225000,
                "gmtCreateTime": "2022-02-11 17:20:25",
                "gmtModified": 1644995028000,
                "gmtModifiedTime": "2022-02-16 15:03:48",
                "id": 2635,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_lanyin_stand",
                "namespaceId": "3ba9b23146bc49a7bde39435ffbab01d",
                "owners": "兰茵,玄苏",
                "testers": "骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "测试配置2",
                "gmtCreate": 1590137964000,
                "gmtCreateTime": "2020-05-22 16:59:24",
                "gmtModified": 1590137964000,
                "gmtModifiedTime": "2020-05-22 16:59:24",
                "id": 2128,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "songxiangtest02",
                "namespaceId": "179cd1a766d2407cb8182314da98a9b0",
                "owners": "松香,兰茵",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "预案中心测试ns",
                "gmtCreate": 1566539301000,
                "gmtCreateTime": "2019-08-23 13:48:21",
                "gmtModified": 1648193452000,
                "gmtModifiedTime": "2022-03-25 15:30:52",
                "id": 1901,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_preplan",
                "namespaceId": "b1e01c7c0df0431c9cd2738ee598b2b3",
                "owners": "39753,230608,177422,77592",
                "testers": "100149",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "预案中心测试",
                "gmtCreate": 1567070568000,
                "gmtCreateTime": "2019-08-29 17:22:48",
                "gmtModified": 1583893997000,
                "gmtModifiedTime": "2020-03-11 10:33:17",
                "id": 1907,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_preplan2",
                "namespaceId": "abff667e99e44a99b4b254081e38bd32",
                "owners": "笛墨,兰茵,罗神,若存,楚东",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "个性化预案测试",
                "gmtCreate": 1568029473000,
                "gmtCreateTime": "2019-09-09 19:44:33",
                "gmtModified": 1644976792000,
                "gmtModifiedTime": "2022-02-16 09:59:52",
                "id": 1914,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_prepan_customer",
                "namespaceId": "0430c551eb9d48728d54fbc875f3fdf6",
                "owners": "玄苏,骊驹",
                "subType": 1,
                "testers": "骊驹,兰茵",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "11233",
                "gmtCreate": 1570864035000,
                "gmtCreateTime": "2019-10-12 15:07:15",
                "gmtModified": 1644976828000,
                "gmtModifiedTime": "2022-02-16 10:00:28",
                "id": 1945,
                "isAvailable": "y",
                "loadLevel": 5,
                "name": "audit_test",
                "namespaceId": "94cbf6de0abf4a0f8ac79992fd6ace85",
                "owners": "松香,lanyin.smz",
                "testers": "骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "1432",
                "gmtCreate": 1571023695000,
                "gmtCreateTime": "2019-10-14 11:28:15",
                "gmtModified": 1571023695000,
                "gmtModifiedTime": "2019-10-14 11:28:15",
                "id": 1948,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "long_test",
                "namespaceId": "642a6736680640fc92b8cb933cff5289",
                "owners": "64508,28727,78396,67596,102537,65892,50715,81205,40039,67772,55601,73777,27745,68116,123171,40405,16533,112296,41622,120331,131844,133847,47399,20202,162430,180422,133380,101991,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖,daikui.dk",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "测试",
                "gmtCreate": 1573797068000,
                "gmtCreateTime": "2019-11-15 13:51:08",
                "gmtModified": 1640244353000,
                "gmtModifiedTime": "2021-12-23 15:25:53",
                "id": 1980,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "lanyin_customer_config",
                "namespaceId": "444ea8ea588a4fd486e5af9b7f1c7498",
                "owners": "兰茵,若存,晏紫",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "Orange 基础配置",
                "gmtCreate": 1640337209000,
                "gmtCreateTime": "2021-12-24 17:13:29",
                "gmtModified": 1640337209000,
                "gmtModifiedTime": "2021-12-24 17:13:29",
                "id": 2502,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange",
                "namespaceId": "39a43a720d094481a52cfc2c8a06eeb0",
                "owners": "兰茵,玄苏,八风,骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "首次发布，不做发布处理",
                "gmtCreate": 1641975117000,
                "gmtCreateTime": "2022-01-12 16:11:57",
                "gmtModified": 1641975117000,
                "gmtModifiedTime": "2022-01-12 16:11:57",
                "id": 2509,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_first",
                "namespaceId": "79b3181ffd5a41adbd9663722dc18cf2",
                "owners": "兰茵,玄苏,骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "33333",
                "gmtCreate": 1577073100000,
                "gmtCreateTime": "2019-12-23 11:51:40",
                "gmtModified": 1589445643000,
                "gmtModifiedTime": "2020-05-14 16:40:43",
                "id": 1998,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "TS111",
                "namespaceId": "51af830bc9bd4899b4dedc6a146cab0b",
                "owners": "若存,兰茵",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034114",
                "auditingFlag": "free",
                "detail": "loremtest",
                "gmtCreate": 1525929492000,
                "gmtCreateTime": "2018-05-10 13:18:12",
                "gmtModified": 1525929492000,
                "gmtModifiedTime": "2018-05-10 13:18:12",
                "id": 1532,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "loremtest",
                "namespaceId": "765ea8d75aaf4cd190145a5f125f8718",
                "owners": "肇丰,兰茵",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "手机淘宝",
                "appId": 2017032401905,
                "appKey": "531772",
                "appName": "手机淘宝-ios",
                "gmtCreate": 1490343869000,
                "isAvailable": "y",
                "motuAppId": "531772@iphoneos",
                "osType": 1,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "531772",
                "auditingFlag": "free",
                "detail": "sss",
                "gmtCreate": 1588847299000,
                "gmtCreateTime": "2020-05-07 18:28:19",
                "gmtModified": 1648115041000,
                "gmtModifiedTime": "2022-03-24 17:44:01",
                "id": 2103,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "owners": "39753,230608,180856",
                "testers": "100149",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "531772",
                "auditingFlag": "free",
                "detail": "test",
                "gmtCreate": 1644563941000,
                "gmtCreateTime": "2022-02-11 15:19:01",
                "gmtModified": 1644564227000,
                "gmtModifiedTime": "2022-02-11 15:23:47",
                "id": 2619,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_xuansu_test_3",
                "namespaceId": "d8c9d90ced4d4b57b660a581be19cdaf",
                "owners": "玄苏,兰茵",
                "subType": 1,
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "531772",
                "auditingFlag": "free",
                "detail": "orange测试",
                "developers": "",
                "gmtCreate": 1442549074000,
                "gmtCreateTime": "2015-09-18 12:04:34",
                "gmtModified": 1644560379000,
                "gmtModifiedTime": "2022-02-11 14:19:39",
                "id": 62,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test",
                "namespaceId": "cfdfa429966f47dba090ec6663272380",
                "owners": "亿刀,游僧,执水,孤星,怀朔,子矜,廷瑞,子晦,道彻,玄苏,兰茵",
                "reviewers": "",
                "subType": 99,
                "testers": "",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "531772",
                "auditingFlag": "free",
                "detail": "1123www",
                "gmtCreate": 1573546790000,
                "gmtCreateTime": "2019-11-12 16:19:50",
                "gmtModified": 1589515020000,
                "gmtModifiedTime": "2020-05-15 11:57:00",
                "id": 1977,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_ruocun_test",
                "namespaceId": "2d2918a4e60343c69434561ca14c2a9b",
                "owners": "兰茵,罗神,daikui.dk,飘零,遇风",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "531772",
                "auditingFlag": "free",
                "detail": "test",
                "gmtCreate": 1643097257000,
                "gmtCreateTime": "2022-01-25 15:54:17",
                "gmtModified": 1643097257000,
                "gmtModifiedTime": "2022-01-25 15:54:17",
                "id": 2531,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "xuansu_test",
                "namespaceId": "969e6d9e8a3b4a1db59d8a7a7912e623",
                "owners": "玄苏,兰茵",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "531772",
                "auditingFlag": "free",
                "detail": "test",
                "gmtCreate": 1643112146000,
                "gmtCreateTime": "2022-01-25 20:02:26",
                "gmtModified": 1644801716000,
                "gmtModifiedTime": "2022-02-14 09:21:56",
                "id": 2536,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_xuansu_test",
                "namespaceId": "9a670c12e55a4a339b89d48ad14e8393",
                "owners": "玄苏,兰茵,若存",
                "subType": 1,
                "testers": "骊驹",
                "type": 3,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "fsgsg, 测试metaq,add to fregata",
                "appId": 2017112305832,
                "appKey": "60039542",
                "appName": "兰茵的应用33-ios",
                "basicAppKey": "60039542",
                "gmtCreate": 1511419565000,
                "isAvailable": "y",
                "motuAppId": "60039542@iphoneos",
                "osType": 1,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "单元测试，请不要手工操作",
                "gmtCreate": 1644460893000,
                "gmtCreateTime": "2022-02-10 10:41:33",
                "gmtModified": 1644460893000,
                "gmtModifiedTime": "2022-02-10 10:41:33",
                "id": 2580,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_preplan_single",
                "namespaceId": "f58431b94b83423794617f8b9057ec7f",
                "owners": "兰茵,玄苏,骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "单元测试，请不要手工操作",
                "gmtCreate": 1644460928000,
                "gmtCreateTime": "2022-02-10 10:42:08",
                "gmtModified": 1644460928000,
                "gmtModifiedTime": "2022-02-10 10:42:08",
                "id": 2581,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_preplan_batch",
                "namespaceId": "ae0c8e990b4945b9a489984eae2a33f2",
                "owners": "兰茵,玄苏,骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "单元测试，请不要操作",
                "gmtCreate": 1644461020000,
                "gmtCreateTime": "2022-02-10 10:43:40",
                "gmtModified": 1644461020000,
                "gmtModifiedTime": "2022-02-10 10:43:40",
                "id": 2582,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_preplan_check",
                "namespaceId": "83997336c3d844da80d2e418fcd9216b",
                "owners": "兰茵,玄苏,骊驹",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "testSubmit",
                "gmtCreate": 1558594877000,
                "gmtCreateTime": "2019-05-23 15:01:17",
                "gmtModified": 1575535685000,
                "gmtModifiedTime": "2019-12-05 16:48:05",
                "id": 1822,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_v1",
                "namespaceId": "b8e56cd0e62f4624b78be2bf1e53c3c9",
                "owners": "daikui.dk,lanyin.smz,罗神",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "第一个版本不要发布",
                "gmtCreate": 1586938506000,
                "gmtCreateTime": "2020-04-15 16:15:06",
                "gmtModified": 1586938506000,
                "gmtModifiedTime": "2020-04-15 16:15:06",
                "id": 2090,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_first",
                "namespaceId": "d1a1960c1003424ab003355f239b053f",
                "owners": "兰茵,若存",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "单元测试用例：上下线",
                "gmtCreate": 1642757971000,
                "gmtCreateTime": "2022-01-21 17:39:31",
                "gmtModified": 1648126591000,
                "gmtModifiedTime": "2022-03-24 20:56:31",
                "id": 2523,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_online",
                "namespaceId": "b120b10d67084065bcc55158580a4f92",
                "owners": "兰茵,玄苏",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "单元测试发布配置，个人不要操作",
                "gmtCreate": 1642998689000,
                "gmtCreateTime": "2022-01-24 12:31:29",
                "gmtModified": 1642998689000,
                "gmtModifiedTime": "2022-01-24 12:31:29",
                "id": 2525,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_publish",
                "namespaceId": "131e9c7c7c7a4fb9a34d3b294fb4ecd2",
                "owners": "兰茵,玄苏",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "测试描述update1648126591485",
                "gmtCreate": 1643010908000,
                "gmtCreateTime": "2022-01-24 15:55:08",
                "gmtModified": 1648126592000,
                "gmtModifiedTime": "2022-03-24 20:56:32",
                "id": 2526,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_update",
                "namespaceId": "0e9551abc72d4f2fb17b3e6c8a424932",
                "owners": "daikui.dk,玄苏,system,39753",
                "testers": "39753",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "单元测试",
                "gmtCreate": 1643173436000,
                "gmtCreateTime": "2022-01-26 13:03:56",
                "gmtModified": 1643173436000,
                "gmtModifiedTime": "2022-01-26 13:03:56",
                "id": 2537,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_junit_publish_steps",
                "namespaceId": "69ca4e982323492c95f151c0bdca87fd",
                "owners": "兰茵,玄苏",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "单元测试创建1648194418877",
                "gmtCreate": 1648194419000,
                "gmtCreateTime": "2022-03-25 15:46:59",
                "gmtModified": 1648194419000,
                "gmtModifiedTime": "2022-03-25 15:46:59",
                "id": 2794,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "orange_test_junit_create",
                "namespaceId": "a3537954314641c1af7d2f8eff3155a7",
                "owners": "230608,177422,39753",
                "testers": "39753",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "free",
                "detail": "sss",
                "gmtCreate": 1580958690000,
                "gmtCreateTime": "2020-02-06 11:11:30",
                "gmtModified": 1580958690000,
                "gmtModifiedTime": "2020-02-06 11:11:30",
                "id": 2036,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_nopublish",
                "namespaceId": "85c99b202fa44d6881ca5dee168fbeea",
                "owners": "兰茵,若存",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60039542",
                "auditingFlag": "1b8908ce-6b4e-4614-9bb6-49fd830b60f1",
                "detail": "lanyin",
                "gmtCreate": 1581911139000,
                "gmtCreateTime": "2020-02-17 11:45:39",
                "gmtModified": 1646811293000,
                "gmtModifiedTime": "2022-03-09 15:34:53",
                "id": 2039,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_lanyin_ns",
                "namespaceId": "b803c779cbd2499f842941edf7e2ab33",
                "owners": "若存,罗神,兰茵",
                "testers": "兰茵,玄苏",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "君慢测试",
                "appId": 2017031600961,
                "appKey": "60034063",
                "appName": "兰茵的双十是-yunos",
                "appPackageName": "com.mappcenter.lanyin.test",
                "gmtCreate": 1489662664000,
                "isAvailable": "y",
                "motuAppId": "60034063@android",
                "osType": 4,
                "valid": true
            },
            "namespaceList": [{
                "appKeyOrGroup": "60034063",
                "auditingFlag": "free",
                "detail": "fdfDD",
                "gmtCreate": 1571029277000,
                "gmtCreateTime": "2019-10-14 13:01:17",
                "gmtModified": 1573531053000,
                "gmtModifiedTime": "2019-11-12 11:57:33",
                "id": 1949,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "publish_test",
                "namespaceId": "3107d1918a254958b394d034ee151a0a",
                "owners": "177422,39753",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "60034063",
                "auditingFlag": "free",
                "detail": "Orange测试",
                "gmtCreate": 1577945302000,
                "gmtCreateTime": "2020-01-02 14:08:22",
                "gmtModified": 1577945302000,
                "gmtModifiedTime": "2020-01-02 14:08:22",
                "id": 2014,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_lanyin_test",
                "namespaceId": "a1b75ae744674c59a95a55fee09262b8",
                "owners": "177422,39753",
                "type": 1,
                "valid": true
            }]
        }], "success": true
    }
    return {
        "errorCode": "",
        "errorMsg": "",
        "model": [{
            "app": {
                "appDetail": "盒马手机客户端",
                "appKey": "23228014",
                "appName": "盒马手机客户端-android",
                "appPackageName": "com.wudaokou.hippo",
                "motuAppId": "23228014@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23228014",
                "auditingFlag": "free",
                "detail": "ALINN 用户行为检测",
                "gmtCreate": 1559615460000,
                "gmtCreateTime": "2019-06-04 10:31:00",
                "gmtModified": 1566870648000,
                "gmtModifiedTime": "2019-08-27 09:50:48",
                "id": 3277,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_userBehaviorTrigger_v01_config",
                "namespaceId": "6b752546506d4870a883481c65b20f4e",
                "owners": "77749,146557,57724,正凡,兵长,灵觉,摩耳,鹿尤,辑熙,胖喵,衣袖,元泊,lanyin.smz",
                "type": 1,
                "valid": false
            }]
        }, {
            "app": {
                "appDetail": "天猫androidPhone",
                "appKey": "23181017",
                "appName": "天猫androidPhone-android",
                "appPackageName": "com.tmall.wireless",
                "motuAppId": "12679450@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23181017",
                "auditingFlag": "free",
                "detail": "ALINN边缘推荐",
                "gmtCreate": 1534826583000,
                "gmtCreateTime": "2018-08-21 12:43:03",
                "gmtModified": 1566870608000,
                "gmtModifiedTime": "2019-08-27 09:50:08",
                "id": 2204,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_edgeRecommend_v01_config",
                "namespaceId": "611a99d5438d4ba8b5882c2e89a4363c",
                "owners": "灵觉,摩耳,正凡,知兵,兵长,唐庚,南钰,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23181017",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1531721279000,
                "gmtCreateTime": "2018-07-16 14:07:59",
                "gmtModified": 1566870620000,
                "gmtModifiedTime": "2019-08-27 09:50:20",
                "id": 2095,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "f1e0a4ee0d094946acca1941942ec9e7",
                "owners": "正凡,万壑,灵觉,摩耳,唐庚,南钰,鹿尤,衣袖,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23181017",
                "auditingFlag": "free",
                "detail": "ALINN头发区域检测",
                "gmtCreate": 1531713260000,
                "gmtCreateTime": "2018-07-16 11:54:20",
                "gmtModified": 1566870629000,
                "gmtModifiedTime": "2019-08-27 09:50:29",
                "id": 2094,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_HairDetection_v01_config",
                "namespaceId": "8c95668728e24cdb9c91e3c0bcf77a81",
                "owners": "正凡,知兵,万壑,灵觉,摩耳,唐庚,南钰,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23181017",
                "auditingFlag": "free",
                "detail": "ALINN活体检测配置",
                "gmtCreate": 1531721316000,
                "gmtCreateTime": "2018-07-16 14:08:36",
                "gmtModified": 1566870636000,
                "gmtModifiedTime": "2019-08-27 09:50:36",
                "id": 2096,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_Liveness_v01_config",
                "namespaceId": "84342ffec1d749b7bfea77c6b70ab193",
                "owners": "正凡,万壑,灵觉,摩耳,唐庚,南钰,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "TOP淘宝卖家移动工作台",
                "appKey": "21281452",
                "appName": "TOP淘宝卖家移动工作台-ios",
                "motuAppId": "21281452@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21281452",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1541666785000,
                "gmtCreateTime": "2018-11-08 16:46:25",
                "gmtModified": 1566870658000,
                "gmtModifiedTime": "2019-08-27 09:50:58",
                "id": 2501,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "3ddd7fc522fd41eaa926657dbc6bd295",
                "owners": "正凡,兵长,灵觉,摩耳,南钰,松香,鹿尤,辑熙,唐庚,lanyin.smz",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "天猫iPhone",
                "appKey": "12615387",
                "appName": "天猫iPhone-ios",
                "motuAppId": "12615387@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "12615387",
                "auditingFlag": "free",
                "detail": "ALINN边缘推荐",
                "gmtCreate": 1534826496000,
                "gmtCreateTime": "2018-08-21 12:41:36",
                "gmtModified": 1566870667000,
                "gmtModifiedTime": "2019-08-27 09:51:07",
                "id": 2202,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_edgeRecommend_v01_config",
                "namespaceId": "a261bf41a3c34b0eb684edbf16d6c044",
                "owners": "正凡,知兵,兵长,灵觉,摩耳,唐庚,南钰,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "12615387",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1529983068000,
                "gmtCreateTime": "2018-06-26 11:17:48",
                "gmtModified": 1566870675000,
                "gmtModifiedTime": "2019-08-27 09:51:15",
                "id": 2040,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "8c90d34206b6497084bdc88b5f2dcb19",
                "owners": "兵长,灵觉,摩耳,唐庚,辑熙,衣袖,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "12615387",
                "auditingFlag": "free",
                "detail": "ALINN头发区域检测",
                "gmtCreate": 1531713183000,
                "gmtCreateTime": "2018-07-16 11:53:03",
                "gmtModified": 1566870684000,
                "gmtModifiedTime": "2019-08-27 09:51:24",
                "id": 2093,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_HairDetection_v01_config",
                "namespaceId": "d025239267ac4c1e9237171ba9186995",
                "owners": "知兵,兵长,灵觉,摩耳,唐庚,辑熙,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "12615387",
                "auditingFlag": "free",
                "detail": "ALINN活体检测配置",
                "gmtCreate": 1529983126000,
                "gmtCreateTime": "2018-06-26 11:18:46",
                "gmtModified": 1566870693000,
                "gmtModifiedTime": "2019-08-27 09:51:33",
                "id": 2041,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_Liveness_v01_config",
                "namespaceId": "adf1927fbd7443e18fad547cbee89dd1",
                "owners": "兵长,灵觉,摩耳,唐庚,辑熙,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "12615387",
                "auditingFlag": "free",
                "detail": "ALINN安全文件",
                "gmtCreate": 1531721385000,
                "gmtCreateTime": "2018-07-16 14:09:45",
                "gmtModified": 1566870700000,
                "gmtModifiedTime": "2019-08-27 09:51:40",
                "id": 2098,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_sec_config",
                "namespaceId": "c2515f50f8c2412485668e1d7faabbcb",
                "owners": "知兵,兵长,灵觉,摩耳,唐庚,辑熙,lanyin.smz",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "淘宝主客户端Android",
                "appKey": "21646297",
                "appName": "淘宝主客户端Android-android",
                "appPackageName": "com.taobao.taobao",
                "motuAppId": "12278902@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN简笔识画",
                "gmtCreate": 1547023160000,
                "gmtCreateTime": "2019-01-09 16:39:20",
                "gmtModified": 1567045427000,
                "gmtModifiedTime": "2019-08-29 10:23:47",
                "id": 2780,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_doodleRecognition_v01_config",
                "namespaceId": "73930ed74c344dc19f84abf1677f4d9c",
                "owners": "鹿尤,灵觉,摩耳,衣袖,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN边缘推荐B",
                "gmtCreate": 1538017049000,
                "gmtCreateTime": "2018-09-27 10:57:29",
                "gmtModified": 1567045465000,
                "gmtModifiedTime": "2019-08-29 10:24:25",
                "id": 2395,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_edgeRecommendB_v01_config",
                "namespaceId": "9f8b23ecff2742769ce381512acf810a",
                "owners": "正凡,知兵,兵长,灵觉,摩耳,唐庚,南钰,鹿尤,辑熙,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN边缘推荐",
                "gmtCreate": 1534826624000,
                "gmtCreateTime": "2018-08-21 12:43:44",
                "gmtModified": 1567045483000,
                "gmtModifiedTime": "2019-08-29 10:24:43",
                "id": 2205,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_edgeRecommend_v01_config",
                "namespaceId": "2b212395116c41ccbdb10dfb092f4e63",
                "owners": "正凡,知兵,兵长,灵觉,摩耳,鹿尤,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1520218994000,
                "gmtCreateTime": "2018-03-05 11:03:14",
                "gmtModified": 1567045497000,
                "gmtModifiedTime": "2019-08-29 10:24:57",
                "id": 1596,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "68a9118149364dbaa81d77bb9167e5fe",
                "owners": "正凡,万壑,灵觉,摩耳,唐庚,南钰,鹿尤,衣袖,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN头发区域检测",
                "gmtCreate": 1530668878000,
                "gmtCreateTime": "2018-07-04 09:47:58",
                "gmtModified": 1567045513000,
                "gmtModifiedTime": "2019-08-29 10:25:13",
                "id": 2062,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_HairDetection_v01_config",
                "namespaceId": "ba093a4ccc904817be3c2ec835eb592a",
                "owners": "正凡,灵觉,摩耳,lanyin.smz",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN 头发分割",
                "gmtCreate": 1567999196000,
                "gmtCreateTime": "2019-09-09 11:19:56",
                "gmtModified": 1567999196000,
                "gmtModifiedTime": "2019-09-09 11:19:56",
                "id": 3694,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_HairSegmentation_v01_config",
                "namespaceId": "c97aeabf120d4e6092286b9d22054b64",
                "owners": "85803,72463,83088,101821,83820,55556,144115,88042,185221,137507,87125,100042,160564,201681,71141,77347,40807,149905,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "手势检测",
                "gmtCreate": 1546830588000,
                "gmtCreateTime": "2019-01-07 11:09:48",
                "gmtModified": 1546830588000,
                "gmtModifiedTime": "2019-01-07 11:09:48",
                "id": 2762,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_HandGesture_v01_config",
                "namespaceId": "b393237e94324cf0ac836ee5fbb98a46",
                "owners": "鹿尤,兰茵",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN活体检测配置",
                "gmtCreate": 1520219073000,
                "gmtCreateTime": "2018-03-05 11:04:33",
                "gmtModified": 1553831143000,
                "gmtModifiedTime": "2019-03-29 11:45:43",
                "id": 1597,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_Liveness_v01_config",
                "namespaceId": "9a1b6bc20f7e4ab48f91a88bebee7f93",
                "owners": "正凡,万壑,兰茵,唐庚,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN拍立淘配置",
                "gmtCreate": 1522287873000,
                "gmtCreateTime": "2018-03-29 09:44:33",
                "gmtModified": 1553831151000,
                "gmtModifiedTime": "2019-03-29 11:45:51",
                "id": 1802,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_pltImageSearch_v01_config",
                "namespaceId": "f1097e33abf94dabb5f74678330b64e9",
                "owners": "正凡,万壑,兰茵,唐庚,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN肢体检测配置",
                "gmtCreate": 1533208821000,
                "gmtCreateTime": "2018-08-02 19:20:21",
                "gmtModified": 1553831157000,
                "gmtModifiedTime": "2019-03-29 11:45:57",
                "id": 2151,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_PoseDetection_v01_config",
                "namespaceId": "3df089fa7c1d441691c81aff0fa6bd57",
                "owners": "正凡,万壑,兰茵,唐庚,南钰,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN搜索重排",
                "gmtCreate": 1542868879000,
                "gmtCreateTime": "2018-11-22 14:41:19",
                "gmtModified": 1542868879000,
                "gmtModifiedTime": "2018-11-22 14:41:19",
                "id": 2545,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_searchRerank_v01_config",
                "namespaceId": "58eae948d37e4a8da5c2279cfc968479",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN tbLiveABR",
                "gmtCreate": 1564122155000,
                "gmtCreateTime": "2019-07-26 14:22:35",
                "gmtModified": 1564122155000,
                "gmtModifiedTime": "2019-07-26 14:22:35",
                "id": 3476,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_tbLiveABR_v01_config",
                "namespaceId": "b410ed86d38b437b83ad5b7a086664a9",
                "owners": "85803,65379,72463,83088,101821,137892,83820,110871,55556,144115,88042,68499,144470,185221,137507,87125,100042,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "ALINN 微信检测",
                "gmtCreate": 1552995219000,
                "gmtCreateTime": "2019-03-19 19:33:39",
                "gmtModified": 1552995219000,
                "gmtModifiedTime": "2019-03-19 19:33:39",
                "id": 3030,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_WeChatDetection_v01_config",
                "namespaceId": "a8d8bc69c5a4472b89f73febf6594286",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "android_windvane_config",
                "developers": "",
                "gmtCreate": 1448590144000,
                "gmtCreateTime": "2015-11-27 10:09:04",
                "gmtModified": 1546832630000,
                "gmtModifiedTime": "2019-01-07 11:43:50",
                "id": 114,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_windvane_config",
                "namespaceId": "4e95430bb2f940b589e5f72a8f1a503d",
                "owners": "正凡,益零,兰茵,鬼道,炼玉",
                "reviewers": "",
                "testers": "",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21646297",
                "auditingFlag": "free",
                "detail": "test",
                "gmtCreate": 1545304127000,
                "gmtCreateTime": "2018-12-20 19:08:47",
                "gmtModified": 1565160955000,
                "gmtModifiedTime": "2019-08-07 14:55:55",
                "id": 2686,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "orange_test_1",
                "namespaceId": "c8a67ad2b86149c38d37f3d2d6d074f7",
                "owners": "佑明,qingchong.fqc,寥望,兰茵,罗神",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "新来往rainbow",
                "appKey": "23626416",
                "appName": "rainbow-ios",
                "motuAppId": "23626416@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23626416",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1558501297000,
                "gmtCreateTime": "2019-05-22 13:01:37",
                "gmtModified": 1558501297000,
                "gmtModifiedTime": "2019-05-22 13:01:37",
                "id": 3240,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "9fe5400fcf5446aa8bf857e7e4cef6a7",
                "owners": "75869,72956,134298,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "rainbow",
                "appKey": "23625621",
                "appName": "rainbow-android",
                "appPackageName": "com.alibaba.android.luffy",
                "motuAppId": "23625621@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23625621",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1559205164000,
                "gmtCreateTime": "2019-05-30 16:32:44",
                "gmtModified": 1559205164000,
                "gmtModifiedTime": "2019-05-30 16:32:44",
                "id": 3264,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "cbe1bb9796844c509f5fc7a166a71aa5",
                "owners": "75869,132509,133336,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23625621",
                "auditingFlag": "free",
                "detail": "ALINN 活体检测",
                "gmtCreate": 1559810572000,
                "gmtCreateTime": "2019-06-06 16:42:52",
                "gmtModified": 1559810572000,
                "gmtModifiedTime": "2019-06-06 16:42:52",
                "id": 3296,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_Liveness_v01_config",
                "namespaceId": "9f8afa01e2424a8fbadb74e35742c70a",
                "owners": "75869,132509,133336,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "阿里巴巴_iPhone",
                "appKey": "21622010",
                "appName": "阿里巴巴_iPhone-ios",
                "motuAppId": "21622010@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21622010",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1566467138000,
                "gmtCreateTime": "2019-08-22 17:45:38",
                "gmtModified": 1566467138000,
                "gmtModifiedTime": "2019-08-22 17:45:38",
                "id": 3618,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "1f98430b0dc24825aae2083e50750255",
                "owners": "62309,41293,103812,109155,124453,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "淘宝跳蚤街",
                "appKey": "12431167",
                "appName": "淘宝跳蚤街-ios",
                "motuAppId": "12431167@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "12431167",
                "auditingFlag": "free",
                "detail": "ALINN DetectGraph",
                "gmtCreate": 1563950624000,
                "gmtCreateTime": "2019-07-24 14:43:44",
                "gmtModified": 1563950624000,
                "gmtModifiedTime": "2019-07-24 14:43:44",
                "id": 3458,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_DetectGraph_v01_config",
                "namespaceId": "9c3ebf20718842858f9a72d224fe15d7",
                "owners": "78396,28727,67596,50715,40039,81205,102537,65892,55601,111990,67772,64508,73777,27745,68116,40405,82757,123171,16533,112296,41622,120331,131844,133847,20202,47399,162430,180422,133380,101991,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "12431167",
                "auditingFlag": "free",
                "detail": "ALINN GenericObjectTrack",
                "gmtCreate": 1564121523000,
                "gmtCreateTime": "2019-07-26 14:12:03",
                "gmtModified": 1564121523000,
                "gmtModifiedTime": "2019-07-26 14:12:03",
                "id": 3474,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_GenericObjectTrack_v01_config",
                "namespaceId": "6577b28fdefc4f05a165b084383ef9c2",
                "owners": "78396,28727,67596,50715,40039,81205,102537,65892,55601,111990,67772,64508,73777,27745,68116,40405,82757,123171,16533,112296,41622,120331,131844,133847,20202,47399,162430,180422,133380,101991,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "零售通买家端android",
                "appKey": "23263159",
                "appName": "零售通买家端android-android",
                "appPackageName": "com.alibaba.wireless.lstretailer",
                "motuAppId": "23263159@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23263159",
                "auditingFlag": "free",
                "detail": "ALINN货架拍照引导",
                "gmtCreate": 1540544056000,
                "gmtCreateTime": "2018-10-26 16:54:16",
                "gmtModified": 1540867276000,
                "gmtModifiedTime": "2018-10-30 10:41:16",
                "id": 2471,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_lstShelf_v01_config",
                "namespaceId": "e70f2adaa593451eb3e6c8c7661cf28b",
                "owners": "正凡,知兵,兵长,兰茵,唐庚,辑熙,南钰,鹿尤,摩力",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "零售通买家端_iPhone",
                "appKey": "23262269",
                "appName": "零售通买家端_iPhone-ios",
                "motuAppId": "23262269@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23262269",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1558405974000,
                "gmtCreateTime": "2019-05-21 10:32:54",
                "gmtModified": 1558405974000,
                "gmtModifiedTime": "2019-05-21 10:32:54",
                "id": 3234,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "bc1441099cfe4e4fb17aa751abdbbb45",
                "owners": "78259,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23262269",
                "auditingFlag": "free",
                "detail": "ALINN货架拍照引导",
                "gmtCreate": 1540520823000,
                "gmtCreateTime": "2018-10-26 10:27:03",
                "gmtModified": 1540867285000,
                "gmtModifiedTime": "2018-10-30 10:41:25",
                "id": 2464,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_lstShelf_v01_config",
                "namespaceId": "9dace9e027de4c80bd1f3c53539ad15f",
                "owners": "正凡,知兵,兵长,兰茵,唐庚,辑熙,南钰,鹿尤,筱逗",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "淘宝旅行",
                "appKey": "12663307",
                "appName": "淘宝旅行-android",
                "appPackageName": "com.taobao.trip",
                "motuAppId": "12663307@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "12663307",
                "auditingFlag": "free",
                "detail": "飞猪验证码识别模型的配置",
                "gmtCreate": 1544068746000,
                "gmtCreateTime": "2018-12-06 11:59:06",
                "gmtModified": 1563272889000,
                "gmtModifiedTime": "2019-07-16 18:28:09",
                "id": 2616,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_OCRCaptcha_v01_config",
                "namespaceId": "6bdff523283c4fe3bebd572d45a4d64b",
                "owners": "政宇,秉乾,coorchice.cb,兰茵,鹿尤",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "应用已经更名为“飞猪”，提供在线旅行服务。",
                "appKey": "12381755",
                "appName": "淘宝旅行-ios",
                "motuAppId": "12381755@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "12381755",
                "auditingFlag": "free",
                "detail": "航旅版本覆盖测试",
                "gmtCreate": 1563504511000,
                "gmtCreateTime": "2019-07-19 10:48:31",
                "gmtModified": 1565577466000,
                "gmtModifiedTime": "2019-08-12 10:37:46",
                "id": 3440,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_lanyin_test",
                "namespaceId": "a99f314b601d4cd3864b9ea93127ed35",
                "owners": "兰茵,寥望,罗神",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "零售通业务员端",
                "appKey": "23257860",
                "appName": "零售通业务员端-ios",
                "motuAppId": "23257860@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23257860",
                "auditingFlag": "free",
                "detail": "ALINN 货架拍照引导",
                "gmtCreate": 1560158884000,
                "gmtCreateTime": "2019-06-10 17:28:04",
                "gmtModified": 1560158884000,
                "gmtModifiedTime": "2019-06-10 17:28:04",
                "id": 3303,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_lstShelf_v01_config",
                "namespaceId": "a54ec0a7d06a458c903f27c6d08a24ad",
                "owners": "62378,60233,45229,100883,47605,85996,60924,130620,78259,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "零售通业务员端",
                "appKey": "23257787",
                "appName": "零售通业务员端-android",
                "appPackageName": "com.alibaba.lstywy",
                "motuAppId": "23257787@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23257787",
                "auditingFlag": "free",
                "detail": "ALINN 货架拍照引导",
                "gmtCreate": 1560305379000,
                "gmtCreateTime": "2019-06-12 10:09:39",
                "gmtModified": 1560305379000,
                "gmtModifiedTime": "2019-06-12 10:09:39",
                "id": 3311,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_lstShelf_v01_config",
                "namespaceId": "a01f6852f8a44fa2953dfea4c4da51e5",
                "owners": "62378,62379,100883,101753,47605,105006,70605,122114,133388,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "安卓阿里家庭医生",
                "appKey": "23249236",
                "appName": "安卓阿里家庭医生-android",
                "appPackageName": "com.alihealth.manager",
                "motuAppId": "23249236@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23249236",
                "auditingFlag": "free",
                "detail": "ALINN语义补全",
                "gmtCreate": 1552447284000,
                "gmtCreateTime": "2019-03-13 11:21:24",
                "gmtModified": 1552447284000,
                "gmtModifiedTime": "2019-03-13 11:21:24",
                "id": 2993,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_AutoSuggestion_v01_config",
                "namespaceId": "82904dc5b0014055a85d70f3f0d30e0c",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23249236",
                "auditingFlag": "free",
                "detail": "ALINN边缘推荐",
                "gmtCreate": 1552042518000,
                "gmtCreateTime": "2019-03-08 18:55:18",
                "gmtModified": 1552042518000,
                "gmtModifiedTime": "2019-03-08 18:55:18",
                "id": 2981,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_edgeRecommend_v01_config",
                "namespaceId": "cc85a38423b94442b0385a92b5b96fc2",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "淘宝联盟android",
                "appKey": "21549244",
                "appName": "淘宝联盟android-android",
                "appPackageName": "com.alimama.moon",
                "motuAppId": "21549244@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21549244",
                "auditingFlag": "free",
                "detail": "WindVane预加载配置",
                "developers": "",
                "gmtCreate": 1487662941000,
                "gmtCreateTime": "2017-02-21 15:42:21",
                "gmtModified": 1488373772000,
                "gmtModifiedTime": "2017-03-01 21:09:32",
                "id": 789,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_windvane_config",
                "namespaceId": "1f093b254fec47e88ee933186fdc9f7d",
                "owners": "文退,兰茵,龙兮",
                "reviewers": "",
                "testers": "",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "盒马",
                "appKey": "23230111",
                "appName": "盒马-ios",
                "motuAppId": "23230111@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23230111",
                "auditingFlag": "free",
                "detail": "ALINN 边缘推荐",
                "gmtCreate": 1559615321000,
                "gmtCreateTime": "2019-06-04 10:28:41",
                "gmtModified": 1559615321000,
                "gmtModifiedTime": "2019-06-04 10:28:41",
                "id": 3276,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_edgeRecommend_v01_config",
                "namespaceId": "7eedb4fbea0b4fa1824fd9eac40266c9",
                "owners": "83706,82144,38585,75611,100610,75358,106285,80950,100883,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "手淘App_iPad端",
                "appKey": "23552352",
                "appName": "手淘App_iPad端-ios",
                "motuAppId": "23552352@ipad",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23552352",
                "auditingFlag": "free",
                "detail": "ios_windvane_config",
                "developers": "",
                "gmtCreate": 1482138923000,
                "gmtCreateTime": "2016-12-19 17:15:23",
                "gmtModified": 1488373772000,
                "gmtModifiedTime": "2017-03-01 21:09:32",
                "id": 659,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_windvane_config",
                "namespaceId": "bc42eae341754084a7ed392127c21972",
                "owners": "炼玉,鬼道,兰茵",
                "reviewers": "",
                "testers": "",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "淘宝跳蚤街",
                "appKey": "21407387",
                "appName": "淘宝跳蚤街-android",
                "appPackageName": "com.taobao.fleamarket",
                "motuAppId": "21407387@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21407387",
                "auditingFlag": "free",
                "detail": "ALINN DetectGraph",
                "gmtCreate": 1563950557000,
                "gmtCreateTime": "2019-07-24 14:42:37",
                "gmtModified": 1563950557000,
                "gmtModifiedTime": "2019-07-24 14:42:37",
                "id": 3457,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_DetectGraph_v01_config",
                "namespaceId": "31fddb5a83064ec7a1342127506db967",
                "owners": "64508,28727,78396,67596,102537,65892,50715,81205,40039,67772,55601,73777,27745,68116,123171,40405,16533,112296,41622,120331,131844,133847,47399,20202,162430,180422,133380,101991,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21407387",
                "auditingFlag": "free",
                "detail": "ALINN GenericObjectTrack",
                "gmtCreate": 1564121539000,
                "gmtCreateTime": "2019-07-26 14:12:19",
                "gmtModified": 1564121539000,
                "gmtModifiedTime": "2019-07-26 14:12:19",
                "id": 3475,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_GenericObjectTrack_v01_config",
                "namespaceId": "ee1b206438104b52baa7cffd29742a63",
                "owners": "64508,28727,78396,67596,102537,65892,50715,81205,40039,67772,55601,73777,27745,68116,123171,40405,16533,112296,41622,120331,131844,133847,47399,20202,162430,180422,133380,101991,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "淘宝iPhone客户端",
                "appKey": "21380790",
                "appName": "淘宝iPhone客户端-ios",
                "motuAppId": "12087020@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN简笔画识别",
                "gmtCreate": 1545702140000,
                "gmtCreateTime": "2018-12-25 09:42:20",
                "gmtModified": 1545702140000,
                "gmtModifiedTime": "2018-12-25 09:42:20",
                "id": 2706,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_doodleRecognition_v01_config",
                "namespaceId": "eaa1748491fc4fe9be446debe9ed63c6",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN边缘推荐",
                "gmtCreate": 1534826530000,
                "gmtCreateTime": "2018-08-21 12:42:10",
                "gmtModified": 1534826530000,
                "gmtModifiedTime": "2018-08-21 12:42:10",
                "id": 2203,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_edgeRecommend_v01_config",
                "namespaceId": "149d5f77b0d24d8a8f4f068a5f83b6c9",
                "owners": "正凡,知兵,兵长,兰茵,唐庚,南钰,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1522287959000,
                "gmtCreateTime": "2018-03-29 09:45:59",
                "gmtModified": 1567425406000,
                "gmtModifiedTime": "2019-09-02 19:56:46",
                "id": 1803,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "610bfb99b7db4361bd2208d9e3cc7d14",
                "owners": "兵长,兰茵,唐庚,南钰,辑熙,衣袖,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "alinn头发区域检测",
                "gmtCreate": 1530668734000,
                "gmtCreateTime": "2018-07-04 09:45:34",
                "gmtModified": 1546846440000,
                "gmtModifiedTime": "2019-01-07 15:34:00",
                "id": 2061,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_HairDetection_v01_config",
                "namespaceId": "50adc133249a4e6290a94c7f054dd233",
                "owners": "知兵,兵长,兰茵,唐庚,辑熙",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN 头发分割",
                "gmtCreate": 1559041280000,
                "gmtCreateTime": "2019-05-28 19:01:20",
                "gmtModified": 1559041280000,
                "gmtModifiedTime": "2019-05-28 19:01:20",
                "id": 3256,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_HairSegmentation_v01_config",
                "namespaceId": "6592f57002ff4017a25a68bb2023cef0",
                "owners": "100198,67907,65379,80945,88042,110871,73791,55556,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN列表重排",
                "gmtCreate": 1548833694000,
                "gmtCreateTime": "2019-01-30 15:34:54",
                "gmtModified": 1548833694000,
                "gmtModifiedTime": "2019-01-30 15:34:54",
                "id": 2866,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_listRerank_v01_config",
                "namespaceId": "1bbb9c05b79d4289b6e36f6fb39134a0",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN活体检测配置",
                "gmtCreate": 1522287998000,
                "gmtCreateTime": "2018-03-29 09:46:38",
                "gmtModified": 1563355572000,
                "gmtModifiedTime": "2019-07-17 17:26:12",
                "id": 1804,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_Liveness_v01_config",
                "namespaceId": "da80d28e40194946befd3795456b07d8",
                "owners": "兵长,兰茵,唐庚,南钰,辑熙,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN猫晚人脸表情",
                "gmtCreate": 1536805769000,
                "gmtCreateTime": "2018-09-13 10:29:29",
                "gmtModified": 1536805769000,
                "gmtModifiedTime": "2018-09-13 10:29:29",
                "id": 2306,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_maowanEmotion_v01_config",
                "namespaceId": "c68521ffeb8e4b00b2a76770766c7123",
                "owners": "正凡,知兵,兵长,兰茵,唐庚,辑熙,南钰,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN拍立淘配置",
                "gmtCreate": 1522288023000,
                "gmtCreateTime": "2018-03-29 09:47:03",
                "gmtModified": 1533798109000,
                "gmtModifiedTime": "2018-08-09 15:01:49",
                "id": 1805,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_pltImageSearch_v01_config",
                "namespaceId": "1e640f2c7ae04bc494a6b42deabd9ca0",
                "owners": "兵长,兰茵,唐庚,辑熙",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN肢体检测配置",
                "gmtCreate": 1533208474000,
                "gmtCreateTime": "2018-08-02 19:14:34",
                "gmtModified": 1533798113000,
                "gmtModifiedTime": "2018-08-09 15:01:53",
                "id": 2150,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_PoseDetection_v01_config",
                "namespaceId": "f9faf9baf43d474e8b694d3c1235e5c9",
                "owners": "知兵,兵长,兰茵,唐庚,南钰,辑熙",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ALINN搜索重排",
                "gmtCreate": 1542616289000,
                "gmtCreateTime": "2018-11-19 16:31:29",
                "gmtModified": 1542616289000,
                "gmtModifiedTime": "2018-11-19 16:31:29",
                "id": 2532,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_searchRerank_v01_config",
                "namespaceId": "272d2bd4034446eea40ab7907b00a038",
                "owners": "正凡,兵长,兰茵,南钰,松香,鹿尤,辑熙,唐庚",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "ios_windvane_config",
                "developers": "",
                "gmtCreate": 1448589998000,
                "gmtCreateTime": "2015-11-27 10:06:38",
                "gmtModified": 1547178618000,
                "gmtModifiedTime": "2019-01-11 11:50:18",
                "id": 113,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_windvane_config",
                "namespaceId": "eee4fd35fc854d71949a4c5783c0be13",
                "owners": "炼玉,兰茵,鬼道",
                "reviewers": "",
                "testers": "",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "灰度、ABTest测试",
                "gmtCreate": 1514463449000,
                "gmtCreateTime": "2017-12-28 20:17:29",
                "gmtModified": 1565577503000,
                "gmtModifiedTime": "2019-08-12 10:38:23",
                "id": 1366,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "OrangeABTest",
                "namespaceId": "04c89e274ab24c7f96610aa2f6bafd3d",
                "owners": "昊浪,岽篱,布可,寥望,兰茵,罗神",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "21380790",
                "auditingFlag": "free",
                "detail": "push test",
                "gmtCreate": 1506065341000,
                "gmtCreateTime": "2017-09-22 15:29:01",
                "gmtModified": 1558327739000,
                "gmtModifiedTime": "2019-05-20 12:48:59",
                "id": 1142,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "pushhhh",
                "namespaceId": "806fcce977234fc98540567e19235749",
                "owners": "岽篱,昊浪,寥望,兰茵",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "共享出行车视觉终端设备是一个基于Android开发板的盒子。该设备由我们自研，盒子的系统是Android，里面的APP是我们自主开发。\n\nAPP的主要功能从连接到盒子上的摄像头采集图片数据，在盒子内部做分析和处理，处理结果上报到我们的服务端。\n\n预计上线时间5月底。",
                "appKey": "26020968",
                "appName": "iovshare_dart-android",
                "appPackageName": "com.aliyun.iovshare.dart",
                "motuAppId": "26020968@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "26020968",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1560765195000,
                "gmtCreateTime": "2019-06-17 17:53:15",
                "gmtModified": 1560765195000,
                "gmtModifiedTime": "2019-06-17 17:53:15",
                "id": 3325,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "5d7b15640fef4da6b8dfb81643544b0c",
                "owners": "85512,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "26020968",
                "auditingFlag": "free",
                "detail": "ALINN 手势检测",
                "gmtCreate": 1562212993000,
                "gmtCreateTime": "2019-07-04 12:03:13",
                "gmtModified": 1562212993000,
                "gmtModifiedTime": "2019-07-04 12:03:13",
                "id": 3393,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_HandGesture_v01_config",
                "namespaceId": "4a62b7f584244042a12e284e5ac02e6c",
                "owners": "85512,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "26020968",
                "auditingFlag": "free",
                "detail": "ALINN IllegalDriving",
                "gmtCreate": 1560847503000,
                "gmtCreateTime": "2019-06-18 16:45:03",
                "gmtModified": 1560847503000,
                "gmtModifiedTime": "2019-06-18 16:45:03",
                "id": 3331,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_IllegalDriving_v01_config",
                "namespaceId": "d4e8257956214cda84a475d87bb7f416",
                "owners": "85512,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "26020968",
                "auditingFlag": "free",
                "detail": "ALINN SmokeDetection",
                "gmtCreate": 1563334014000,
                "gmtCreateTime": "2019-07-17 11:26:54",
                "gmtModified": 1563334014000,
                "gmtModifiedTime": "2019-07-17 11:26:54",
                "id": 3428,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_SmokeDetection_v01_config",
                "namespaceId": "37f5f1908eed444dbc3f4dd5412f6cdf",
                "owners": "85512,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "专注短视频社交的社区App",
                "appKey": "26032880",
                "appName": "开箱-android",
                "appPackageName": "com.shenma.openbox",
                "motuAppId": "26032880@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "26032880",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1565871395000,
                "gmtCreateTime": "2019-08-15 20:16:35",
                "gmtModified": 1565871395000,
                "gmtModifiedTime": "2019-08-15 20:16:35",
                "id": 3576,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "7fe5823ce06648b6a6f494e78971c48b",
                "owners": "80283,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "黑猴_iOS",
                "appKey": "25577461",
                "appName": "黑猴_iOS-ios",
                "motuAppId": "25577461@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25577461",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1557815742000,
                "gmtCreateTime": "2019-05-14 14:35:42",
                "gmtModified": 1557815742000,
                "gmtModifiedTime": "2019-05-14 14:35:42",
                "id": 3207,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "336f988939ea4326b6c4c4fb4088a3a0",
                "owners": "131452,185315,170661,77543,102913,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "黑猴_Android",
                "appKey": "25585042",
                "appName": "黑猴_Android-android",
                "appPackageName": "fm.xiami.main.a",
                "motuAppId": "25585042@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25585042",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1558078148000,
                "gmtCreateTime": "2019-05-17 15:29:08",
                "gmtModified": 1558078148000,
                "gmtModifiedTime": "2019-05-17 15:29:08",
                "id": 3222,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "c600195d96f1412fa201087601b2ac5c",
                "owners": "67345,67351,141913,136621,104480,103277,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "25585042",
                "auditingFlag": "free",
                "detail": "ALINN 体态识别",
                "gmtCreate": 1559183476000,
                "gmtCreateTime": "2019-05-30 10:31:16",
                "gmtModified": 1559183476000,
                "gmtModifiedTime": "2019-05-30 10:31:16",
                "id": 3262,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_PostureDetection_v01_config",
                "namespaceId": "547bc9bdfa564da18a0023b6191737b3",
                "owners": "67345,67351,141913,136621,104480,103277,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "Heyho",
                "appKey": "25520561",
                "appName": "Heyho-ios",
                "motuAppId": "25520561@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25520561",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1559361103000,
                "gmtCreateTime": "2019-06-01 11:51:43",
                "gmtModified": 1559361103000,
                "gmtModifiedTime": "2019-06-01 11:51:43",
                "id": 3270,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "9ad330e3c38342ae95b7521d15e20a10",
                "owners": "131452,185315,170661,77543,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "图兰朵智能设备嵌入智能监控功能，需要申请一个独立应用appkey。\n\n预计上线时间：2019年1月15日",
                "appKey": "25452022",
                "appName": "图兰朵智能看护-android",
                "appPackageName": "com.alibaba.ailabs.ar.monitor",
                "motuAppId": "25452022@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25452022",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1562640872000,
                "gmtCreateTime": "2019-07-09 10:54:32",
                "gmtModified": 1562640872000,
                "gmtModifiedTime": "2019-07-09 10:54:32",
                "id": 3402,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "09c6165262734e1ea09a02204784ec06",
                "owners": "144970,143972,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "TaobaoLive4Android",
                "appKey": "25443018",
                "appName": "TaobaoLive4Android-android",
                "appPackageName": "com.taobao.live",
                "motuAppId": "25443018@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25443018",
                "auditingFlag": "free",
                "detail": "ALINN简笔画识别",
                "gmtCreate": 1547724632000,
                "gmtCreateTime": "2019-01-17 19:30:32",
                "gmtModified": 1547724632000,
                "gmtModifiedTime": "2019-01-17 19:30:32",
                "id": 2814,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_doodleRecognition_v01_config",
                "namespaceId": "d67d721545214f3eaf69de1ab65a265a",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "用于蜂鸟感知视觉的app，用于嵌入式设备端，目前主要供内部使用，主要用于读取摄像头画面并进行目标检测，预计上线时间为12月25号",
                "appKey": "25374627",
                "appName": "蜂鸟视觉-android",
                "appPackageName": "com.taobao.hummingbirdCV4android",
                "motuAppId": "25374627@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25374627",
                "auditingFlag": "free",
                "detail": "ALINN 目标检测",
                "gmtCreate": 1544600061000,
                "gmtCreateTime": "2018-12-12 15:34:21",
                "gmtModified": 1544600061000,
                "gmtModifiedTime": "2018-12-12 15:34:21",
                "id": 2634,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_objectDetection_v01_config",
                "namespaceId": "ea46da6f3be34c3bae9b89a0a758f96d",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "上线时间：11月中旬\n业务场景：UC浏览器内的拍摄组件。服务于小视频业务，为用户提供拍摄功能。",
                "appKey": "25245941",
                "appName": "UC浏览器拍摄组件-android",
                "appPackageName": "com.uc.module.aloha",
                "motuAppId": "25245941@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25245941",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1543815503000,
                "gmtCreateTime": "2018-12-03 13:38:23",
                "gmtModified": 1543815503000,
                "gmtModifiedTime": "2018-12-03 13:38:23",
                "id": 2590,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "c371847cc4a94c5fa615cfa909d27d2b",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "alinn,dai等客户端模型评测",
                "appKey": "25142640",
                "appName": "etestlite_android-android",
                "appPackageName": "com.taobao.etestlite.android",
                "motuAppId": "25142640@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25142640",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1541657114000,
                "gmtCreateTime": "2018-11-08 14:05:14",
                "gmtModified": 1541657114000,
                "gmtModifiedTime": "2018-11-08 14:05:14",
                "id": 2500,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "83f2232ef10e4b419c72f745e1b748ed",
                "owners": "正凡,知兵,兰茵,唐庚,南钰,鹿尤,辑熙",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "HomeAIAndroid",
                "appKey": "25076610",
                "appName": "HomeAIAndroid-android",
                "appPackageName": "com.taobao.homeai",
                "motuAppId": "25076610@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "25076610",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1543390547000,
                "gmtCreateTime": "2018-11-28 15:35:47",
                "gmtModified": 1543390547000,
                "gmtModifiedTime": "2018-11-28 15:35:47",
                "id": 2573,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "887b99237a944dfc8f45306555cf864a",
                "owners": "正凡,知兵,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "goHigh",
                "appKey": "24851425",
                "appName": "goHigh-android",
                "appPackageName": "com.goHigh",
                "motuAppId": "24851425@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "24851425",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1531898124000,
                "gmtCreateTime": "2018-07-18 15:15:24",
                "gmtModified": 1531898124000,
                "gmtModifiedTime": "2018-07-18 15:15:24",
                "id": 2105,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "eca13f92a83a419b8dcb39670151a392",
                "owners": "正凡,万壑,兰茵,唐庚,南钰",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24851425",
                "auditingFlag": "free",
                "detail": "ALINN安全文件",
                "gmtCreate": 1531898062000,
                "gmtCreateTime": "2018-07-18 15:14:22",
                "gmtModified": 1531988480000,
                "gmtModifiedTime": "2018-07-19 16:21:20",
                "id": 2104,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_sec_config",
                "namespaceId": "dcd75ccd5f8749fdb3d944195fee6229",
                "owners": "正凡,知兵,万壑,兰茵,唐庚,南钰",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "goHigh",
                "appKey": "24851424",
                "appName": "goHigh-ios",
                "motuAppId": "24851424@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "24851424",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1535348378000,
                "gmtCreateTime": "2018-08-27 13:39:38",
                "gmtModified": 1535348378000,
                "gmtModifiedTime": "2018-08-27 13:39:38",
                "id": 2220,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "20448669bc324a7cb6b0adfac9f0c472",
                "owners": "正凡,知兵,兵长,兰茵,唐庚,辑熙,南钰,鹿尤",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "rainbow_appstore",
                "appKey": "24807926",
                "appName": "rainbow_appstore-ios",
                "motuAppId": "24807926@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "24807926",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1560400533000,
                "gmtCreateTime": "2019-06-13 12:35:33",
                "gmtModified": 1560400533000,
                "gmtModifiedTime": "2019-06-13 12:35:33",
                "id": 3315,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "993cc3f9acae436b8b742ed260763191",
                "owners": "72956,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "直播_主播专用版",
                "appKey": "24534735",
                "appName": "直播_主播专用版-android",
                "appPackageName": "com.taobao.live4anchor",
                "motuAppId": "24534735@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "24534735",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1565774609000,
                "gmtCreateTime": "2019-08-14 17:23:29",
                "gmtModified": 1565774609000,
                "gmtModifiedTime": "2019-08-14 17:23:29",
                "id": 3567,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "a7cfcf1cb89d4f0888ebadda37875d07",
                "owners": "77003,152625,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "lanyin s app 13579",
                "appKey": "24533017",
                "appName": "兰茵的测试应用2-android",
                "appPackageName": "com.lanyin",
                "motuAppId": "24533017@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "ALINN 人脸识别",
                "gmtCreate": 1555401596000,
                "gmtCreateTime": "2019-04-16 15:59:56",
                "gmtModified": 1555401596000,
                "gmtModifiedTime": "2019-04-16 15:59:56",
                "id": 3126,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "1161deaaa70e475099b2e8ec5fb04b21",
                "owners": "39753,52317,62084,150418,163277,57724,72396,61383,65306,55556,104705,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "ALINN GenericObjectTrack",
                "gmtCreate": 1564629355000,
                "gmtCreateTime": "2019-08-01 11:15:55",
                "gmtModified": 1564629355000,
                "gmtModifiedTime": "2019-08-01 11:15:55",
                "id": 3495,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_GenericObjectTrack_v01_config",
                "namespaceId": "fdfaf716a5d541b398b1876676acad38",
                "owners": "39753,55556,77793,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "ALINN 手势检测",
                "gmtCreate": 1556264333000,
                "gmtCreateTime": "2019-04-26 15:38:53",
                "gmtModified": 1556264333000,
                "gmtModifiedTime": "2019-04-26 15:38:53",
                "id": 3166,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_HandGesture_v01_config",
                "namespaceId": "7fc12f252c834307a827f0a66c8aeaa3",
                "owners": "39753,55556,77793,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "ALINN 手势识别",
                "gmtCreate": 1556263418000,
                "gmtCreateTime": "2019-04-26 15:23:38",
                "gmtModified": 1556263418000,
                "gmtModifiedTime": "2019-04-26 15:23:38",
                "id": 3165,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_handRecognition_v01_config",
                "namespaceId": "578dad4173134d1d9b368e87b85aa645",
                "owners": "39753,55556,77793,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "ALINN OCR",
                "gmtCreate": 1555401419000,
                "gmtCreateTime": "2019-04-16 15:56:59",
                "gmtModified": 1555401419000,
                "gmtModifiedTime": "2019-04-16 15:56:59",
                "id": 3125,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_ocr_v01_config",
                "namespaceId": "8dea526a31e7473c84e5ed73962eb861",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "chone-test",
                "gmtCreate": 1515508650000,
                "gmtCreateTime": "2018-01-09 22:37:30",
                "gmtModified": 1558327746000,
                "gmtModifiedTime": "2019-05-20 12:49:06",
                "id": 1404,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "chone-test",
                "namespaceId": "6f0e9c9c107f4314a93776ea389a9e61",
                "owners": "悟二,qingchong.fqc,hongju.wp,寥望,兰茵",
                "type": 3,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "test",
                "gmtCreate": 1522760204000,
                "gmtCreateTime": "2018-04-03 20:56:44",
                "gmtModified": 1568783315000,
                "gmtModifiedTime": "2019-09-18 13:08:35",
                "id": 1830,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test",
                "namespaceId": "bb9048ca85c7471da1e9de85b8d9669f",
                "owners": "悟二,qingchong.fqc,寥望,lanyin.smz,罗神,宁邑,若存",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "预案中心联调",
                "gmtCreate": 1568705411000,
                "gmtCreateTime": "2019-09-17 15:30:11",
                "gmtModified": 1568792100000,
                "gmtModifiedTime": "2019-09-18 15:35:00",
                "id": 3746,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_preplan",
                "namespaceId": "036d20bd563e488faa8abde183dcd82d",
                "owners": "罗神,若存,兰茵,玄乌",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "dd46b7c6-9da2-4347-bb49-819853295c31",
                "detail": "api测试",
                "gmtCreate": 1553574215000,
                "gmtCreateTime": "2019-03-26 12:23:35",
                "gmtModified": 1570609421000,
                "gmtModifiedTime": "2019-10-09 16:23:41",
                "id": 3052,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "sdk-test",
                "namespaceId": "b252ebad0e4c46969a8fafcbd437627d",
                "owners": "岽篱,hongju.wp,寥望,兰茵,若存",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "24533017",
                "auditingFlag": "free",
                "detail": "test",
                "gmtCreate": 1509097531000,
                "gmtCreateTime": "2017-10-27 17:45:31",
                "gmtModified": 1565577488000,
                "gmtModifiedTime": "2019-08-12 10:38:08",
                "id": 1217,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "test_orange",
                "namespaceId": "2aa67757524a4fc2b8988d437186ec2d",
                "owners": "岽篱,佑明,昊浪,零夕,寥望,兰茵,罗神",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "直播_主播专用版",
                "appKey": "23903947",
                "appName": "直播_主播专用版-ios",
                "motuAppId": "23903947@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23903947",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1535534762000,
                "gmtCreateTime": "2018-08-29 17:26:02",
                "gmtModified": 1535534762000,
                "gmtModifiedTime": "2018-08-29 17:26:02",
                "id": 2236,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "20ceb7cf83534f1da31b5329f7b0ddc8",
                "owners": "正凡,知兵,兵长,兰茵,唐庚,辑熙,南钰,鹿尤",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23903947",
                "auditingFlag": "free",
                "detail": "ALINN RL带宽预测",
                "gmtCreate": 1552446596000,
                "gmtCreateTime": "2019-03-13 11:09:56",
                "gmtModified": 1552446596000,
                "gmtModifiedTime": "2019-03-13 11:09:56",
                "id": 2992,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_PredictBitrateRL_v01_config",
                "namespaceId": "898b87eb09f94dc796b35175d666ab93",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "AliExpressiPhone主客户端",
                "appKey": "21371581",
                "appName": "AliExpressiPhone主客户端-ios",
                "motuAppId": "21371581@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21371581",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1545097163000,
                "gmtCreateTime": "2018-12-18 09:39:23",
                "gmtModified": 1545097163000,
                "gmtModifiedTime": "2018-12-18 09:39:23",
                "id": 2657,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "783f0d6aead844c19225ac929406123b",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "uc的ios版本",
                "appKey": "21803344",
                "appName": "uc的ios版本-ios",
                "motuAppId": "21803344@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21803344",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1545296025000,
                "gmtCreateTime": "2018-12-20 16:53:45",
                "gmtModified": 1545296025000,
                "gmtModifiedTime": "2018-12-20 16:53:45",
                "id": 2683,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "5a0d51352db542d78da86b55d3e15236",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜,098991",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "国际站卖家客户端",
                "appKey": "21596413",
                "appName": "国际站卖家客户端-ios",
                "motuAppId": "21596413@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21596413",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1542164364000,
                "gmtCreateTime": "2018-11-14 10:59:24",
                "gmtModified": 1542164364000,
                "gmtModifiedTime": "2018-11-14 10:59:24",
                "id": 2517,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "308493dec0e14220a7c55f6b10e7ea35",
                "owners": "正凡,兵长,兰茵,南钰,松香,鹿尤,辑熙,唐庚",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "国际站卖家客户端",
                "appKey": "21523971",
                "appName": "国际站卖家客户端-android",
                "appPackageName": "com.alibaba.icbu.app.seller",
                "motuAppId": "21523971@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21523971",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1542621358000,
                "gmtCreateTime": "2018-11-19 17:55:58",
                "gmtModified": 1542621358000,
                "gmtModifiedTime": "2018-11-19 17:55:58",
                "id": 2534,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "4955a88fb348422588843a1cb1f6486a",
                "owners": "正凡,知兵,兰茵,唐庚,南钰,鹿尤,辑熙",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "UC_IOS_越狱版",
                "appKey": "21820712",
                "appName": "UC_IOS_越狱版-ios",
                "motuAppId": "21820712@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21820712",
                "auditingFlag": "free",
                "detail": "ALINN人脸配置",
                "gmtCreate": 1545295898000,
                "gmtCreateTime": "2018-12-20 16:51:38",
                "gmtModified": 1545295898000,
                "gmtModifiedTime": "2018-12-20 16:51:38",
                "id": 2682,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "118eb06bcf4141d7870ccecccbae50d6",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜,098991",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "UC浏览器",
                "appKey": "21711551",
                "appName": "UC浏览器-android",
                "appPackageName": "com.UCMobile",
                "motuAppId": "21711551@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "21711551",
                "auditingFlag": "free",
                "detail": "ALINN 识别猫",
                "gmtCreate": 1569414242000,
                "gmtCreateTime": "2019-09-25 20:24:02",
                "gmtModified": 1569414242000,
                "gmtModifiedTime": "2019-09-25 20:24:02",
                "id": 3794,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_recognizecat_v01_config",
                "namespaceId": "dc08c92b212a4ad1a10b68f82295e2d0",
                "owners": "35446,104490,102693,89578,95944,89375,101343,89888,120476,99366,正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "智分宝安卓版-线上",
                "appKey": "23367098",
                "appName": "智配宝安卓版-android",
                "appPackageName": "com.cainiao.android.zpb",
                "motuAppId": "23367098@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23367098",
                "auditingFlag": "free",
                "detail": "ALINN条形码检测",
                "gmtCreate": 1547631138000,
                "gmtCreateTime": "2019-01-16 17:32:18",
                "gmtModified": 1547631702000,
                "gmtModifiedTime": "2019-01-16 17:41:42",
                "id": 2806,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_barcodeDetection_v01_config",
                "namespaceId": "6da7caa1f5544d78a3cf6e062c8a3ae3",
                "owners": "正凡,兵长,兰茵,鹿尤,辑熙,胖喵,衣袖,雁夜,重娄",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "不在电脑旁（出差/会议/旅行/长假等），依然可接单 \n买家咨询，手机快捷短语快速响应\n更能支持语音转文字输入，减少打字烦恼\n\<EMAIL>",
                "appKey": "23355917",
                "appName": "千牛Android版-android",
                "appPackageName": "com.icbu.qianniu",
                "motuAppId": "23355917@android",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23355917",
                "auditingFlag": "free",
                "detail": "ALINN人脸检测配置",
                "gmtCreate": 1531898225000,
                "gmtCreateTime": "2018-07-18 15:17:05",
                "gmtModified": 1531898225000,
                "gmtModifiedTime": "2018-07-18 15:17:05",
                "id": 2107,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "android_alinn_FaceDetection_v01_config",
                "namespaceId": "4d8b6253f9894ca5ac8aca839998afd8",
                "owners": "正凡,万壑,兰茵,唐庚,南钰",
                "type": 1,
                "valid": true
            }, {
                "appKeyOrGroup": "23355917",
                "auditingFlag": "free",
                "detail": "ALINN安全文件",
                "gmtCreate": 1531898174000,
                "gmtCreateTime": "2018-07-18 15:16:14",
                "gmtModified": 1531898174000,
                "gmtModifiedTime": "2018-07-18 15:16:14",
                "id": 2106,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "android_alinn_sec_config",
                "namespaceId": "c56c2b4170dc4df2a7955a455d45a386",
                "owners": "正凡,知兵,万壑,兰茵,唐庚,南钰",
                "type": 1,
                "valid": true
            }]
        }, {
            "app": {
                "appDetail": "优酷 iPhone",
                "appKey": "23569910",
                "appName": "优酷 iPhone-ios",
                "motuAppId": "23569910@iphoneos",
                "valid": false
            },
            "namespaceList": [{
                "appKeyOrGroup": "23569910",
                "auditingFlag": "free",
                "detail": "人脸检测模型下发",
                "gmtCreate": 1530688037000,
                "gmtCreateTime": "2018-07-04 15:07:17",
                "gmtModified": 1537282640000,
                "gmtModifiedTime": "2018-09-18 22:57:20",
                "id": 2065,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "ios_alinn_FaceDetection_v01_config",
                "namespaceId": "fd7a8972437948ba8bde36b5863f9714",
                "owners": "知兵,兵长,兰茵,唐庚,辑熙",
                "type": 1,
                "valid": true
            }]
        }],
        "success": true
    };
}

function getQueryKnockoutForOfflineNamespace() {
    return {
        "errorCode": "",
        "errorMsg": "",
        "model": [{
            "appKey": "691439",
            "appVersion": "*",
            "creator": "system",
            "gmtCreate": 1529893223000,
            "gmtCreateTime": "2018-06-25 10:20:23",
            "gmtModified": 1529893223000,
            "gmtModifiedTime": "2018-06-25 10:20:23",
            "gmtPublish": 1529893223000,
            "gmtPublishTime": "2018-06-25 10:20:23",
            "id": 88243,
            "isAvailable": "y",
            "isEmergent": "n",
            "loadLevel": 10,
            "md5": "f6d53dbcbe61340083685d5d6476118b",
            "name": "android_windvane_config",
            "namespaceId": "3ab80fa6602a4314a6f1083961b7d3b2",
            "previousResourceId": "nsadef89c117134cf3b1b3f3aa366641b5.json",
            "resourceId": "ns7fa508f624a344cf9798bddff5b2abb2.json",
            "reviewer": "system",
            "source": 2,
            "sourceData": "",
            "status": 200,
            "type": 1,
            "valid": true,
            "version": "2120180625102023445"
        }],
        "success": true
    };
}


function getNamespace() {
    //bpms
    return {
        "errorCode": "", "errorMsg": "", "model": {
            "appBO": {
                "appDetail": "手机淘宝",
                "appId": 2017032401905,
                "appKey": "531772",
                "appName": "手机淘宝-ios",
                "gmtCreate": 1490343869000,
                "isAvailable": "y",
                "motuAppId": "531772@iphoneos",
                "osType": 1,
                "valid": true
            },
            "auditState": {
                "bpmUrl": "http://bpms-test.alibaba-inc.com/workdesk/instDetail?procInsId=",
                "isAuditing": true
            },
            "namespaceBO": {
                "appKeyOrGroup": "531772",
                "auditingFlag": "9bd511c0-efa2-47e6-8a0d-10dc3802d374",
                "creator": "230608",
                "detail": "1123www",
                "gmtCreate": 1573546790000,
                "gmtCreateTime": "2019-11-12 16:19:50",
                "gmtModified": 1649926019000,
                "gmtModifiedTime": "2022-04-14 16:46:59",
                "id": 1977,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_ruocun_test",
                "namespaceId": "2d2918a4e60343c69434561ca14c2a9b",
                "offlineOperator": "orange-console",
                "onlineOperator": "230608",
                "owners": "39753,230608,177422",
                "modules": "389,415",
                "subType": 99,
                "testers": "100149",
                "type": 1,
                "valid": true
            },
            "moduleList": [{
                "activeLevel": "LEVEL_10",
                "admins": [
                    "73699",
                    "76777",
                    "79527"
                ],
                "bundleName": "com.taobao.nearby",
                "codeLibraryAddress": "**************************:taobao-android/nearby.git",
                "depKey": "com.taobao.android.tbnearby_android.version",
                "description": "nearby",
                "developers": [
                    "43675",
                    "12065",
                    "29911"
                ],
                "hasDynamic": false,
                "hasIntegrateToTB": false,
                "hasLatestUtRecordPass": false,
                "hasStandard": false,
                "hasUtRecord": false,
                "identifier": "nearby",
                "moduleId": 389,
                "moduleType": "BUNDLE",
                "mtlAddress": "https://mtl4.alibaba-inc.com/#/app/389/detail/setting",
                "name": "nearby",
                "platformType": "ANDROID"
            },
            {
                "activeLevel": "LEVEL_30",
                "admins": [
                    "272265"
                ],
                "bundleName": "com.alibaba.motu.crashreporter",
                "codeLibraryAddress": "**************************:wireless/TBCrashReporter4Android.git",
                "depKey": "com.taobao.android.crashreporter.version",
                "description": "crashReporter4Android的sdk",
                "developers": [
                    "397227",
                    "247077",
                    "123035",
                    "248457",
                    "321453"
                ],
                "ds": "20240711",
                "hasDynamic": false,
                "hasIntegrateToTB": true,
                "hasLatestUtRecordPass": false,
                "hasStandard": false,
                "hasUtRecord": false,
                "identifier": "TBCrashReporter4Android",
                "latestVersion": "*********-1688-rc5",
                "latestVersionCommitNumber": "3f6b1eda17f5099b543fdfa02f97152ffe7bd0ee",
                "moduleId": 415,
                "moduleType": "BUNDLE",
                "mtlAddress": "https://mtl4.alibaba-inc.com/#/app/415/detail/setting",
                "name": "TBCrashReporter4Android",
                "owner": "55843",
                "platformType": "ANDROID",
                "tags": "unmaintained_module"
            }],
            "ownerList": [{
                "displayName": "兰茵",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "lanyin.smz",
                "empId": "39753",
                "hrStatus": "A",
                "id": 65153,
                "name": "邵明芝",
                "nickNameCn": "兰茵",
                "supervisorEmpId": "024523"
            }, {
                "displayName": "若存",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "daikui.dk",
                "empId": "230608",
                "hrStatus": "A",
                "id": 1550049,
                "name": "戴奎",
                "nickNameCn": "若存",
                "supervisorEmpId": "024523"
            }, {
                "displayName": "玄苏",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "dungang.ydg",
                "empId": "177422",
                "hrStatus": "A",
                "id": 1193794,
                "name": "余敦刚",
                "nickNameCn": "玄苏",
                "supervisorEmpId": "024523"
            }],
            "testerList": [{
                "displayName": "骊驹",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "zhendan.czd",
                "empId": "100149",
                "hrStatus": "A",
                "id": 654447,
                "name": "陈震旦",
                "nickNameCn": "骊驹",
                "supervisorEmpId": "105912"
            }],
            "versionState": {"hasVersion": true, "resourceSize": 290}
        }, "success": true
    }
    //CUS
    return {
        "errorCode": "", "errorMsg": "", "model": {
            "appBO": {
                "appDetail": "手机淘宝",
                "appId": 2017032401903,
                "appKey": "4272",
                "appName": "手机淘宝-android",
                "appPackageName": "com.taobao.taobao",
                "gmtCreate": 1490343869000,
                "isAvailable": "y",
                "motuAppId": "4272@android",
                "osType": 2,
                "valid": true
            },
            "auditState": {
                "bpmUrl": "http://bpms-test.alibaba-inc.com/workdesk/instDetail?procInsId=",
                "isAuditing": false
            },
            "namespaceBO": {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "creator": "136238",
                "detail": "tst",
                "gmtCreate": 1516182761000,
                "gmtCreateTime": "2018-01-17 17:52:41",
                "gmtModified": 1649226920000,
                "gmtModifiedTime": "2022-04-06 14:35:20",
                "id": 1175,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "test-custom",
                "namespaceId": "1482b7ce79124abda75279b62fe2d0c1",
                "owners": "136238,77592,140817,64623,39753",
                "testers": "39753,177422,100149",
                "type": 3,
                "valid": true
            },
            "ownerList": [{
                "displayName": "岽篱",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "qingchong.fqc",
                "empId": "136238",
                "hrStatus": "A",
                "id": 916261,
                "name": "樊庆冲",
                "nickNameCn": "岽篱",
                "supervisorEmpId": "27837"
            }, {
                "displayName": "笛墨",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "jielong.hjl",
                "empId": "77592",
                "hrStatus": "A",
                "id": 297734,
                "name": "黄杰龙",
                "nickNameCn": "笛墨",
                "supervisorEmpId": "38940"
            }, {
                "displayName": "鸿举",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "hongju.wp",
                "empId": "140817",
                "hrStatus": "A",
                "id": 942412,
                "name": "王沛",
                "nickNameCn": "鸿举",
                "supervisorEmpId": "53352"
            }, {
                "displayName": "寥望",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "huangchao.hc",
                "empId": "64623",
                "hrStatus": "A",
                "id": 156537,
                "name": "黄超",
                "nickNameCn": "寥望",
                "supervisorEmpId": "180516"
            }, {
                "displayName": "兰茵",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "lanyin.smz",
                "empId": "39753",
                "hrStatus": "A",
                "id": 65153,
                "name": "邵明芝",
                "nickNameCn": "兰茵",
                "supervisorEmpId": "24523"
            }],
            "testerList": [{
                "displayName": "兰茵",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "lanyin.smz",
                "empId": "39753",
                "hrStatus": "A",
                "id": 65153,
                "name": "邵明芝",
                "nickNameCn": "兰茵",
                "supervisorEmpId": "24523"
            }, {
                "displayName": "玄苏",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "dungang.ydg",
                "empId": "177422",
                "hrStatus": "A",
                "id": 1193794,
                "name": "余敦刚",
                "nickNameCn": "玄苏",
                "supervisorEmpId": "24523"
            }, {
                "displayName": "骊驹",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "zhendan.czd",
                "empId": "100149",
                "hrStatus": "A",
                "id": 654447,
                "name": "陈震旦",
                "nickNameCn": "骊驹",
                "supervisorEmpId": "105912"
            }],
            "versionState": {"hasVersion": true, "resourceSize": 271}
        }, "success": true
    }
    //有测试
    //return {"errorCode":"","errorMsg":"","model":{"appBO":{"appDetail":"手机淘宝","appId":2017032401905,"appKey":"531772","appName":"手机淘宝-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"531772@iphoneos","osType":1,"valid":true},"auditState":{"bpmUrl":"http://bpms-test.alibaba-inc.com/workdesk/instDetail?procInsId=","isAuditing":false},"namespaceBO":{"appKeyOrGroup":"531772","auditingFlag":"free","detail":"sss","gmtCreate":1588847299000,"gmtCreateTime":"2020-05-07 18:28:19","gmtModified":1648115041000,"gmtModifiedTime":"2022-03-24 17:44:01","id":2103,"isAvailable":"y","loadLevel":0,"name":"orange_test_lanyin","namespaceId":"1fca1ef2da0b47d292571bcf2349508f","owners":"39753,230608,180856","testers":"100149","type":1,"valid":true},"ownerList":[{"displayName":"兰茵","emailAddr":"<EMAIL>","emailPrefix":"lanyin.smz","empId":"39753","hrStatus":"A","id":65153,"name":"邵明芝","nickNameCn":"兰茵","supervisorEmpId":"24523"},{"displayName":"若存","emailAddr":"<EMAIL>","emailPrefix":"daikui.dk","empId":"230608","hrStatus":"A","id":1550049,"name":"戴奎","nickNameCn":"若存","supervisorEmpId":"24523"},{"displayName":"家愿","emailAddr":"<EMAIL>","emailPrefix":"jialei.zjl","empId":"180856","hrStatus":"A","id":1210876,"name":"祝佳磊","nickNameCn":"家愿","supervisorEmpId":"24523"}],"testerList":[{"displayName":"骊驹","emailAddr":"<EMAIL>","emailPrefix":"zhendan.czd","empId":"100149","hrStatus":"A","id":654447,"name":"陈震旦","nickNameCn":"骊驹","supervisorEmpId":"105912"}],"versionState":{"hasVersion":true,"resourceSize":289}},"success":true}
//无测试
    return {
        "errorCode": "", "errorMsg": "", "model": {
            "appBO": {
                "appDetail": "手机淘宝",
                "appId": 2017032401903,
                "appKey": "4272",
                "appName": "手机淘宝-android",
                "appPackageName": "com.taobao.taobao",
                "gmtCreate": 1490343869000,
                "isAvailable": "y",
                "motuAppId": "4272@android",
                "osType": 2,
                "valid": true
            },
            "auditState": {
                "bpmUrl": "http://bpms-test.alibaba-inc.com/workdesk/instDetail?procInsId=",
                "isAuditing": false
            },
            "namespaceBO": {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "detail": "动态配置，能够远程控制整个域名白名单",
                "developers": "",
                "gmtCreate": 1483951414000,
                "gmtCreateTime": "2017-01-09 16:43:34",
                "gmtModified": 1558331298000,
                "gmtModifiedTime": "2019-05-20 13:48:18",
                "id": 835,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "orange",
                "namespaceId": "d65c3533620a4bd2ae3a1fe5cf8b1b03",
                "owners": "悟二,游僧,岽篱,宸銮,寥望,兰茵",
                "reviewers": "",
                "testers": "",
                "type": 1,
                "valid": true
            },
            "ownerList": [{
                "displayName": "游僧",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "lijian.slj",
                "empId": "74166",
                "hrStatus": "A",
                "id": 270924,
                "name": "隋利剑",
                "nickNameCn": "游僧",
                "supervisorEmpId": "80729"
            }, {
                "displayName": "岽篱",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "qingchong.fqc",
                "empId": "136238",
                "hrStatus": "A",
                "id": 916261,
                "name": "樊庆冲",
                "nickNameCn": "岽篱",
                "supervisorEmpId": "27837"
            }, {
                "displayName": "寥望",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "huangchao.hc",
                "empId": "64623",
                "hrStatus": "A",
                "id": 156537,
                "name": "黄超",
                "nickNameCn": "寥望",
                "supervisorEmpId": "180516"
            }, {
                "displayName": "兰茵",
                "emailAddr": "<EMAIL>",
                "emailPrefix": "lanyin.smz",
                "empId": "39753",
                "hrStatus": "A",
                "id": 65153,
                "name": "邵明芝",
                "nickNameCn": "兰茵",
                "supervisorEmpId": "24523"
            }],
            "versionState": {"hasVersion": true, "resourceSize": 417}
        }, "success": true
    }
}

function getLoadLevelAuditingResult() {
    return {"errorCode": "", "errorMsg": "", "model": {"needAuditing": true}, "success": true};
}

function getQueryUnAvailableNamespace() {
    //无同名
    return {"errorCode": "", "errorMsg": "", "success": true};
    //有同名
    return {
        "errorCode": "",
        "errorMsg": "",
        "model": {
            "appKeyOrGroup": "60034114",
            "auditingFlag": "free",
            "detail": "1123",
            "gmtCreate": 1578452027000,
            "gmtCreateTime": "2020-01-08 10:53:47",
            "gmtModified": 1578476306000,
            "gmtModifiedTime": "2020-01-08 17:38:26",
            "id": 657,
            "isAvailable": "n",
            "loadLevel": 0,
            "name": "online_test",
            "namespaceId": "57ebf91bb3d142baa08c9025f9ace2aa",
            "owners": "若存,兰茵",
            "type": 1,
            "valid": false
        },
        "success": true
    };
}


function getNamespaceDetail() {
    //CUS
    return {
        "errorCode": "", "errorMsg": "", "model": {
            "appBO": {
                "appDetail": "手机淘宝",
                "appId": 2017032401903,
                "appKey": "4272",
                "appName": "手机淘宝-android",
                "appPackageName": "com.taobao.taobao",
                "gmtCreate": 1490343869000,
                "isAvailable": "y",
                "motuAppId": "4272@android",
                "osType": 2,
                "valid": true
            },
            "changeBO": {
                "appKey": "4272",
                "changeVersion": "3120220406144740493",
                "gmtCreate": 1649226801000,
                "gmtCreateTime": "2022-04-06 14:33:21",
                "gmtModified": 1649227660000,
                "gmtModifiedTime": "2022-04-06 14:47:40",
                "id": 24621,
                "isAvailable": "y",
                "loadLevel": 10,
                "metas": "{\"appVersion\":\"*\",\"highLazy\":0,\"loadLevel\":\"HIGH\",\"md5\":\"f37e506d613ad372e867c483dabf30d0\",\"name\":\"test-custom\",\"resourceId\":\"nc9609b72b75714b14884d90541d093f05.json\",\"type\":\"CUSTOM\",\"version\":\"2220220406143321461\"}",
                "name": "test-custom",
                "namespaceId": "1482b7ce79124abda75279b62fe2d0c1",
                "operator": "39753",
                "status": 200,
                "type": 1,
                "valid": true,
                "versionVersion": "2220220406143321461",
                "versions": "2220220406143321461"
            },
            "list": [{
                "appKey": "4272",
                "appVersion": "*",
                "creator": "39753",
                "gmtCreate": 1649226801000,
                "gmtCreateTime": "2022-04-06 14:33:21",
                "gmtModified": 1649227660000,
                "gmtModifiedTime": "2022-04-06 14:47:40",
                "gmtPublish": 1649227660000,
                "gmtPublishTime": "2022-04-06 14:47:40",
                "id": 128982,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 10,
                "md5": "f37e506d613ad372e867c483dabf30d0",
                "name": "test-custom",
                "namespaceId": "1482b7ce79124abda75279b62fe2d0c1",
                "offlines": "{\"2120190409233014722\":1}",
                "previousResourceId": "nc623a1fddfc3946e2b0e97a8439414b7f.json",
                "resourceId": "nc9609b72b75714b14884d90541d093f05.json",
                "reviewer": "39753",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "type": 3,
                "valid": true,
                "version": "2220220406143321461",
                "versions": "-"
            }],
            "namespaceBO": {
                "appKeyOrGroup": "4272",
                "auditingFlag": "free",
                "creator": "136238",
                "detail": "tst",
                "gmtCreate": 1516182761000,
                "gmtCreateTime": "2018-01-17 17:52:41",
                "gmtModified": 1649226920000,
                "gmtModifiedTime": "2022-04-06 14:35:20",
                "id": 1175,
                "isAvailable": "y",
                "loadLevel": 10,
                "name": "test-custom",
                "namespaceId": "1482b7ce79124abda75279b62fe2d0c1",
                "owners": "136238,77592,140817,64623,39753",
                "subType": 1,
                "testers": "39753,177422,100149",
                "type": 3,
                "valid": true
            },
            "noStrategyList": [{
                "appKey": "4272",
                "appVersion": "*",
                "creator": "39753",
                "gmtCreate": 1649226801000,
                "gmtCreateTime": "2022-04-06 14:33:21",
                "gmtModified": 1649227660000,
                "gmtModifiedTime": "2022-04-06 14:47:40",
                "gmtPublish": 1649227660000,
                "gmtPublishTime": "2022-04-06 14:47:40",
                "id": 128982,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 10,
                "md5": "f37e506d613ad372e867c483dabf30d0",
                "name": "test-custom",
                "namespaceId": "1482b7ce79124abda75279b62fe2d0c1",
                "offlines": "{\"2120190409233014722\":1}",
                "previousResourceId": "nc623a1fddfc3946e2b0e97a8439414b7f.json",
                "resourceId": "nc9609b72b75714b14884d90541d093f05.json",
                "reviewer": "39753",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "type": 3,
                "valid": true,
                "version": "2220220406143321461",
                "versions": "-"
            }, {
                "appKey": "4272",
                "appVersion": "1.0.0",
                "creator": "鸿举",
                "gmtCreate": 1551189145000,
                "gmtCreateTime": "2019-02-26 21:52:25",
                "gmtModified": 1551189146000,
                "gmtModifiedTime": "2019-02-26 21:52:26",
                "gmtPublish": 1551189146000,
                "gmtPublishTime": "2019-02-26 21:52:26",
                "id": 96635,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 10,
                "md5": "6a18a57dc1f500e66f9ac16ee4f0511b",
                "name": "test-custom",
                "namespaceId": "1482b7ce79124abda75279b62fe2d0c1",
                "previousResourceId": "nc561ff5e4061647388727261053c371d0.json",
                "resourceId": "nc21fcadf0861847c391dff05ce202e5de.json",
                "reviewer": "system",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "type": 3,
                "valid": true,
                "version": "2120190226215225860"
            }],
            "userMap": {
                "140817": {
                    "displayName": "鸿举",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "hongju.wp",
                    "empId": "140817",
                    "hrStatus": "A",
                    "id": 942412,
                    "name": "王沛",
                    "nickNameCn": "鸿举",
                    "supervisorEmpId": "53352"
                },
                "39753": {
                    "displayName": "兰茵",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "lanyin.smz",
                    "empId": "39753",
                    "hrStatus": "A",
                    "id": 65153,
                    "name": "邵明芝",
                    "nickNameCn": "兰茵",
                    "supervisorEmpId": "24523"
                },
                "136238": {
                    "displayName": "岽篱",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "qingchong.fqc",
                    "empId": "136238",
                    "hrStatus": "A",
                    "id": 916261,
                    "name": "樊庆冲",
                    "nickNameCn": "岽篱",
                    "supervisorEmpId": "27837"
                },
                "64623": {
                    "displayName": "寥望",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "huangchao.hc",
                    "empId": "64623",
                    "hrStatus": "A",
                    "id": 156537,
                    "name": "黄超",
                    "nickNameCn": "寥望",
                    "supervisorEmpId": "180516"
                },
                "100149": {
                    "displayName": "骊驹",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "zhendan.czd",
                    "empId": "100149",
                    "hrStatus": "A",
                    "id": 654447,
                    "name": "陈震旦",
                    "nickNameCn": "骊驹",
                    "supervisorEmpId": "105912"
                },
                "177422": {
                    "displayName": "玄苏",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "dungang.ydg",
                    "empId": "177422",
                    "hrStatus": "A",
                    "id": 1193794,
                    "name": "余敦刚",
                    "nickNameCn": "玄苏",
                    "supervisorEmpId": "24523"
                },
                "77592": {
                    "displayName": "笛墨",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "jielong.hjl",
                    "empId": "77592",
                    "hrStatus": "A",
                    "id": 297734,
                    "name": "黄杰龙",
                    "nickNameCn": "笛墨",
                    "supervisorEmpId": "38940"
                }
            },
            "viewConfig": {
                "maxGrayCntArray": [10, 10, 10],
                "oneStepSkip": false,
                "rollback": false,
                "rollbackAny": false,
                "showReport": false,
                "useGrayLink": "V1",
                "wholeProcess": true
            },
            "waitList": []
        }, "success": true
    }
    return {
        "errorCode": "", "errorMsg": "", "model": {
            "appBO": {
                "appDetail": "淘宝旅行",
                "appId": 2017031500867,
                "appKey": "12663307",
                "appMark": "alitrip_android",
                "appName": "淘宝旅行-android",
                "appPackageName": "com.taobao.trip",
                "basicAppKey": "12663307",
                "gmtCreate": 1355305478000,
                "isAvailable": "y",
                "motuAppId": "12663307@android",
                "mtlId": "5637",
                "osType": 2,
                "valid": true
            },
            "namespaceBO": {
                "appKeyOrGroup": "12663307",
                "auditingFlag": "free",
                "detail": "walle配置",
                "gmtCreate": 1648521724000,
                "gmtCreateTime": "2022-03-29 10:42:04",
                "gmtModified": 1648543466000,
                "gmtModifiedTime": "2022-03-29 16:44:26",
                "id": 8956,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "ODCP_walle_config",
                "namespaceId": "476ac4525fcc4c8baca0ad243b184338",
                "owners": "359824,139852",
                "subType": 1,
                "testers": "359824",
                "type": 1,
                "valid": true
            },
            "userMap": {
                "139852": {
                    "displayName": "故昀",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "guyun.wcx",
                    "empId": "139852",
                    "id": 938042,
                    "name": "王长旭",
                    "nickNameCn": "故昀",
                    "supervisorEmpId": "144121"
                },
                "359824": {
                    "displayName": "子驱",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "jinkadi.jkd",
                    "empId": "359824",
                    "id": 2521853,
                    "name": "金卡迪",
                    "nickNameCn": "子驱",
                    "supervisorEmpId": "144121"
                }
            },
            "viewConfig": {
                "maxGrayCntArray": [10, 10, 10],
                "oneStepSkip": false,
                "rollback": false,
                "rollbackAny": false,
                "showReport": true,
                "useGrayLink": "V1",
                "wholeProcess": true
            },
            "waitList": []
        }, "success": true
    }
    //无发布单，普通
    return {
        "errorCode": "", "errorMsg": "", "model": {
            "appBO": {
                "appDetail": "手机淘宝",
                "appId": 2017032401905,
                "appKey": "531772",
                "appName": "手机淘宝-ios",
                "gmtCreate": 1490343869000,
                "isAvailable": "y",
                "motuAppId": "531772@iphoneos",
                "osType": 1,
                "valid": true
            },
            "changeBO": {
                "appKey": "531772",
                "changeVersion": "3120220324174353557",
                "gmtCreate": 1647418815000,
                "gmtCreateTime": "2022-03-16 16:20:15",
                "gmtModified": 1648115033000,
                "gmtModifiedTime": "2022-03-24 17:43:53",
                "id": 24191,
                "isAvailable": "y",
                "loadLevel": 0,
                "metas": "{\"appVersion\":\"*\",\"candidates\":[{\"match\":\"app_ver>=5.0\",\"md5\":\"a5b0a8f37163f306f236fcfc74ae46b5\",\"resourceId\":\"nsb7a26f32584d454fad715bfb86f20c74.json\",\"version\":\"2120220120094810650\"},{\"match\":\"app_ver=4.0\",\"md5\":\"6e0a1c4d49083ca4d04fd6da2b2aaaf8\",\"resourceId\":\"nsa31cb9501a9b46749962a566bf4b58b4.json\",\"version\":\"2120220119165441926\"},{\"match\":\"app_ver=3.0\",\"md5\":\"65bf7ccdacd9b8ce79d61081684a975b\",\"resourceId\":\"ns656baef49cf4466289b71ef0cd6a45d7.json\",\"version\":\"2120220119163210420\"},{\"match\":\"app_ver=2.0\",\"md5\":\"27423d9e05a0962e4249547d608d5741\",\"resourceId\":\"ns5d88f1b5033049e5befd8b373a0b7ea0.json\",\"version\":\"2120220119151323301\"},{\"match\":\"app_ver=1.0\",\"md5\":\"629984da2c1469e5705c0d4493f877c5\",\"resourceId\":\"nseea55792ad374f85b9005dc49f213227.json\",\"version\":\"2120220119151305936\"}],\"changeVersion\":\"3120220324174353557\",\"highLazy\":1,\"loadLevel\":\"DEFAULT\",\"md5\":\"602c1f31c731b8e389530eecc020d510\",\"name\":\"orange_test_lanyin\",\"resourceId\":\"nsdb801b7b6fec4d6aa4f59a2b474e49a1.json\",\"type\":\"STANDARD\",\"version\":\"2120220316162015310\"}",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "operator": "39753",
                "status": 200,
                "type": 1,
                "valid": true,
                "versionVersion": "2120220316162015310",
                "versions": "2120220120094810650,2120220119165441926,2120220119163210420,2120220119151323301,2120220119151305936,2120220316162015310"
            },
            "list": [{
                "appKey": "531772",
                "appVersion": "*",
                "creator": "兰茵",
                "gmtCreate": 1642643290000,
                "gmtCreateTime": "2022-01-20 09:48:10",
                "gmtModified": 1644569827000,
                "gmtModifiedTime": "2022-02-11 16:57:07",
                "gmtPublish": 1644569827000,
                "gmtPublishTime": "2022-02-11 16:57:07",
                "id": 113209,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 0,
                "md5": "a5b0a8f37163f306f236fcfc74ae46b5",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "offlines": "{}",
                "previousResourceId": "nsa31cb9501a9b46749962a566bf4b58b4.json",
                "resourceId": "nsb7a26f32584d454fad715bfb86f20c74.json",
                "reviewer": "兰茵",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "strategy": "app_ver>=5.0",
                "type": 1,
                "valid": true,
                "version": "2120220120094810650",
                "versions": "-,2120220119165441926,2120220119163210420,2120220119151323301,2120220119151305936,2120220119151241725"
            }, {
                "appKey": "531772",
                "appVersion": "*",
                "creator": "兰茵",
                "gmtCreate": 1642582482000,
                "gmtCreateTime": "2022-01-19 16:54:42",
                "gmtModified": 1642643261000,
                "gmtModifiedTime": "2022-01-20 09:47:41",
                "gmtPublish": 1642643261000,
                "gmtPublishTime": "2022-01-20 09:47:41",
                "id": 112552,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 0,
                "md5": "6e0a1c4d49083ca4d04fd6da2b2aaaf8",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "offlines": "{}",
                "previousResourceId": "ns656baef49cf4466289b71ef0cd6a45d7.json",
                "resourceId": "nsa31cb9501a9b46749962a566bf4b58b4.json",
                "reviewer": "兰茵",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "strategy": "app_ver=4.0",
                "type": 1,
                "valid": true,
                "version": "2120220119165441926",
                "versions": "-,2120220119163210420,2120220119151323301,2120220119151305936,2120220119151241725"
            }, {
                "appKey": "531772",
                "appVersion": "*",
                "creator": "兰茵",
                "gmtCreate": 1642581130000,
                "gmtCreateTime": "2022-01-19 16:32:10",
                "gmtModified": 1642582444000,
                "gmtModifiedTime": "2022-01-19 16:54:04",
                "gmtPublish": 1642582444000,
                "gmtPublishTime": "2022-01-19 16:54:04",
                "id": 112513,
                "isAvailable": "y",
                "isEmergent": "y",
                "loadLevel": 0,
                "md5": "65bf7ccdacd9b8ce79d61081684a975b",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "offlines": "{\"2120220119151339537\":6,\"2120220119163111868\":6,\"2120220119163012744\":6}",
                "overwriteStrategyVersions": "2120220119151339537,2120220119163111868,2120220119163012744",
                "previousResourceId": "nsb340e065d1dd4c3c92547c2ec96d65f8.json",
                "resourceId": "ns656baef49cf4466289b71ef0cd6a45d7.json",
                "reviewer": "兰茵",
                "source": 0,
                "sourceData": "{\"businessName\":\"orange-console\",\"fromVersion\":\"2120220119163111868\",\"lossy\":false,\"rollback\":true,\"toVersion\":\"2120220119151339537\"}",
                "sourceDataMeta": {
                    "businessName": "orange-console",
                    "fromVersion": "2120220119163111868",
                    "lossy": false,
                    "rollback": true,
                    "toVersion": "2120220119151339537"
                },
                "status": 200,
                "strategy": "app_ver=3.0",
                "type": 1,
                "valid": true,
                "version": "2120220119163210420",
                "versions": "-,2120220119151323301,2120220119151305936,2120220119151241725"
            }, {
                "appKey": "531772",
                "appVersion": "*",
                "creator": "兰茵",
                "deleter": "system",
                "gmtCreate": 1642576403000,
                "gmtCreateTime": "2022-01-19 15:13:23",
                "gmtModified": 1642581055000,
                "gmtModifiedTime": "2022-01-19 16:30:55",
                "gmtPublish": 1642576407000,
                "gmtPublishTime": "2022-01-19 15:13:27",
                "id": 112469,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 0,
                "md5": "27423d9e05a0962e4249547d608d5741",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "offlines": "{}",
                "previousResourceId": "nseea55792ad374f85b9005dc49f213227.json",
                "resourceId": "ns5d88f1b5033049e5befd8b373a0b7ea0.json",
                "reviewer": "兰茵",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "strategy": "app_ver=2.0",
                "type": 1,
                "valid": true,
                "version": "2120220119151323301",
                "versions": "-,2120220119151305936,2120220119151241725"
            }, {
                "appKey": "531772",
                "appVersion": "*",
                "creator": "兰茵",
                "gmtCreate": 1642576386000,
                "gmtCreateTime": "2022-01-19 15:13:06",
                "gmtModified": 1642576389000,
                "gmtModifiedTime": "2022-01-19 15:13:09",
                "gmtPublish": 1642576389000,
                "gmtPublishTime": "2022-01-19 15:13:09",
                "id": 112468,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 0,
                "md5": "629984da2c1469e5705c0d4493f877c5",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "offlines": "{}",
                "previousResourceId": "ns8a154100347f429b9f361cae9ec3d029.json",
                "resourceId": "nseea55792ad374f85b9005dc49f213227.json",
                "reviewer": "兰茵",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "strategy": "app_ver=1.0",
                "type": 1,
                "valid": true,
                "version": "2120220119151305936",
                "versions": "-,2120220119151241725"
            }, {
                "appKey": "531772",
                "appVersion": "*",
                "creator": "兰茵",
                "gmtCreate": 1647418815000,
                "gmtCreateTime": "2022-03-16 16:20:15",
                "gmtModified": 1648115033000,
                "gmtModifiedTime": "2022-03-24 17:43:53",
                "gmtPublish": 1648115033000,
                "gmtPublishTime": "2022-03-24 17:43:53",
                "id": 128553,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 0,
                "md5": "602c1f31c731b8e389530eecc020d510",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "offlines": "{\"2120220315141131043\":1}",
                "previousResourceId": "ns640f60f2ee964bdc9f6d3c2cb53b7691.json",
                "resourceId": "nsdb801b7b6fec4d6aa4f59a2b474e49a1.json",
                "reviewer": "39753",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "type": 1,
                "valid": true,
                "version": "2120220316162015310",
                "versions": "2120220120094810650,2120220119165441926,2120220119163210420,2120220119151323301,2120220119151305936,-"
            }],
            "namespaceBO": {
                "appKeyOrGroup": "531772",
                "auditingFlag": "free",
                "detail": "sss",
                "gmtCreate": 1588847299000,
                "gmtCreateTime": "2020-05-07 18:28:19",
                "gmtModified": 1648115041000,
                "gmtModifiedTime": "2022-03-24 17:44:01",
                "id": 2103,
                "isAvailable": "y",
                "loadLevel": 0,
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "owners": "39753,230608,180856",
                "testers": "100149",
                "type": 1,
                "valid": true
            },
            "noStrategyList": [{
                "appKey": "531772",
                "appVersion": "*",
                "creator": "兰茵",
                "gmtCreate": 1647418815000,
                "gmtCreateTime": "2022-03-16 16:20:15",
                "gmtModified": 1648115033000,
                "gmtModifiedTime": "2022-03-24 17:43:53",
                "gmtPublish": 1648115033000,
                "gmtPublishTime": "2022-03-24 17:43:53",
                "id": 128553,
                "isAvailable": "y",
                "isEmergent": "n",
                "loadLevel": 0,
                "md5": "602c1f31c731b8e389530eecc020d510",
                "name": "orange_test_lanyin",
                "namespaceId": "1fca1ef2da0b47d292571bcf2349508f",
                "offlines": "{\"2120220315141131043\":1}",
                "previousResourceId": "ns640f60f2ee964bdc9f6d3c2cb53b7691.json",
                "resourceId": "nsdb801b7b6fec4d6aa4f59a2b474e49a1.json",
                "reviewer": "39753",
                "source": 0,
                "sourceData": "",
                "status": 200,
                "type": 1,
                "valid": true,
                "version": "2120220316162015310",
                "versions": "2120220120094810650,2120220119165441926,2120220119163210420,2120220119151323301,2120220119151305936,-"
            }],
            "userMap": {
                "39753": {
                    "displayName": "兰茵",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "lanyin.smz",
                    "empId": "39753",
                    "hrStatus": "A",
                    "id": 65153,
                    "name": "邵明芝",
                    "nickNameCn": "兰茵",
                    "supervisorEmpId": "24523"
                },
                "230608": {
                    "displayName": "若存",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "daikui.dk",
                    "empId": "230608",
                    "hrStatus": "A",
                    "id": 1550049,
                    "name": "戴奎",
                    "nickNameCn": "若存",
                    "supervisorEmpId": "24523"
                },
                "180856": {
                    "displayName": "家愿",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "jialei.zjl",
                    "empId": "180856",
                    "hrStatus": "A",
                    "id": 1210876,
                    "name": "祝佳磊",
                    "nickNameCn": "家愿",
                    "supervisorEmpId": "24523"
                },
                "100149": {
                    "displayName": "骊驹",
                    "emailAddr": "<EMAIL>",
                    "emailPrefix": "zhendan.czd",
                    "empId": "100149",
                    "hrStatus": "A",
                    "id": 654447,
                    "name": "陈震旦",
                    "nickNameCn": "骊驹",
                    "supervisorEmpId": "105912"
                }
            },
            "viewConfig": {
                "maxGrayCntArray": [10, 10, 10],
                "oneStepSkip": false,
                "rollback": false,
                "rollbackAny": false,
                "showReport": true,
                "useGrayLink": "V1",
                "wholeProcess": true
            },
            "waitList": []
        }, "success": true
    }
}


function getPermissionApplyJSON() {
    return {"errorCode": "", "errorMsg": "", "model": "e8dcafc4-8874-4b24-92ff-6281ada64b7f", "success": true};
    // return{"errorCode":"","errorMsg":"","model":"SKIP","success":true}
}
