export default {
  'get /api/setting/queryAppList.json': (req, res) => {
    res.json(queryAppListJSON());
  },
  'get /api/setting/queryAppDetail.json': (req, res) => {
    res.json(queryAppDetailJSON());
  },
  'get /api/setting/appConfigSetting.json': (req, res) => {
    res.json(getCommonBooleanJSON());
  }
};
function getCommonJSON(){
  return {
    "errorCode": 1,
    "errorMessage": null,
    "module": {},
    "success": true
  };
}
function getCommonBooleanJSON(){
  return {
    "errorCode": 1,
    "errorMessage": null,
    "module": {result: true},
    "success": true
  };
}

function queryAppDetailJSON(){
  return {"errorCode":"","errorMsg":"","model":{"allReportTasks":["HH2DDTask","ConfigRateDDTask","IndexRateDDTask","CrashDDTask","OrangeCrashDDTask","BizCrashDDTask","ConfigRateHHTask","IndexRateHHTask","CrashHHTask","OrangeCrashHHTask","IntervalHHTask","ConfigUseRateDelayHHTask","UpdateRateDelayHHTask","ConfigUseIntervalTask","UpdateRateIntervalTask","BizCrashIntervalTask","ActiveUserIntervalTask","GlobalStatsTask","DDTopologies","HHTopologies","IntervalTopologies"],"appBO":{"appDetail":"淘宝iPhone客户端","appId":2017031500124,"appKey":"21380790","appMark":"taobao4iphone","appName":"淘宝iPhone客户端-ios","basicAppKey":"12087020","gmtCreate":1359597872000,"isAvailable":"y","motuAppId":"12087020@iphoneos","mtlId":"6","osType":1,"valid":true},"configKey":"21380790","grayConfig":{"configValueDO":{"grayEffectUrlTemplate":"https://wop.alibaba-inc.com/#/share?namespace=${name}&version=${version}&appKey=${appKey}","maxGrayCntOfMassCircle":200000,"minAppVersionOfSupportOsAndModel":"10.7.0","namespaceNamesOfSupportOsAndModel":"*"},"sysConfigDO":{"code":"gray","configKey":"21380790","configValue":"{\"grayEffectUrlTemplate\":\"https://wop.alibaba-inc.com/#/share?namespace=${name}&version=${version}&appKey=${appKey}\",\"maxGrayCntOfMassCircle\":200000,\"minAppVersionOfSupportOsAndModel\":\"10.7.0\",\"namespaceNamesOfSupportOsAndModel\":\"*\"}","gmtCreate":1627356001000,"gmtModified":1647236897000,"id":12,"isAvailable":"y","subCode":"app","valid":true}},"publishConfig":{"configValueDO":{"diffProbeGapMinutes":"60,360,1440","diffProbeGapMinutesList":[60,360,1440],"openChangeVersion":true,"openDiffProbe":true,"openRollbackAny":false},"sysConfigDO":{"code":"publish","configKey":"21380790","configValue":"{\"diffProbeGapMinutes\":\"60,360,1440\",\"diffProbeGapMinutesList\":[60,360,1440],\"openChangeVersion\":true,\"openDiffProbe\":true,\"openRollbackAny\":false}","gmtCreate":1647236876000,"gmtModified":1647236876000,"id":24,"isAvailable":"y","subCode":"app","valid":true}},"reportConfig":{"configValueDO":{"configRateReportId":24472,"configUpdateReportId":153959,"configUseReportId":154184,"dmInsightConfigUpdateReportId":47337,"enabledTasks":["HH2DDTask","IndexRateDDTask","OrangeCrashDDTask","BizCrashDDTask","ConfigRateHHTask","IndexRateHHTask","OrangeCrashHHTask","IntervalHHTask","BizCrashIntervalTask","ActiveUserIntervalTask","HHTopologies","DDTopologies","IntervalTopologies","ConfigRateDDTask","UpdateRateDelayHHTask","ConfigUseRateDelayHHTask"],"indexRateReportId":24467,"samplingRate":0.05},"sysConfigDO":{"code":"report","configKey":"21380790","configValue":"{\"configRateReportId\":24472,\"configUpdateReportId\":153959,\"configUseReportId\":154184,\"dmInsightConfigUpdateReportId\":47337,\"enabledTasks\":[\"HH2DDTask\",\"IndexRateDDTask\",\"OrangeCrashDDTask\",\"BizCrashDDTask\",\"ConfigRateHHTask\",\"IndexRateHHTask\",\"OrangeCrashHHTask\",\"IntervalHHTask\",\"BizCrashIntervalTask\",\"ActiveUserIntervalTask\",\"HHTopologies\",\"DDTopologies\",\"IntervalTopologies\",\"ConfigRateDDTask\",\"UpdateRateDelayHHTask\",\"ConfigUseRateDelayHHTask\"],\"indexRateReportId\":24467,\"samplingRate\":0.05}","gmtCreate":1594019411000,"gmtModified":1597047706000,"id":4,"isAvailable":"y","subCode":"app","valid":true}}},"success":true}
  return {"errorCode":"","errorMsg":"","model":{"grayConfig":{"configValueDO":{"grayEffectUrlTemplate":"http://30.10.49.155:3333/#/share?namespace=${name}&version=${version}&appKey=${appKey}","minAppVersionOfSupportOsAndModel": "10.7.0"},"sysConfigDO":{"code":"gray","configKey":"4272","configValue":"{\"grayEffectUrlTemplate\":\"http://30.10.49.155:3333/#/share?namespace=${name}&version=${version}&appKey=${appKey}\"}","gmtCreate":1626059811000,"gmtModified":1626059811000,"id":16,"isAvailable":"y","subCode":"app","valid":true}}, "allReportTasks":["HH2DDTask","ConfigRateDDTask","IndexRateDDTask","CrashDDTask","OrangeCrashDDTask","BizCrashDDTask","ConfigRateHHTask","IndexRateHHTask","CrashHHTask","OrangeCrashHHTask","IntervalHHTask","ConfigUseIntervalTask","UpdateRateIntervalTask","BizCrashIntervalTask","ActiveUserIntervalTask","GlobalStatsTask","DDTopologies","HHTopologies","IntervalTopologies"],"appBO":{"appDetail":"手机淘宝","appId":2017032401903,"appKey":"4272","appName":"手机淘宝-android","appPackageName":"com.taobao.taobao","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"4272@android","osType":2,"valid":true},"configKey":"4272","publishConfig":{"configValueDO":{"openDiffProbe":true},"sysConfigDO":{"code":"publish","configKey":"4272","configValue":"{\"openDiffProbe\":true}","gmtCreate":1591933990000,"gmtModified":1591933990000,"id":10,"isAvailable":"y","subCode":"app","valid":true}},"reportConfig":{"configValueDO":{"samplingRate":0.05,"statsDurationSeconds":3600},"sysConfigDO":{"code":"report","configKey":"4272","configValue":"{\"samplingRate\":0.05,\"statsDurationSeconds\": 3600}","gmtCreate":1591932371000,"gmtModified":1591932371000,"id":9,"isAvailable":"y","subCode":"app","valid":true}}},"success":true}
  //return {"errorCode":"","errorMsg":"","model":{"allReportTasks":["HH2DDTask","ConfigRateDDTask","IndexRateDDTask","CrashDDTask","OrangeCrashDDTask","BizCrashDDTask","ConfigRateHHTask","IndexRateHHTask","CrashHHTask","OrangeCrashHHTask","IntervalHHTask","ConfigUseIntervalTask","UpdateRateIntervalTask","BizCrashIntervalTask","ActiveUserIntervalTask","GlobalStatsTask","DDTopologies","HHTopologies","IntervalTopologies"],"configKey":"DEFAULT","globalPublishConfig":{"configValueDO":{},"sysConfigDO":{"code":"publish","configKey":"DEFAULT","configValue":"{}","gmtCreate":1592883837000,"gmtModified":1592883837000,"id":12,"isAvailable":"y","subCode":"global","valid":true}},"globalReportConfig":{"configValueDO":{"enabledTasks":["HH2DDTask","IndexRateDDTask","ConfigRateHHTask"]},"sysConfigDO":{"code":"report","configKey":"DEFAULT","configValue":"{\"enabledTasks\":[\"HH2DDTask\",\"IndexRateDDTask\",\"ConfigRateHHTask\"]}","gmtCreate":1591858443000,"gmtModified":1593413546000,"id":7,"isAvailable":"y","subCode":"global","valid":true}},"reportConfig":{"configValueDO":{"enabledTasks":["HH2DDTask","IndexRateDDTask"],"samplingRate":0.01},"sysConfigDO":{"code":"report","configKey":"DEFAULT","configValue":"{\"enabledTasks\":[\"HH2DDTask\",\"IndexRateDDTask\"],\"samplingRate\":0.01}","gmtCreate":1593413557000,"gmtModified":1593414060000,"id":13,"isAvailable":"y","subCode":"default","valid":true}}},"success":true}
}


function queryAppListJSON(){
  return {"errorCode":"","errorMsg":"","model":[{"appDetail":"高德淘金日常环境","appId":2017052404976,"appKey":"60028694","appName":"高德淘金日常环境-android","appPackageName":"com.autonavi.gxdtaojin","gmtCreate":1457680740000,"isAvailable":"y","motuAppId":"60028694@android","osType":2,"valid":true},{"appDetail":"ali-cloud","appId":2017051603585,"appKey":"60035627","appName":"来疯直播iOS客户端-ios","gmtCreate":1494899682000,"isAvailable":"y","motuAppId":"60035627@iphoneos","osType":1,"valid":true},{"appDetail":"阿里健康，医生端app","appId":2017050903574,"appKey":"60035352","appName":"医碟谷-ios","gmtCreate":1494320257000,"isAvailable":"y","motuAppId":"60035352@iphoneos","osType":1,"valid":true},{"appDetail":"ali-cloud","appId":2017050303553,"appKey":"60035020","appName":"来疯-android","appPackageName":"com.youku.crazytogether","gmtCreate":1493796152000,"isAvailable":"y","motuAppId":"60035020@android","osType":2,"valid":true},{"appDetail":"大麦日常环境-iOS企业版本","appId":2017050303552,"appKey":"60035015","appName":"大麦-ios","gmtCreate":1493783318000,"isAvailable":"y","motuAppId":"60035015@iphoneos","osType":1,"valid":true},{"appDetail":"ali-cloud","appId":2017050303551,"appKey":"60035014","appName":"大麦-android","appPackageName":"cn.damai","gmtCreate":1493782011000,"isAvailable":"y","motuAppId":"60035014@android","osType":2,"valid":true},{"appDetail":"菜鸟商家app","appId":2017042703543,"appKey":"60034706","appName":"掌货易-ios","gmtCreate":1493276488000,"isAvailable":"y","motuAppId":"60034706@iphoneos","osType":1,"valid":true},{"appDetail":"菜鸟商家无线客户端","appId":2017041303506,"appKey":"60034466","appName":"菜鸟商家-android","appPackageName":"com.cainiao.bms","gmtCreate":1492083613000,"isAvailable":"y","motuAppId":"60034466@android","osType":2,"valid":true},{"appDetail":"TVHelper测试IOS","appId":2017041303405,"appKey":"60021169","appName":"TVHelper测试IOS-ios","gmtCreate":1487300896000,"isAvailable":"y","motuAppId":"60021169@iphoneos","osType":1,"valid":true},{"appDetail":"优酷android日常","appId":2017041303129,"appKey":"60032872","appName":"优酷android日常-android","appPackageName":"com.youku.phone","gmtCreate":1481792170000,"isAvailable":"y","motuAppId":"60032872@android","osType":2,"valid":true},{"appDetail":"优酷 Android","appId":2017041303059,"appKey":"23033760","appName":"优酷 Android-android","appPackageName":"com.youku.phone","gmtCreate":1481706292000,"isAvailable":"y","motuAppId":"23033760@android","osType":2,"valid":true},{"appDetail":"我的通信","appId":2017041302427,"appKey":"60030067","appName":"mytel_android-android","appPackageName":"com.aliqin.mytel","gmtCreate":1465194512000,"isAvailable":"y","motuAppId":"60030067@android","osType":2,"valid":true},{"appDetail":"点点掌柜","appId":2017041302127,"appKey":"60007883","appName":"tongcheng_android-android","appPackageName":"com.taobao.tongcheng","gmtCreate":1449133954000,"isAvailable":"y","motuAppId":"60007883@android","osType":2,"valid":true},{"appDetail":"阿里健康iOS日常环境","appId":2017040502000,"appKey":"60034372","appName":"阿里健康iOS日常环境-ios","gmtCreate":1491362059000,"isAvailable":"y","motuAppId":"60034372@iphoneos","osType":1,"valid":true},{"appDetail":"淘鲜达POS机桌面定制LAUNCHER","appId":2017032901981,"appKey":"60034248","appName":"淘鲜达POS_Launcher-android","appPackageName":"com.wdk.pos.launcher","gmtCreate":1490776760000,"isAvailable":"y","motuAppId":"60034248@apad","osType":2,"valid":true},{"appDetail":"阿里钱盾android","appId":2017032401976,"appKey":"699163","appName":"阿里钱盾-android","appPackageName":"com.ali.money.shield","gmtCreate":*************,"isAvailable":"y","motuAppId":"699163@android","osType":2,"valid":true},{"appDetail":"阿里内外Android","appId":2017032401975,"appKey":"691106","appName":"阿里内外-android","appPackageName":"com.alibaba.work.android","gmtCreate":*************,"isAvailable":"y","motuAppId":"691106@android","osType":2,"valid":true},{"appDetail":"聚划算","appId":2017032401974,"appKey":"689607","appName":"聚划算-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"689607@iphoneos","osType":1,"valid":true},{"appDetail":"阿里内外-iPhone","appId":2017032401973,"appKey":"691107","appName":"阿里内外-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"691107@iphoneos","osType":1,"valid":true},{"appDetail":"淘宝彩票-android","appId":2017032401972,"appKey":"691439","appName":"淘宝彩票-android","appPackageName":"com.taobao.caipiao","gmtCreate":*************,"isAvailable":"y","motuAppId":"691439@android","osType":2,"valid":true},{"appDetail":"聚划算-android","appId":2017032401971,"appKey":"698482","appName":"聚划算-android","appPackageName":"com.taobao.ju.android","gmtCreate":*************,"isAvailable":"y","motuAppId":"698482@android","osType":2,"valid":true},{"appDetail":"口碑外卖iphone","appId":2017032401970,"appKey":"614210","appName":"口碑外卖-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"614210@iphoneos","osType":1,"valid":true},{"appDetail":"口碑外卖android","appId":2017032401969,"appKey":"614545","appName":"口碑外卖-android","appPackageName":"com.taobao.mobile.dipei","gmtCreate":*************,"isAvailable":"y","motuAppId":"614545@android","osType":2,"valid":true},{"appDetail":"1688-iphone","appId":2017032401968,"appKey":"688103","appName":"1688-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"688103@iphoneos","osType":1,"valid":true},{"appDetail":"淘宝旅行iPhone客户端","appId":2017032401967,"appKey":"607586","appName":"阿里旅行-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"607586@iphoneos","osType":1,"valid":true},{"appDetail":"阿里旅行-android","appId":2017032401966,"appKey":"60029300","appName":"阿里旅行-android","appPackageName":"com.taobao.trip","gmtCreate":*************,"isAvailable":"y","motuAppId":"60029300@android","osType":2,"valid":true},{"appDetail":"虾米音乐-iphone","appId":2017032401965,"appKey":"60029447","appName":"虾米音乐-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"60029447@iphoneos","osType":1,"valid":true},{"appDetail":"菜鸟农村物流PDA应用安装-android","appId":2017032401964,"appKey":"60029814","appName":"菜鸟农村物流PDA应用安装-android","gmtCreate":*************,"isAvailable":"y","motuAppId":"60029814@android","osType":2,"valid":true},{"appDetail":"孔明灯-android","appId":2017032401963,"appKey":"********","appName":"孔明灯-android","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@android","osType":2,"valid":true},{"appDetail":"孔明灯-iphone","appId":*************,"appKey":"********","appName":"孔明灯-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@iphoneos","osType":1,"valid":true},{"appDetail":"阿里云邮iPhone","appId":*************,"appKey":"********","appName":"阿里云邮-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@iphoneos","osType":1,"valid":true},{"appDetail":"UniversalAccountDemo","appId":*************,"appKey":"********","appName":"UniversalAccountDemo-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@iphoneos","osType":1,"valid":true},{"appDetail":"村淘物流android","appId":*************,"appKey":"********","appName":"村淘物流-android","appPackageName":"com.cainiao.cargo.owner","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@android","osType":2,"valid":true},{"appDetail":"章鱼店长iphone","appId":*************,"appKey":"********","appName":"章鱼店长-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@iphoneos","osType":1,"valid":true},{"appDetail":"章鱼店长android","appId":*************,"appKey":"********","appName":"章鱼店长-android","appPackageName":"com.taobao.myshop","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@android","osType":2,"valid":true},{"appDetail":"拍卖会iPhone","appId":*************,"appKey":"********","appName":"拍卖会-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@iphoneos","osType":1,"valid":true},{"appDetail":"1688wg-Android","appId":2017032401955,"appKey":"60029101","appName":"1688wg-android","appPackageName":"com.alibaba.wireless.microsupply","gmtCreate":*************,"isAvailable":"y","motuAppId":"60029101@android","osType":2,"valid":true},{"appDetail":"1688wg-iphone","appId":2017032401954,"appKey":"60029113","appName":"1688wg-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"60029113@iphoneos","osType":1,"valid":true},{"appDetail":"阿里妹iphone","appId":2017032401953,"appKey":"60027756","appName":"阿里妹-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"60027756@iphoneos","osType":1,"valid":true},{"appDetail":"阿里星球-iphone","appId":2017032401952,"appKey":"60027891","appName":"阿里星球-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"60027891@iphoneos","osType":1,"valid":true},{"appDetail":"淘我要android","appId":2017032401951,"appKey":"60027901","appName":"淘我要-android","appPackageName":"com.taobao.android.need","gmtCreate":*************,"isAvailable":"y","motuAppId":"60027901@android","osType":2,"valid":true},{"appDetail":"手机阿里云-iOS","appId":2017032401950,"appKey":"60028015","appName":"手机阿里云-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"60028015@iphoneos","osType":1,"valid":true},{"appDetail":"手机阿里云-android","appId":2017032401949,"appKey":"60028021","appName":"手机阿里云-android","appPackageName":"com.alibaba.aliyun","gmtCreate":*************,"isAvailable":"y","motuAppId":"60028021@android","osType":2,"valid":true},{"appDetail":"虾米音乐-android","appId":2017032401948,"appKey":"60028040","appName":"虾米音乐-android","appPackageName":"fm.xiami.main","gmtCreate":*************,"isAvailable":"y","motuAppId":"60028040@android","osType":2,"valid":true},{"appDetail":"1688-Android","appId":2017032401947,"appKey":"60028100","appName":"1688-android","appPackageName":"com.alibaba.wireless","gmtCreate":*************,"isAvailable":"y","motuAppId":"60028100@android","osType":2,"valid":true},{"appDetail":"login4android_demo","appId":2017032401946,"appKey":"60028140","appName":"login4_demo-android","appPackageName":"com.taobao.logindemowithautologin","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60028140@android","osType":2,"valid":true},{"appDetail":"A.I Trip-iphone","appId":2017032401945,"appKey":"60028178","appName":"A.I Trip-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60028178@iphoneos","osType":1,"valid":true},{"appDetail":"ali-cloud","appId":2017032401944,"appKey":"60027341","appName":"菜鸟裹裹-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027341@iphoneos","osType":1,"valid":true},{"appDetail":"阿里妈妈android","appId":2017032401943,"appKey":"60027342","appName":"阿里妈妈-android","appPackageName":"com.taobao.kepler","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027342@android","osType":2,"valid":true},{"appDetail":"点点送iphone","appId":2017032401942,"appKey":"60028042","appName":"点点送-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60028042@iphoneos","osType":1,"valid":true},{"appDetail":"淘我要-iphone","appId":2017032401941,"appKey":"60027367","appName":"淘我要-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027367@iphoneos","osType":1,"valid":true},{"appDetail":"喵街客户端iphone","appId":2017032401940,"appKey":"60027434","appName":"喵街客户端-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027434@iphoneos","osType":1,"valid":true},{"appDetail":"盒马鲜生","appId":2017032401939,"appKey":"60027608","appName":"盒马鲜生-android","appPackageName":"com.wudaokou.hippo","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027608@android","osType":2,"valid":true},{"appDetail":"零售通买家版-iphone","appId":2017032401938,"appKey":"60027676","appName":"零售通买家版-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027676@iphoneos","osType":1,"valid":true},{"appDetail":"零售通买家版-android","appId":2017032401937,"appKey":"60027703","appName":"零售通买家版-android","appPackageName":"com.alibaba.wireless.lstretailer","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027703@android","osType":2,"valid":true},{"appDetail":"零小宝-iOS","appId":2017032401936,"appKey":"60027731","appName":"零小宝-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027731@iphoneos","osType":1,"valid":true},{"appDetail":"喵街卖家android","appId":2017032401935,"appKey":"60026637","appName":"喵街卖家-android","appPackageName":"com.taobao.shoppingmanagement","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60026637@android","osType":2,"valid":true},{"appDetail":"爱逛街-andorid","appId":2017032401934,"appKey":"60026640","appName":"爱逛街-android","appPackageName":"com.taobao.ishopping","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60026640@android","osType":2,"valid":true},{"appDetail":"阿里星球-android","appId":2017032401933,"appKey":"60026663","appName":"阿里星球-android","appPackageName":"com.sds.android.ttpod","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60026663@android","osType":2,"valid":true},{"appDetail":"爱逛街-iphone","appId":2017032401932,"appKey":"60026667","appName":"爱逛街-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60026667@iphoneos","osType":1,"valid":true},{"appDetail":"盒马驾到-android","appId":2017032401931,"appKey":"60027222","appName":"盒马驾到-android","appPackageName":"com.wudaokou.flyingfish","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027222@android","osType":2,"valid":true},{"appDetail":"阿里妈妈","appId":2017032401930,"appKey":"60027264","appName":"阿里妈妈-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027264@iphoneos","osType":1,"valid":true},{"appDetail":"盒马驾到-iphone","appId":2017032401929,"appKey":"60027280","appName":"盒马驾到-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027280@iphoneos","osType":1,"valid":true},{"appDetail":"盒马鲜生iphone","appId":2017032401928,"appKey":"60027281","appName":"盒马鲜生-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60027281@iphoneos","osType":1,"valid":true},{"appDetail":"点点送android","appId":2017032401927,"appKey":"60020831","appName":"点点送-android","appPackageName":"com.taobao.ddcarry","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60020831@android","osType":2,"valid":true},{"appDetail":"村淘无线政府版-iOS","appId":2017032401926,"appKey":"60020826","appName":"村淘无线政府版-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60020826@iphoneos","osType":1,"valid":true},{"appDetail":"移动工作平台-iOS","appId":2017032401925,"appKey":"60023095","appName":"移动工作平台-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60023095@iphoneos","osType":1,"valid":true},{"appDetail":"天猫客户端android","appId":2017032401924,"appKey":"60023163","appName":"天猫客户端-android","appPackageName":"com.tmall.wireless","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60023163@android","osType":2,"valid":true},{"appDetail":"村淘工作平台-iphone","appId":2017032401923,"appKey":"60024417","appName":"村淘工作平台-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60024417@iphoneos","osType":1,"valid":true},{"appDetail":"口碑外卖掌柜iphone","appId":2017032401922,"appKey":"60024890","appName":"口碑外卖掌柜-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60024890@iphoneos","osType":1,"valid":true},{"appDetail":"淘宝全球-iOS","appId":2017032401921,"appKey":"60026310","appName":"淘宝全球-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60026310@iphoneos","osType":1,"valid":true},{"appDetail":"村淘工作平台-android","appId":2017032401920,"appKey":"60024418","appName":"村淘工作平台-android","appPackageName":"com.ali.cuntaocrm","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60024418@android","osType":2,"valid":true},{"appDetail":"闲鱼android","appId":2017032401919,"appKey":"60013903","appName":"闲鱼-android","appPackageName":"com.taobao.fleamarket","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60013903@android","osType":2,"valid":true},{"appDetail":"钉钉iphone","appId":2017032401918,"appKey":"60011816","appName":"钉钉-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60011816@iphoneos","osType":1,"valid":true},{"appDetail":"喵街客户端android","appId":2017032401917,"appKey":"60016286","appName":"喵街客户端-android","appPackageName":"com.taobao.shoppingstreets","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60016286@android","osType":2,"valid":true},{"appDetail":"农村淘宝iphone","appId":2017032401916,"appKey":"60016812","appName":"农村淘宝-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60016812@iphoneos","osType":1,"valid":true},{"appDetail":"菜鸟裹裹android","appId":2017032401915,"appKey":"60015330","appName":"菜鸟裹裹-android","appPackageName":"com.cainiao.wireless","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60015330@android","osType":2,"valid":true},{"appDetail":"村淘无线政府版-android","appId":2017032401914,"appKey":"60020825","appName":"村淘无线政府版-android","appPackageName":"com.ali.crm","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60020825@android","osType":2,"valid":true},{"appDetail":"阿里智能-iphone","appId":2017032401913,"appKey":"60017710","appName":"阿里智能-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60017710@iphoneos","osType":1,"valid":true},{"appDetail":"淘宝电影iOS","appId":2017032401912,"appKey":"60003111","appName":"淘票票-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60003111@iphoneos","osType":1,"valid":true},{"appDetail":"农村淘宝android","appId":2017032401911,"appKey":"60011625","appName":"农村淘宝-android","appPackageName":"com.taobao.cun","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60011625@android","osType":2,"valid":true},{"appDetail":"阿里钱盾iphone","appId":2017032401910,"appKey":"60012593","appName":"阿里钱盾-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60012593@iphoneos","osType":1,"valid":true},{"appDetail":"手机淘宝ipad","appId":2017032401909,"appKey":"531771","appName":"手机淘宝ipad-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"531771@ipad","osType":1,"valid":true},{"appDetail":"淘票票android","appId":2017032401908,"appKey":"60003110","appName":"淘票票-android","appPackageName":"com.taobao.movie.android","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"60003110@android","osType":2,"valid":true},{"appDetail":"天猫客户端","appId":2017032401907,"appKey":"489132","appName":"天猫客户端-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"489132@iphoneos","osType":1,"valid":true},{"appDetail":"闲鱼","appId":2017032401906,"appKey":"541941","appName":"闲鱼-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"541941@iphoneos","osType":1,"valid":true},{"appDetail":"手机淘宝","appId":2017032401905,"appKey":"531772","appName":"手机淘宝-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"531772@iphoneos","osType":1,"valid":true},{"appDetail":"阿里数据-iphone","appId":2017032401904,"appKey":"23436183","appName":"阿里数据-ios","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"23436183@iphoneos","osType":1,"valid":true},{"appDetail":"手机淘宝","appId":2017032401903,"appKey":"4272","appName":"手机淘宝-android","appPackageName":"com.taobao.taobao","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"4272@android","osType":2,"valid":true},{"appDetail":"千牛android","appId":2017032401902,"appKey":"23263161","appName":"千牛-android","gmtCreate":1490343869000,"isAvailable":"y","motuAppId":"23263161@android","osType":2,"valid":true},{"appDetail":"阿里健康iphone","appId":2017032401901,"appKey":"23205033","appName":"阿里健康-ios","gmtCreate":1490343674000,"isAvailable":"y","motuAppId":"23205033@iphoneos","osType":1,"valid":true},{"appDetail":"UT-demo","appId":2017032401900,"appKey":"21771303","appName":"UT-demo-android","gmtCreate":1490343674000,"isAvailable":"y","motuAppId":"21771303@android","osType":2,"valid":true},{"appDetail":"rainbow_ios","appId":2017032201000,"appKey":"60034139","appName":"rainbow_daily-ios","gmtCreate":1490183658000,"isAvailable":"y","motuAppId":"60034139@iphoneos","osType":1,"valid":true},{"appDetail":"君慢应用中心测试7","appId":2017032100990,"appKey":"60034131","appName":"君慢应用中心测试7-yunos","appPackageName":"com.taobao.junman.test7","gmtCreate":1490098036000,"isAvailable":"y","motuAppId":"60034131@android","osType":4,"valid":true},{"appDetail":"aa兰茵的应用兰茵的应用兰茵的应用兰茵的应用兰茵兰茵的应用兰茵的应用兰茵的应用兰茵的应用兰茵的应用兰茵的应用兰茵的应aaa。","appId":2017032100979,"appKey":"60034126","appName":"la兰茵d的-android","appPackageName":"com.taobao.lanyin3","gmtCreate":1490093580000,"isAvailable":"y","motuAppId":"60034126@android","osType":2,"valid":true},{"appDetail":"优酷机房和新加坡机房信息。zz","appId":2017032100977,"appKey":"60034114","appName":"兰茵的测试应用-android","appPackageName":"com.taobao.lanyin123214","basicAppKey":"60034114","gmtCreate":1490074235000,"isAvailable":"y","motuAppId":"60034114@android","osType":2,"valid":true},{"appDetail":"新点点虫","appId":2017032000970,"appKey":"60034102","appName":"rainbow_daily-android","appPackageName":"com.alibaba.android.luffy","gmtCreate":1489982257000,"isAvailable":"y","motuAppId":"60034102@android","osType":2,"valid":true},{"appDetail":"君慢应用中心测试3","appId":2017031700969,"appKey":"60034100","appName":"君慢应用中心测试3-android","appPackageName":"com.taobao.junman.11","basicAppKey":"60034100","gmtCreate":1489743212000,"isAvailable":"y","motuAppId":"60034100@apad","osType":2,"valid":true},{"appDetail":"memo","appId":2017031700968,"appKey":"60034072","appName":"lanyin46-ios","gmtCreate":1489738011000,"isAvailable":"y","motuAppId":"60034072@iphoneos","osType":1,"valid":true},{"appDetail":"君慢测试","appId":2017031600961,"appKey":"60034063","appName":"兰茵的双十是-yunos","appPackageName":"com.mappcenter.lanyin.test","gmtCreate":1489662664000,"isAvailable":"y","motuAppId":"60034063@android","osType":4,"valid":true},{"appDetail":"线下的认证终端通用方案应用","appId":2017031600960,"appKey":"60034096","appName":"VerifyClient-android","appPackageName":"com.alibaba.security.rp.verifyclient","gmtCreate":1489659342000,"isAvailable":"y","motuAppId":"60034096@android","osType":2,"valid":true},{"appDetail":"驿站无线工作台，主要是为了驿站工作人员使用的","appId":2017031500812,"appKey":"60034030","appName":"驿站无线工作台_ios-ios","gmtCreate":1489549208000,"isAvailable":"y","motuAppId":"60034030@iphoneos","osType":1,"valid":true},{"appDetail":"O2O全渠道POS","appId":2017031400652,"appKey":"60033997","appName":"o2opos-android","gmtCreate":1489477562000,"isAvailable":"y","motuAppId":"60033997@android","osType":2,"valid":true},{"appDetail":"这是一个测试应用","appId":2017031400512,"appKey":"60034004","appName":"百罗应用中心测试A-android","appPackageName":"com.alibaba.mappcenter.daily.net.bla","gmtCreate":1489477286000,"isAvailable":"y","motuAppId":"60034004@apad","osType":2,"valid":true},{"appDetail":"新县域新流通，即商贸项目，是菜鸟的第一个面向B2B交易的前端业务，提供给县域商家使用","appId":2017031400508,"appKey":"60033858","appName":"橙运iOS-ios","gmtCreate":1488516224000,"isAvailable":"y","motuAppId":"60033858@iphoneos","osType":1,"valid":true},{"appDetail":"运行在银泰线下专柜移动POS机上，负责收银、导购等功能的app","appId":2017031400499,"appKey":"60033734","appName":"导购百宝箱-android","gmtCreate":1488274885000,"isAvailable":"y","motuAppId":"60033734@android","osType":2,"valid":true},{"appDetail":"社区零售云android客户端","appId":2017031400497,"appKey":"60033386","appName":"社区零售云android客户端-android","appPackageName":"com.tmall.communityretail","gmtCreate":1484889473000,"isAvailable":"y","motuAppId":"60033386@android","osType":2,"valid":true},{"appDetail":"阿里郎_Android","appId":2017031400492,"appKey":"60033461","appName":"阿里郎_Android-android","appPackageName":"com.alibaba.android.security.activity","gmtCreate":1486536312000,"isAvailable":"y","motuAppId":"60033461@android","osType":2,"valid":true},{"appDetail":"阿里郎测试环境","appId":2017031400491,"appKey":"60033470","appName":"阿里郎_iOS-ios","gmtCreate":1486536574000,"isAvailable":"y","motuAppId":"60033470@iphoneos","osType":1,"valid":true},{"appDetail":"阿里通信门户App_iOS","appId":2017031400484,"appKey":"60033182","appName":"阿里通信门户App_iOS-ios","gmtCreate":1483523991000,"isAvailable":"y","motuAppId":"60033182@iphoneos","osType":1,"valid":true},{"appDetail":"新县域新流通，即商贸项目，是菜鸟的第一个面向B2B交易的前端业务，提供给县域商家使用","appId":2017031400479,"appKey":"60033104","appName":"橙运-android","appPackageName":"com.cainiao.chengyun","gmtCreate":1482891483000,"isAvailable":"y","motuAppId":"60033104@android","osType":2,"valid":true},{"appDetail":"淘宝联盟","appId":2017031400477,"appKey":"60033141","appName":"淘宝联盟iphone-ios","gmtCreate":1483089900000,"isAvailable":"y","motuAppId":"60033141@iphoneos","osType":1,"valid":true},{"appDetail":"淘宝联盟android","appId":2017031400476,"appKey":"60033142","appName":"淘宝联盟android-android","appPackageName":"com.taobao.taobao","gmtCreate":1483089955000,"isAvailable":"y","motuAppId":"60033142@android","osType":2,"valid":true},{"appDetail":"为商家提供便捷收款，管理等操作","appId":2017031400475,"appKey":"60033173","appName":"飞猪商家iOS-ios","gmtCreate":1483512811000,"isAvailable":"y","motuAppId":"60033173@iphoneos","osType":1,"valid":true},{"appDetail":"youku_android","appId":2017031400470,"appKey":"60032873","appName":"youku_android-ios","gmtCreate":1481783968000,"isAvailable":"y","motuAppId":"60032873@iphoneos","osType":1,"valid":true},{"appDetail":"虾米音乐LITE，为YUNOS定制的精简版本","appId":2017031400469,"appKey":"60032333","appName":"虾米音乐LITE_YUNOS-android","gmtCreate":1479884524000,"isAvailable":"y","motuAppId":"60032333@android","osType":2,"valid":true},{"appDetail":"五道口团队RF应用","appId":2017031400468,"appKey":"60032556","appName":"RF-android","appPackageName":"com.wudaokou.rf","gmtCreate":1480320839000,"isAvailable":"y","motuAppId":"60032556@android","osType":2,"valid":true},{"appDetail":"菜鸟园区管理","appId":2017031400467,"appKey":"60032566","appName":"菜鸟园区android-android","appPackageName":"com.cainiao.park","gmtCreate":1480332313000,"isAvailable":"y","motuAppId":"60032566@android","osType":2,"valid":true},{"appDetail":"菜鸟园区管理","appId":2017031400466,"appKey":"60032559","appName":"菜鸟园区ios-ios","gmtCreate":1480332364000,"isAvailable":"y","motuAppId":"60032559@iphoneos","osType":1,"valid":true},{"appDetail":"手淘App，在iPad端，日常环境","appId":2017031400464,"appKey":"60032765","appName":"手淘App_iPad端_日常-ios","gmtCreate":1481103892000,"isAvailable":"y","motuAppId":"60032765@ipad","osType":1,"valid":true},{"appDetail":"用于支持国际站销售团队的移动办公平台","appId":2017031400463,"appKey":"60032766","appName":"ICBU_CRM移动工作平台-android","appPackageName":"com.ali.crm","gmtCreate":1481104906000,"isAvailable":"y","motuAppId":"60032766@android","osType":2,"valid":true},{"appDetail":"旺信App","appId":2017031400462,"appKey":"60032096","appName":"旺信-ios","gmtCreate":1478588082000,"isAvailable":"y","motuAppId":"60032096@iphoneos","osType":1,"valid":true},{"appDetail":"阿里郎生态版对阿里郎进行生态化改造，支持开放账号体系，为集团前台业务发展保驾护航","appId":2017031400458,"appKey":"60032172","appName":"阿里郎生态版_TAndroid-android","gmtCreate":1479113946000,"isAvailable":"y","motuAppId":"60032172@android","osType":2,"valid":true},{"appDetail":"阿里郎生态化版本，为生态化企业提供动态令牌服务","appId":2017031400457,"appKey":"60032163","appName":"阿里郎_iOS_Test-ios","gmtCreate":1479174166000,"isAvailable":"y","motuAppId":"60032163@iphoneos","osType":1,"valid":true},{"appDetail":"阿里健康Android客户端日常环境","appId":2017031400450,"appKey":"60031612","appName":"阿里健康客户端日常环境-android","gmtCreate":1474959622000,"isAvailable":"y","motuAppId":"60031612@android","osType":2,"valid":true},{"appDetail":"大宝WMS无线RF","appId":2017031400446,"appKey":"60031819","appName":"大宝WMS无线RF-android","gmtCreate":1476948147000,"isAvailable":"y","motuAppId":"60031819@android","osType":2,"valid":true},{"appDetail":"小区pos机","appId":2017031400444,"appKey":"60031470","appName":"小区pos机-android","appPackageName":"com.taobao.octopus","gmtCreate":1473666567000,"isAvailable":"y","motuAppId":"60031470@android","osType":2,"valid":true},{"appDetail":"阿里新零售平台主要服务于供应商和零售商，核心为双方提供b2b供货采购合作平台，及为零售商提供b2c门店管理、销售、会员运营的价值。","appId":2017031400442,"appKey":"60031471","appName":"阿里新零售Android-android","gmtCreate":1473736449000,"isAvailable":"y","motuAppId":"60031471@android","osType":2,"valid":true},{"appDetail":"新零售平台app客户端","appId":2017031400440,"appKey":"60031591","appName":"阿里新零售-ios","gmtCreate":1474428572000,"isAvailable":"y","motuAppId":"60031591@iphoneos","osType":1,"valid":true},{"appDetail":"驿站广告平台日常","appId":2017031400430,"appKey":"60031270","appName":"驿站广告平台日常-android","appPackageName":"com.cainiao.stationadvertisement","gmtCreate":1471937120000,"isAvailable":"y","motuAppId":"60031270@android","osType":2,"valid":true},{"appDetail":"ali-cloud","appId":2017031400429,"appKey":"60031280","appName":"包裹侠-ios","basicAppKey":"23435543","gmtCreate":1471939123000,"isAvailable":"y","motuAppId":"23435543@iphoneos","osType":1,"valid":true},{"appDetail":"在县域一级的运力市场中，基本上还是空白状态，都处在无政府无组织的状态，线下恶性竞争激烈，钱货交易原始，对于菜鸟来说可以依托这个契机，正好搭建一个公共的运力市场。","appId":2017031400417,"appKey":"60030985","appName":"县域智能物流小件员-android","gmtCreate":1469783414000,"isAvailable":"y","motuAppId":"60030985@android","osType":2,"valid":true},{"appDetail":"为阿里员工方便获取运营数据，开发的一个内部app应用","appId":2017031400409,"appKey":"60030881","appName":"阿里数据-android","gmtCreate":1469414506000,"isAvailable":"y","motuAppId":"60030881@android","osType":2,"valid":true},{"appDetail":"为校园包裹侠提供抢单APP","appId":2017031400408,"appKey":"60030901","appName":"青果派-android","appPackageName":"com.cainiao.station.pie","gmtCreate":1469428150000,"isAvailable":"y","motuAppId":"60030901@android","osType":2,"valid":true},{"appDetail":"dfasdfads","appId":2017031400403,"appKey":"60030692","appName":"asdfs-android","gmtCreate":1468395361000,"isAvailable":"y","motuAppId":"60030692@android","osType":2,"valid":true},{"appDetail":"为Yunos用户提供支付安全，资金安全，钱盾保险箱等功能","appId":2017031400401,"appKey":"60030730","appName":"阿里钱盾云OS版-android","gmtCreate":1468827310000,"isAvailable":"y","motuAppId":"60030730@android","osType":2,"valid":true},{"appDetail":"在县域一级的运力市场中，基本上还是空白状态，都处在无政府无组织的状态，线下恶性竞争激烈，钱货交易原始，对于菜鸟来说可以依托这个契机，正好搭建一个公共的运力市场。","appId":2017031400400,"appKey":"60030747","appName":"县域智能物流货主端-android","appPackageName":"com.cainiao.cargo.owner","gmtCreate":1468892270000,"isAvailable":"y","motuAppId":"60030747@android","osType":2,"valid":true},{"appDetail":"在县域一级的运力市场中，基本上还是空白状态，都处在无政府无组织的状态，线下恶性竞争激烈，钱货交易原始，对于菜鸟来说可以依托这个契机，正好搭建一个公共的运力市场。","appId":2017031400399,"appKey":"60030748","appName":"县域智能物流司机端-android","appPackageName":"com.cainiao.cargo.driver","gmtCreate":1468892325000,"isAvailable":"y","motuAppId":"60030748@android","osType":2,"valid":true},{"appDetail":"在县域一级的运力市场中，基本上还是空白状态，都处在无政府无组织的状态，线下恶性竞争激烈，钱货交易原始，对于菜鸟来说可以依托这个契机，正好搭建一个公共的运力市场。","appId":2017031400398,"appKey":"60030749","appName":"县域智能物流货栈端PDA-android","appPackageName":"com.cainiao.cargo.terminal","gmtCreate":1468892425000,"isAvailable":"y","motuAppId":"60030749@android","osType":2,"valid":true},{"appDetail":"直播一体机主客户端","appId":2020052800520,"appKey":"60044083","appName":"MinDLiveClient-android","appPackageName":"com.aliyun.mindlive","gmtCreate":1590644436000,"isAvailable":"y","motuAppId":"60044083@apad","osType":2,"valid":true},{"appDetail":"弘翊的IOS测试应用3","appId":2020051500509,"appKey":"60044073","appName":"弘翊的IOS测试应用3-ios","gmtCreate":1589524288000,"isAvailable":"y","motuAppId":"60044073@iphoneos","osType":1,"valid":true},{"appDetail":"交易猫日常","appId":2020051500506,"appKey":"60044070","appName":"交易猫租号daily-android","appPackageName":"com.jym.zuhao","gmtCreate":1589512805000,"isAvailable":"y","motuAppId":"60044070@android","osType":2,"valid":true},{"appDetail":"阿里工业品是一个工业品类目的采购销售平台，阿里工业品_ios 是其ios端","appId":2020050800502,"appKey":"60044056","appName":"阿里工业品_ios-ios","basicAppKey":"60044056","gmtCreate":1588916725000,"isAvailable":"y","motuAppId":"60044056@iphoneos","osType":1,"valid":true},{"appDetail":"阿里工业品_android\n阿里工业品是一个工业品类目的采购销售平台，阿里工业品_android是其android端","appId":2020050800501,"appKey":"60044057","appName":"阿里工业品_android-android","appPackageName":"com.alibaba.mro","gmtCreate":1588915825000,"isAvailable":"y","motuAppId":"60044057@android","osType":2,"valid":true},{"appDetail":"菜鸟末端商业数据采集器","appId":2020042400493,"appKey":"60044027","appName":"末端商业采集器-android","appPackageName":"com.cainiao.mdec.spider","gmtCreate":1587698173000,"isAvailable":"y","motuAppId":"60044027@android","osType":2,"valid":true},{"appDetail":"阿里云游戏_iOS_iPhone","appId":2020032400473,"appKey":"60044001","appName":"阿里云游戏_iOS_iPhone-ios","gmtCreate":1585048057000,"isAvailable":"y","motuAppId":"60044001@iphoneos","osType":1,"valid":true},{"appDetail":"pow设备桌面应用","appId":2020030500457,"appKey":"60043973","appName":"PLauncher-android","appPackageName":"com.caini.cniot.plauncher","gmtCreate":1583388565000,"isAvailable":"y","motuAppId":"60043973@apad","osType":2,"valid":true},{"appDetail":"点我达_android1","appId":2020030400456,"appKey":"60043952","appName":"点我达_android1-android","appPackageName":"com.dwd.rider","gmtCreate":1583316971000,"isAvailable":"y","motuAppId":"60043952@android","osType":2,"valid":true},{"appDetail":"com.dianwoda.DWDRider","appId":2020022600439,"appKey":"60043923","appName":"点我达骑手端iOS-ios","basicAppKey":"60043923","gmtCreate":1582702960000,"isAvailable":"y","motuAppId":"60043923@iphoneos","osType":1,"valid":true},{"appDetail":"面向C端用户的农产品购买App。","appId":2020022500432,"appKey":"60043895","appName":"觅蜂客户端Android-android","appPackageName":"com.mifeng.mobile","gmtCreate":1582614140000,"isAvailable":"y","motuAppId":"60043895@android","osType":2,"valid":true},{"appDetail":"云游戏的Android应用","appId":2020022400429,"appKey":"60043904","appName":"阿里云游戏_android-android","appPackageName":"com.alibaba.cloudgame","gmtCreate":1582535127000,"isAvailable":"y","motuAppId":"60043904@android","osType":2,"valid":true},{"appDetail":"云游戏_iOS_iPhone5","appId":2020021900425,"appKey":"60043880","appName":"云游戏_iOS_iPhone5-ios","gmtCreate":1582081312000,"isAvailable":"y","motuAppId":"60043880@iphoneos","osType":1,"valid":true},{"appDetail":"Lex is Lazada express application for drivers who deliver packages","appId":2020020400375,"appKey":"60043843","appName":"Lex-android","appPackageName":"com.lazada.lmsandroid.development","gmtCreate":1580804093000,"isAvailable":"y","motuAppId":"60043843@android","osType":2,"valid":true},{"appDetail":"测试","appId":2020020400373,"appKey":"60043841","appName":"端鉴黄-android","appPackageName":"com.alibaba.security.tblive","gmtCreate":1580797562000,"isAvailable":"y","motuAppId":"60043841@android","osType":2,"valid":true},{"appDetail":"面向C端用户的农产品购买App。","appId":2020010200316,"appKey":"60043812","appName":"觅蜂-android","appPackageName":"com.alibaba.bee","gmtCreate":1577933688000,"isAvailable":"y","motuAppId":"60043812@android","osType":2,"valid":true},{"appDetail":"淘花","appId":2019121900310,"appKey":"60043779","appName":"淘花-android","appPackageName":"com.alibaba.cun.superb","gmtCreate":1576741847000,"isAvailable":"y","motuAppId":"60043779@android","osType":2,"valid":true},{"appDetail":"淘花","appId":2019121900309,"appKey":"60043783","appName":"淘花-ios","gmtCreate":1576736199000,"isAvailable":"y","motuAppId":"60043783@iphoneos","osType":1,"valid":true},{"appDetail":"阿里体育校园体测项目 iOS 端","appId":2019120200293,"appKey":"60043733","appName":"阿里体育校园体测_iOS-ios","gmtCreate":1575253525000,"isAvailable":"y","motuAppId":"60043733@iphoneos","osType":1,"valid":true},{"appDetail":"考拉海购iOS","appId":2019110400247,"appKey":"60043688","appName":"考拉海购iOS-ios","gmtCreate":1572866242000,"isAvailable":"y","motuAppId":"60043688@iphoneos","osType":1,"valid":true},{"appDetail":"目前在开发动态hook敏感api的sdk，需要接入orange。申请一个app做测试。","appId":2019103000235,"appKey":"60043653","appName":"mytestorange2-android","appPackageName":"com.example.myaa2","gmtCreate":1572415889000,"isAvailable":"y","motuAppId":"60043653@android","osType":2,"valid":true},{"appDetail":"安全部统一id demo app，测试演示使用，不上线","appId":2019102300225,"appKey":"60043640","appName":"onesdkdemo-ios","gmtCreate":1571830116000,"isAvailable":"y","motuAppId":"60043640@iphoneos","osType":1,"valid":true},{"appDetail":"考拉海购Android","appId":2019101100217,"appKey":"60043689","appName":"考拉海购Android-android","appPackageName":"com.kaola","gmtCreate":1570790537000,"isAvailable":"y","motuAppId":"60043689@android","osType":2,"valid":true},{"appDetail":"阿里云数据宝app","appId":2019093000213,"appKey":"60043462","appName":"数据宝-android","appPackageName":"com.alicloud.databox","gmtCreate":1569807603000,"isAvailable":"y","motuAppId":"60043462@android","osType":2,"valid":true},{"appDetail":"蓝豚司机App","appId":2019082300180,"appKey":"60043321","appName":"蓝豚司机App_iOS-ios","gmtCreate":1566526648000,"isAvailable":"y","motuAppId":"60043321@iphoneos","osType":1,"valid":true},{"appDetail":"蓝豚独立App","appId":2019080600160,"appKey":"60043254","appName":"蓝豚App_Android-android","appPackageName":"com.cainiao.lantun.android","gmtCreate":1565074559000,"isAvailable":"y","motuAppId":"60043254@android","osType":2,"valid":true},{"appDetail":"菜鸟包裹侠Android版","appId":2019072900151,"appKey":"60043245","appName":"包裹侠Android-android","appPackageName":"com.cainiao.cs","gmtCreate":1564405853000,"isAvailable":"y","motuAppId":"60043245@android","osType":2,"valid":true},{"appDetail":"智分宝安卓版","appId":2019072600147,"appKey":"60043226","appName":"智分宝安卓版-android","appPackageName":"com.cainiao.android.zfb","gmtCreate":1564129567000,"isAvailable":"y","motuAppId":"60043226@android","osType":2,"valid":true},{"appDetail":"零售终端智能POS，红星美凯龙","appId":2019072600145,"appKey":"60043242","appName":"零售终端POS_红星-android","appPackageName":"com.tmall.smartpos.redstar","gmtCreate":1564107984000,"isAvailable":"y","motuAppId":"60043242@android","osType":2,"valid":true},{"appDetail":"天猫服务工单履约APP，提供给工人、阿姨等使用","appId":2019072200126,"appKey":"60043218","appName":"喵师傅专业版-ios","gmtCreate":1563777842000,"isAvailable":"y","motuAppId":"60043218@iphoneos","osType":1,"valid":true},{"appDetail":"天猫服务工单履约APP，提供给工人、阿姨等使用","appId":2019072200125,"appKey":"60043217","appName":"喵师傅专业版-android","appPackageName":"com.tmall.mmaster2","gmtCreate":1563777763000,"isAvailable":"y","motuAppId":"60043217@android","osType":2,"valid":true},{"appDetail":"阿里互娱-九游客户端","appId":2019071600119,"appKey":"60043222","appName":"九游客户端-android","appPackageName":"cn.ninegame.gamemanager","basicAppKey":"23067643","gmtCreate":1563283065000,"isAvailable":"y","motuAppId":"23067643@android","osType":2,"valid":true},{"appDetail":"日常环境台湾项目卖家端","appId":2019071500107,"appKey":"60043198","appName":"HaoWangJiao_Seller_iOS-ios","gmtCreate":1563158142000,"isAvailable":"y","motuAppId":"60043198@iphoneos","osType":1,"valid":true},{"appDetail":"躺平设计家,躺平设计家是D端设计师来查看设计作品，加入设计学院进行学习的一个app","appId":2019071000102,"appKey":"60043208","appName":"躺平设计家-ios","gmtCreate":1562731044000,"isAvailable":"y","motuAppId":"60043208@iphoneos","osType":1,"valid":true},{"appDetail":"躺平设计家是D端设计师来查看设计作品，加入设计学院进行学习的一个app。","appId":2019071000101,"appKey":"60043195","appName":"躺平设计家Android-android","appPackageName":"com.shejijia.android.designer","gmtCreate":1562730818000,"isAvailable":"y","motuAppId":"60043195@android","osType":2,"valid":true},{"appDetail":"iPhone版本PP体育app","appId":2019070900100,"appKey":"60043197","appName":"PPSports_iPhone-ios","gmtCreate":1562636667000,"isAvailable":"y","motuAppId":"60043197@iphoneos","osType":1,"valid":true},{"appDetail":"星缕测试应用orange","appId":2019070800099,"appKey":"60043206","appName":"星缕测试应用orange-winphone","gmtCreate":1562582872000,"isAvailable":"y","motuAppId":"60043206@","osType":3,"valid":true},{"appDetail":"东南亚购物软件","appId":2019070100091,"appKey":"60043179","appName":"youpik-ios","gmtCreate":1561950844000,"isAvailable":"y","motuAppId":"60043179@iphoneos","osType":1,"valid":true},{"appDetail":"TM海外Android客户端","appId":2019062800090,"appKey":"60043183","appName":"TM海外Android客户端-android","appPackageName":"com.taobao.tmoversea.android","gmtCreate":1561734159000,"isAvailable":"y","motuAppId":"60043183@android","osType":2,"valid":true},{"appDetail":"天猫海外app","appId":2019062800088,"appKey":"60043181","appName":"TMOverSea_iOS_daily-ios","gmtCreate":1561697334000,"isAvailable":"y","motuAppId":"60043181@iphoneos","osType":1,"valid":true},{"appDetail":"菜鸟乡村业务，溪鸟共配公司的业务流程作业安卓客户端","appId":2019062000037,"appKey":"60043151","appName":"xiniao_andriod-android","appPackageName":"com.xiniao.andriod.xnapp","gmtCreate":1561042088000,"isAvailable":"y","motuAppId":"60043151@android","osType":2,"valid":true},{"appDetail":"测试orange用","appId":2019060600024,"appKey":"60043082","appName":"WindowsQianniuTest1-android","appPackageName":"com.taobao.qianniu.test","gmtCreate":1559800602000,"isAvailable":"y","motuAppId":"60043082@android","osType":2,"valid":true},{"appDetail":"情兮","appId":2019060300004,"appKey":"60043042","appName":"情兮-android","appPackageName":"com.qingxi.android","gmtCreate":1559528661000,"isAvailable":"y","motuAppId":"60043042@android","osType":2,"valid":true},{"appDetail":"PP体育新App","appId":2019053100002,"appKey":"60043030","appName":"PPSports-ios","gmtCreate":1559289547000,"isAvailable":"y","motuAppId":"60043030@iphoneos","osType":1,"valid":true},{"appDetail":"会议吧顶部屏幕控制智能显示系统","appId":2019053000001,"appKey":"60043022","appName":"会议吧顶部屏-android","appPackageName":"com.aliwork.meetingbar.top","gmtCreate":1559219721000,"isAvailable":"y","motuAppId":"60043022@apad","osType":2,"valid":true},{"appDetail":"运行在银泰线下专柜移动POS机上，负责银行渠道的收银、银泰储值卡等功能的app","appId":2019052806645,"appKey":"60043021","appName":"喵支付-android","appPackageName":"com.intime.mjpay","gmtCreate":1559032237000,"isAvailable":"y","motuAppId":"60043021@android","osType":2,"valid":true},{"appDetail":"ihome设计师端","appId":2019052706643,"appKey":"60043020","appName":"ihome设计师端-android","appPackageName":"com.taobao.ihomed","gmtCreate":1558943503000,"isAvailable":"y","motuAppId":"60043020@android","osType":2,"valid":true},{"appDetail":"aliexpress 轻应用","appId":2019051506610,"appKey":"60042950","appName":"aeinstant-android","appPackageName":"com.alibaba.felin.sample","gmtCreate":1557910832000,"isAvailable":"y","motuAppId":"60042950@android","osType":2,"valid":true},{"appDetail":"飞猪商家版日常","appId":2019042906595,"appKey":"60042921","appName":"飞猪商家版日常-ios","gmtCreate":1556539992000,"isAvailable":"y","motuAppId":"60042921@iphoneos","osType":1,"valid":true},{"appDetail":"用于线下店专柜导购员使用。","appId":2019042206576,"appKey":"60042885","appName":"喵导购-android","appPackageName":"com.shoppingstreets.guide","basicAppKey":"60042885","gmtCreate":1555900938000,"isAvailable":"y","motuAppId":"60042885@apad","osType":2,"valid":true},{"appDetail":"设备信息安全网络通道sdk demo","appId":2019041806572,"appKey":"60042866","appName":"AliSecNetwork-android","appPackageName":"com.alibaba.networksdk","gmtCreate":1555570494000,"isAvailable":"y","motuAppId":"60042866@android","osType":2,"valid":true},{"appDetail":"菜鸟&卡行交接工具","appId":2019041506566,"appKey":"60042855","appName":"菜鸟交接工具-android","appPackageName":"com.cainiao.android.transition","gmtCreate":1555312174000,"isAvailable":"y","motuAppId":"60042855@android","osType":2,"valid":true},{"appDetail":"AliSecNetworkSdkDemo","appId":2019041106564,"appKey":"60042839","appName":"AliSecNetworkSdkDemo-ios","gmtCreate":1554980866000,"isAvailable":"y","motuAppId":"60042839@iphoneos","osType":1,"valid":true},{"appDetail":"lazada 东南亚社交电商","appId":2019041106560,"appKey":"60042835","appName":"ZAL-android","appPackageName":"com.lazadazal.android","basicAppKey":"60042835","gmtCreate":1554965076000,"isAvailable":"y","motuAppId":"60042835@android","osType":2,"valid":true},{"appDetail":"口碑POS收银","appId":2019041006558,"appKey":"60042834","appName":"口碑POS收银-android","appPackageName":"com.koubei.kbpos.cashier","gmtCreate":1554881821000,"isAvailable":"y","motuAppId":"60042834@apad","osType":2,"valid":true},{"appDetail":"黑土农村物流","appId":2019040806550,"appKey":"60042824","appName":"黑土农村物流-ios","basicAppKey":"60042824","gmtCreate":1554695990000,"isAvailable":"y","motuAppId":"60042824@iphoneos","osType":1,"valid":true},{"appDetail":"Youku华为日常白牌","appId":2019032606532,"appKey":"60042780","appName":"Youku华为日常白牌-android","appPackageName":"com.huawei.hwvplayer.youku","gmtCreate":1553590564000,"isAvailable":"y","motuAppId":"60042780@android","osType":2,"valid":true},{"appDetail":"村淘内容投放应用，支持天猫魔盒、触摸导购屏等","appId":2019031406517,"appKey":"60042711","appName":"村淘云货架-android","appPackageName":"com.alibaba.cun.play","gmtCreate":1552533532000,"isAvailable":"y","motuAppId":"60042711@apad","osType":2,"valid":true},{"appDetail":"优酷直播推出移动端开播平台，针对地方生活类直播、游戏直播、旅游直播、看剧直播等品类扩大用户市场，建立长尾直播内容池，通过移动端开播工具实现主播申请入驻、主播开播流程、主播收益商业化一站式完成直播链路，优酷建立自主直播工会，与其他直播平台作为竞争品牌，通过优酷直播工会建立一系列活动运营、市场策略运营，其核心价值通过直播产品业务，定义直播商业化营收，完成营收目标为核心价值。       平台化产品包含承接主播开播、申请入驻工会、直播分发、直播商业化收益产品，通过平台形成业内核心竞争力，通过主播参与主办活动，自身扩展主播、机构市场，通过平台对主播、机构双丰收，形成优酷直播平台的商业新生态。","appId":2019022606483,"appKey":"60042462","appName":"优酷直播助手-ios","gmtCreate":1551147773000,"isAvailable":"y","motuAppId":"60042462@iphoneos","osType":1,"valid":true},{"appDetail":"天猫会员店旨在 回归消费者、回归零售、回归品类，回归场景，建立以消费者为核心的场，以主打 精品心选，天猫自营，会员专享的电商业务。通过推出天猫会员店独立app，建立新日销运营阵地。","appId":2019022106477,"appKey":"60042452","appName":"天猫会员店_Android-android","appPackageName":"com.tmall.tmallvip","gmtCreate":1550727374000,"isAvailable":"y","motuAppId":"60042452@android","osType":2,"valid":true},{"appDetail":"交易猫androiddaily","appId":2019011806435,"appKey":"60042370","appName":"交易猫androiddaily-android","appPackageName":"com.jym.mall","gmtCreate":1547796732000,"isAvailable":"y","motuAppId":"60042370@android","osType":2,"valid":true},{"appDetail":"筷马收银台APP","appId":2018122606404,"appKey":"60042210","appName":"筷马-android","appPackageName":"com.alibaba.xpos.quickmart","basicAppKey":"60042210","gmtCreate":1545806878000,"isAvailable":"y","motuAppId":"60042210@apad","osType":2,"valid":true},{"appDetail":"UC头条开发版本，仅供内部开发使用","appId":2018122006398,"appKey":"60042160","appName":"UC头条开发版本-ios","gmtCreate":1545295290000,"isAvailable":"y","motuAppId":"60042160@iphoneos","osType":1,"valid":true},{"appDetail":"UC浏览器_企业版 是 UC 浏览器内部测试版本","appId":2018122006397,"appKey":"60042149","appName":"UC浏览器_企业版-ios","gmtCreate":1545294956000,"isAvailable":"y","motuAppId":"60042149@iphoneos","osType":1,"valid":true},{"appDetail":"饿了么商家版android","appId":2018121806393,"appKey":"60042153","appName":"饿了么商家版android-android","appPackageName":"me.ele.napos","gmtCreate":1545123179000,"isAvailable":"y","motuAppId":"60042153@android","osType":2,"valid":true},{"appDetail":"唱鸭","appId":2018121706392,"appKey":"60042120","appName":"唱鸭-android","appPackageName":"com.rockets.chang","gmtCreate":1545017201000,"isAvailable":"y","motuAppId":"60042120@android","osType":2,"valid":true},{"appDetail":"凿凿是以贡献知识，学习知识，分享知识为目的优质知识社区。","appId":2018121306391,"appKey":"60042091","appName":"凿凿-android","appPackageName":"com.shenma.zaozao","gmtCreate":1544683094000,"isAvailable":"y","motuAppId":"60042091@android","osType":2,"valid":true},{"appDetail":"测试 AliNN 模型发布","appId":2018120406383,"appKey":"60042040","appName":"纽特测试-android","appPackageName":"com.taobao.alinnkit.test","gmtCreate":1543908920000,"isAvailable":"y","motuAppId":"60042040@android","osType":2,"valid":true},{"appDetail":"盒马服务框架","appId":2018120306379,"appKey":"60042001","appName":"盒马服务框架-android","appPackageName":"com.wudaokou.rexos.hemaservice","gmtCreate":1543822583000,"isAvailable":"y","motuAppId":"60042001@android","osType":2,"valid":true},{"appDetail":"室内地图工具包含编辑、采集等功能。","appId":2018112806377,"appKey":"60041950","appName":"indoormaptools-android","appPackageName":"com.shoppingstreets.indoormap","gmtCreate":1543396057000,"isAvailable":"y","motuAppId":"60041950@apad","osType":2,"valid":true},{"appDetail":"阿里家居iPhone端APP","appId":2018111606351,"appKey":"60041791","appName":"iHome4iPhone-ios","gmtCreate":1542353793000,"isAvailable":"y","motuAppId":"60041791@iphoneos","osType":1,"valid":true},{"appDetail":"(日常)盒马数字化门店作业终端，支撑盒马门店的日常作业，人员管理调度，盒马出海项目海外门店标准作业终端","appId":2018110506348,"appKey":"60041656","appName":"NRF-android","appPackageName":"com.wudaokou.nrf","gmtCreate":1541401012000,"isAvailable":"y","motuAppId":"60041656@android","osType":2,"valid":true},{"appDetail":"用于人物跟踪，物体跟踪，商品分析","appId":2018103006347,"appKey":"60041651","appName":"智能FACE-ios","gmtCreate":1540880844000,"isAvailable":"y","motuAppId":"60041651@ipad","osType":1,"valid":true},{"appDetail":"利用视觉防损系统，通过APP通知安保人员，终止不良行为，降低盒马店的不明损耗率","appId":2018101106330,"appKey":"60041584","appName":"视觉防损-android","appPackageName":"com.wudaokou.videopunch","gmtCreate":1539247209000,"isAvailable":"y","motuAppId":"60041584@android","osType":2,"valid":true},{"appDetail":"魔放","appId":2018100806326,"appKey":"60041553","appName":"魔放-ios","gmtCreate":1538965058000,"isAvailable":"y","motuAppId":"60041553@iphoneos","osType":1,"valid":true},{"appDetail":"魔放","appId":2018100206324,"appKey":"60041564","appName":"魔放-android","appPackageName":"com.agtech.mofun","gmtCreate":1538451004000,"isAvailable":"y","motuAppId":"60041564@android","osType":2,"valid":true},{"appDetail":"mdlp demo","appId":2018092606319,"appKey":"60041551","appName":"mobilematser-ios","gmtCreate":1537960127000,"isAvailable":"y","motuAppId":"60041551@iphoneos","osType":1,"valid":true},{"appDetail":"天猫魔投产品是优酷集团针对手机和电视大小屏互动场景研发的新款硬件产品，硬件初始化过程需要在手机端进行配置网络，播放过程中在手机端进行遥控控制，该app就是天猫魔投的控制端。\n","appId":2018092506316,"appKey":"60041529","appName":"魔投-android","appPackageName":"com.taobao.motou","gmtCreate":1537857980000,"isAvailable":"y","motuAppId":"60041529@android","osType":2,"valid":true},{"appDetail":"天猫魔投产品是优酷集团针对手机和电视大小屏互动场景研发的新款硬件产品，硬件初始化过程需要在手机端进行配置网络，播放过程中在手机端进行遥控控制，该app就是天猫魔投的控制端。\n","appId":2018092006312,"appKey":"60041530","appName":"魔投-ios","gmtCreate":1537424824000,"isAvailable":"y","motuAppId":"60041530@iphoneos","osType":1,"valid":true},{"appDetail":"HomeAI客户端","appId":2018091706304,"appKey":"60041525","appName":"HomeAIAndroid-android","appPackageName":"com.taobao.homeai","gmtCreate":1537169372000,"isAvailable":"y","motuAppId":"60041525@android","osType":2,"valid":true},{"appDetail":"阿里拍卖Xpos收单APP","appId":2018091706303,"appKey":"60041518","appName":"阿里拍卖-android","appPackageName":"com.alibaba.xpos.auction","gmtCreate":1537168265000,"isAvailable":"y","motuAppId":"60041518@android","osType":2,"valid":true},{"appDetail":"菜鸟小盒","appId":2018082206288,"appKey":"60041410","appName":"菜鸟小盒-ios","gmtCreate":1534937115000,"isAvailable":"y","motuAppId":"60041410@iphoneos","osType":1,"valid":true},{"appDetail":"菜鸟小盒 Android","appId":2018082106286,"appKey":"60041397","appName":"cainiao_box_android-android","appPackageName":"com.cainiao.ued.box4android","gmtCreate":1534852635000,"isAvailable":"y","motuAppId":"60041397@android","osType":2,"valid":true},{"appDetail":"大麦小场馆票务验票、项目上架等功能","appId":2018080206220,"appKey":"60041291","appName":"大麦票务_Android-android","appPackageName":"cn.damai.ticketbusiness","basicAppKey":"60041291","gmtCreate":1533191344000,"isAvailable":"y","motuAppId":"60041291@android","osType":2,"valid":true},{"appDetail":"鉴权测试应用","appId":2018072406207,"appKey":"60041133","appName":"鹿尤测试应用-ios","gmtCreate":1532399617000,"isAvailable":"y","motuAppId":"60041133@iphoneos","osType":1,"valid":true},{"appDetail":"饿了么","appId":2018071806202,"appKey":"60041111","appName":"饿了么-android","appPackageName":"me.ele","gmtCreate":1531884406000,"isAvailable":"y","motuAppId":"60041111@android","osType":2,"valid":true},{"appDetail":"饿了么","appId":2018071806200,"appKey":"60041100","appName":"饿了么-ios","gmtCreate":1531882803000,"isAvailable":"y","motuAppId":"60041100@iphoneos","osType":1,"valid":true},{"appDetail":"优酷hd版本","appId":2018070206181,"appKey":"60040860","appName":"优酷_Android_Pad-android","appPackageName":"com.youku.pad","basicAppKey":"60040860","gmtCreate":1530538906000,"isAvailable":"y","motuAppId":"60040860@apad","osType":2,"valid":true},{"appDetail":"天猫进口客户端-鹿宝宝，承载天猫进口内容、商品、玩法","appId":2018062506174,"appKey":"60040783","appName":"tmallimport4iphone-ios","gmtCreate":1529891895000,"isAvailable":"y","motuAppId":"60040783@iphoneos","osType":1,"valid":true},{"appDetail":"Daraz","appId":2018062206170,"appKey":"60040780","appName":"Daraz-ios","gmtCreate":1529637290000,"isAvailable":"y","motuAppId":"60040780@iphoneos","osType":1,"valid":true},{"appDetail":"demo+ client for ios \n\nrefer: https://udemo.alibaba-inc.com/categories/9491","appId":2018061306163,"appKey":"60040710","appName":"DemoPlus-ios","gmtCreate":1528878879000,"isAvailable":"y","motuAppId":"60040710@iphoneos","osType":1,"valid":true},{"appDetail":"UC中东短视频App","appId":2018061106158,"appKey":"60040690","appName":"Vaka-android","appPackageName":"com.alibaba.vaka.video","gmtCreate":1528696201000,"isAvailable":"y","motuAppId":"60040690@android","osType":2,"valid":true},{"appDetail":"菜鸟橙运App_Android","appId":2018060806155,"appKey":"********","appName":"菜鸟运输App_Android-android","appPackageName":"com.cainiao.android.zyb","gmtCreate":152**********,"isAvailable":"y","motuAppId":"********@android","osType":2,"valid":true},{"appDetail":"TOP淘宝卖家移动工作台","appId":*************,"appKey":"********","appName":"TOP淘宝卖家移动工作台-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@iphoneos","osType":1,"valid":true},{"appDetail":"提供TV端的手机淘宝账号登陆，注册与授权。","appId":*************,"appKey":"********","appName":"天猫魔盒账号客户端-yunos","appPackageName":"com.yunos.account","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@android","osType":4,"valid":true},{"appDetail":"五道口飞鱼应用司机端","appId":*************,"appKey":"********","appName":"飞鱼驿丁宝-ios","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@iphoneos","osType":1,"valid":true},{"appDetail":"五道口飞鱼应用司机端","appId":*************,"appKey":"********","appName":"飞鱼驿丁宝-android","appPackageName":"com.wudaokou.flyingfishdriver","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@android","osType":2,"valid":true},{"appDetail":"阿里游戏即时小游戏APP，支持多人在线一起玩游戏","appId":*************,"appKey":"********","appName":"即时小游戏_Android-android","appPackageName":"com.aligames.wegame","gmtCreate":*************,"isAvailable":"y","motuAppId":"********@android","osType":2,"valid":true},{"appDetail":"Demo+Android客户端","appId":*************,"appKey":"********","appName":"DemoPlus-android","appPackageName":"demoplustest","gmtCreate":1526263177000,"isAvailable":"y","motuAppId":"********@android","osType":2,"valid":true},{"appDetail":"小视频Android日常","appId":2018050706115,"appKey":"60040525","appName":"小视频Android日常-android","appPackageName":"com.youku.shortvideo","gmtCreate":1525679510000,"isAvailable":"y","motuAppId":"60040525@android","osType":2,"valid":true},{"appDetail":"电流小视频iPhone日常","appId":2018050706114,"appKey":"60040524","appName":"小视频iPhone日常-ios","gmtCreate":1525672539000,"isAvailable":"y","motuAppId":"60040524@iphoneos","osType":1,"valid":true},{"appDetail":"盒马帮门店使用的收银POS","appId":2018041606109,"appKey":"60040387","appName":"盒马帮POS-android","appPackageName":"com.wudaokou.bang","gmtCreate":1523868961000,"isAvailable":"y","motuAppId":"60040387@android","osType":2,"valid":true},{"appDetail":"淘小宝","appId":2018040206081,"appKey":"60040331","appName":"淘小宝-android","appPackageName":"com.taobao.taokids","gmtCreate":1522655682000,"isAvailable":"y","motuAppId":"60040331@android","osType":2,"valid":true},{"appDetail":"菜鸟驿站智能取件App","appId":2018032906078,"appKey":"60040322","appName":"菜鸟驿站智能取件App-android","appPackageName":"com.cainiao.stationapp","gmtCreate":1522312662000,"isAvailable":"y","motuAppId":"60040322@apad","osType":2,"valid":true},{"appDetail":"运行在线下专柜移动POS机上，负责收银、导购等功能的app","appId":2018032906076,"appKey":"60040315","appName":"喵街云pos-android","appPackageName":"com.nointime.mjpos","gmtCreate":1522304531000,"isAvailable":"y","motuAppId":"60040315@android","osType":2,"valid":true},{"appDetail":"XXX4iPhone","appId":2018032606070,"appKey":"60040311","appName":"XXX4iPhone-ios","gmtCreate":1522046498000,"isAvailable":"y","motuAppId":"60040311@iphoneos","osType":1,"valid":true},{"appDetail":"乐动力小青","appId":2018032206064,"appKey":"60040306","appName":"乐动力小青-ios","gmtCreate":1521717285000,"isAvailable":"y","motuAppId":"60040306@iphoneos","osType":1,"valid":true},{"appDetail":"乐动力小白","appId":2018032006028,"appKey":"60040292","appName":"乐动力小白-android","appPackageName":"cn.ledongli.ldl","gmtCreate":1521532971000,"isAvailable":"y","motuAppId":"60040292@android","osType":2,"valid":true},{"appDetail":"线下导购员导购iPad","appId":2018031606027,"appKey":"60040303","appName":"TMMJ4iPad-ios","gmtCreate":1521191064000,"isAvailable":"y","motuAppId":"60040303@ipad","osType":1,"valid":true},{"appDetail":"优酷Passport电视端Demo，用于测试账号登录及管理。。","appId":2018031406026,"appKey":"60040302","appName":"PassportOTTDemo-android","appPackageName":"com.youku.passport.demo","gmtCreate":1521008715000,"isAvailable":"y","motuAppId":"60040302@android","osType":2,"valid":true},{"appDetail":"运行在银泰线下专柜移动POS机上，负责收银、导购等功能的app","appId":2018031306015,"appKey":"60040300","appName":"云POS银泰城版-android","appPackageName":"com.intime.mjpos.intimecity","gmtCreate":1520922619000,"isAvailable":"y","motuAppId":"60040300@android","osType":2,"valid":true},{"appDetail":"手机上运行H5小游戏","appId":2018030806002,"appKey":"60040271","appName":"即时小游戏iOS客户端-ios","gmtCreate":1520488281000,"isAvailable":"y","motuAppId":"60040271@iphoneos","osType":1,"valid":true},{"appDetail":"零售通移动工作台","appId":2018030706001,"appKey":"60040260","appName":"零售通移动工作台-android","appPackageName":"com.alibaba.lst.operateplatform","gmtCreate":1520423730000,"isAvailable":"y","motuAppId":"60040260@android","osType":2,"valid":true},{"appDetail":"Beyond电竞","appId":2018020805979,"appKey":"60040132","appName":"阿里电竞_Android版-android","appPackageName":"com.alisports.wesg","gmtCreate":1518054721000,"isAvailable":"y","motuAppId":"60040132@android","osType":2,"valid":true},{"appDetail":"天猫云镜，安卓智能后视镜数据采集","appId":2018012905956,"appKey":"60040030","appName":"天猫云镜-android","appPackageName":"com.alibaba.ailabs.cloudmirror","gmtCreate":1517211429000,"isAvailable":"y","motuAppId":"60040030@android","osType":2,"valid":true},{"appDetail":"极光线下端android版daily环境","appId":2018011605932,"appKey":"60039900","appName":"极光线下端android版-android","appPackageName":"com.qianniu.retail","gmtCreate":1516093983000,"isAvailable":"y","motuAppId":"60039900@android","osType":2,"valid":true},{"appDetail":"lazada_seller","appId":2018010505919,"appKey":"60039780","appName":"lazadasellercenter-android","appPackageName":"com.sc.lazada","gmtCreate":1515129692000,"isAvailable":"y","motuAppId":"60039780@android","osType":2,"valid":true},{"appDetail":"该app将天猫精灵的语音交互能力以app的形式赋予基于android平台的硬件设备","appId":2017121105900,"appKey":"60039620","appName":"AliGenieSDK_Android-android","appPackageName":"com.alibaba.ailabs.genisdk","gmtCreate":1512994251000,"isAvailable":"y","motuAppId":"60039620@android","osType":2,"valid":true},{"appDetail":"盒马、淘鲜达POS秤项目","appId":2017120805897,"appKey":"60039609","appName":"盒马POS秤-android","appPackageName":"com.wudaokou.balancepos","gmtCreate":1512718465000,"isAvailable":"y","motuAppId":"60039609@apad","osType":2,"valid":true},{"appDetail":"银泰网app是银泰百货全域卖货的一个重要的线上销售域，是将银泰百货各门店专柜的精选商品，以门店好东西不贵的实惠价格，面向全国消费者一个销售终端。银泰会员在银泰网上可享有银泰会员所有的权益，同时也支持银泰365会员。","appId":2017120705896,"appKey":"60039608","appName":"银泰网-ios","gmtCreate":1512618255000,"isAvailable":"y","motuAppId":"60039608@iphoneos","osType":1,"valid":true},{"appDetail":"优酷 Passport Demo","appId":2017120605893,"appKey":"60039595","appName":"YoukuLoginDemo-ios","gmtCreate":1512530199000,"isAvailable":"y","motuAppId":"60039595@iphoneos","osType":1,"valid":true},{"appDetail":"淘宝Lite版","appId":2017120105872,"appKey":"60039601","appName":"TBLiteAndroid-android","appPackageName":"com.taobao.litetao","gmtCreate":1512120245000,"isAvailable":"y","motuAppId":"60039601@android","osType":2,"valid":true},{"appDetail":"LTao4iPhone","appId":2017120105871,"appKey":"60039592","appName":"LTao4iPhone-ios","gmtCreate":1512117115000,"isAvailable":"y","motuAppId":"60039592@iphoneos","osType":1,"valid":true},{"appDetail":"fsgsg, 测试metaq,add to fregata","appId":2017112305832,"appKey":"60039542","appName":"兰茵的应用33-ios","basicAppKey":"60039542","gmtCreate":1511419565000,"isAvailable":"y","motuAppId":"60039542@iphoneos","osType":1,"valid":true},{"appDetail":"lazada","appId":2017111705817,"appKey":"60039502","appName":"lazada-ios","gmtCreate":1510917745000,"isAvailable":"y","motuAppId":"60039502@iphoneos","osType":1,"valid":true},{"appDetail":"盒马门店的手持收银POS","appId":2017110605806,"appKey":"60039421","appName":"盒马无线POS-android","appPackageName":"com.wudaokou.mobpos","gmtCreate":1509970002000,"isAvailable":"y","motuAppId":"60039421@android","osType":2,"valid":true},{"appDetail":"淘鲜达自助收银APP，自助收银","appId":2017092905774,"appKey":"60039163","appName":"淘鲜达自助收银APP-android","appPackageName":"com.wudaokou.txdss","gmtCreate":1506658572000,"isAvailable":"y","motuAppId":"60039163@apad","osType":2,"valid":true},{"appDetail":"大麦PDA设备，用于提供验票服务","appId":2017092505753,"appKey":"60039110","appName":"大麦PDA-android","appPackageName":"cn.damai.ticket","gmtCreate":1506331508000,"isAvailable":"y","motuAppId":"60039110@android","osType":2,"valid":true},{"appDetail":"lazada 东南亚电商","appId":2017092405749,"appKey":"60039086","appName":"lazada-android","appPackageName":"com.lazada.android","gmtCreate":1506223036000,"isAvailable":"y","motuAppId":"60039086@android","osType":2,"valid":true},{"appDetail":"保密","appId":2017091205532,"appKey":"60038884","appName":"菜鸟自提柜-android","appPackageName":"com.cainiao.cabinetapp","gmtCreate":1505220178000,"isAvailable":"y","motuAppId":"60038884@apad","osType":2,"valid":true},{"appDetail":"店内餐饮设备上的应用","appId":2017090705528,"appKey":"60038800","appName":"盒马餐饮数字化-android","appPackageName":"com.wudaokou.catering","gmtCreate":1504754001000,"isAvailable":"y","motuAppId":"60038800@apad","osType":2,"valid":true},{"appDetail":"AMS-租赁市场-用于菜鸟新能源车租赁平台","appId":2017090405525,"appKey":"60038766","appName":"AMS_MARKET_IOS-ios","gmtCreate":1504524393000,"isAvailable":"y","motuAppId":"60038766@iphoneos","osType":1,"valid":true},{"appDetail":"AMS-租赁市场-用于菜鸟新能源车租赁平台","appId":2017090405524,"appKey":"60038767","appName":"AMS_MARKET_ANDROID-android","appPackageName":"com.cainiao.ace.market.android","gmtCreate":1504524314000,"isAvailable":"y","motuAppId":"60038767@android","osType":2,"valid":true},{"appDetail":"新能源车司机App","appId":2017082505511,"appKey":"60038660","appName":"菜鸟EMS司机APP-android","appPackageName":"com.cainiao.ems.driverapp","gmtCreate":1503652591000,"isAvailable":"y","motuAppId":"60038660@android","osType":2,"valid":true},{"appDetail":"优酷PassportSDKDemo","appId":2017082405508,"appKey":"60038655","appName":"优酷PassportSDKDemo-android","appPackageName":"com.youku.usercenter.passport.demo","gmtCreate":1503575709000,"isAvailable":"y","motuAppId":"60038655@android","osType":2,"valid":true},{"appDetail":"demo","appId":2017082105501,"appKey":"60038586","appName":"村淘门店_POS-android","appPackageName":"com.alibaba.cun.pos","gmtCreate":1503293900000,"isAvailable":"y","motuAppId":"60038586@apad","osType":2,"valid":true},{"appDetail":"租赁市场app日常环境","appId":2017080205477,"appKey":"60038373","appName":"AMS_MARKET_APP-android","appPackageName":"com.cainiao.ams.market.android","gmtCreate":1501665380000,"isAvailable":"y","motuAppId":"60038373@android","osType":2,"valid":true},{"appDetail":"tmallBAJ","appId":2017080105475,"appKey":"60038356","appName":"百安居未来店-android","appPackageName":"com.tmall.baj","gmtCreate":1501577552000,"isAvailable":"y","motuAppId":"60038356@apad","osType":2,"valid":true},{"appDetail":"菜鸟轨迹","appId":2017071005461,"appKey":"60037858","appName":"菜鸟无线云_Android-android","appPackageName":"com.cainiao.wirelesscloud.android.platform","gmtCreate":1499684280000,"isAvailable":"y","motuAppId":"60037858@android","osType":2,"valid":true},{"appDetail":"智村宝平台是菜鸟乡村物流下为县域物流合作伙伴，提供的智能服务平台，整合县域物流资源，提升县域物流服务。","appId":2017062905453,"appKey":"60037531","appName":"智村宝-ios","gmtCreate":1498726433000,"isAvailable":"y","motuAppId":"60037531@iphoneos","osType":1,"valid":true},{"appDetail":"菜鸟工作台应用","appId":2017062805450,"appKey":"60037489","appName":"菜鸟工作台-android","appPackageName":"com.cainiao.one","gmtCreate":1498638953000,"isAvailable":"y","motuAppId":"60037489@android","osType":2,"valid":true},{"appDetail":"掌柜工作台","appId":2017062005425,"appKey":"60037095","appName":"掌柜工作台-android","appPackageName":"com.alibaba.cun.assistant","basicAppKey":"60037095","gmtCreate":1497925638000,"isAvailable":"y","motuAppId":"60037095@android","osType":2,"valid":true},{"appDetail":"掌柜工作台_iOS","appId":2017062005424,"appKey":"60037125","appName":"掌柜工作台_iOS-ios","gmtCreate":1497924823000,"isAvailable":"y","motuAppId":"60037125@iphoneos","osType":1,"valid":true},{"appDetail":"盒马设备管理app","appId":2017061205418,"appKey":"60036741","appName":"盒马设备管家-android","appPackageName":"com.wudaokou.hdm","gmtCreate":1497270013000,"isAvailable":"y","motuAppId":"60036741@android","osType":2,"valid":true},{"appDetail":"AMS_Android","appId":2017060705413,"appKey":"60036435","appName":"AMS-android","appPackageName":"com.cainiao.ace.android","gmtCreate":1496824052000,"isAvailable":"y","motuAppId":"60036435@android","osType":2,"valid":true},{"appDetail":"买家秀项目 秀秀 APP","appId":2017060505410,"appKey":"60036424","appName":"买家秀-ios","gmtCreate":1496633438000,"isAvailable":"y","motuAppId":"60036424@iphoneos","osType":1,"valid":true},{"appDetail":"eee","appId":2017060105409,"appKey":"60036281","appName":"兰茵的accs应用-ios","gmtCreate":1496286182000,"isAvailable":"y","motuAppId":"60036281@iphoneos","osType":1,"valid":true},{"appDetail":"盒马门店自助结单pos","appId":2017053105408,"appKey":"60036246","appName":"自助POS-android","appPackageName":"com.wudaokou.sspos","gmtCreate":1496225061000,"isAvailable":"y","motuAppId":"60036246@apad","osType":2,"valid":true},{"appDetail":"测试应用版本","appId":2017050303550,"appKey":"60035012","appName":"兰茵的应用44-android","appPackageName":"com.taobao.lanyin44","basicAppKey":"60035012","gmtCreate":1493780475000,"isAvailable":"y","motuAppId":"60035012@android","osType":2,"valid":true},{"appDetail":"菜鸟盒子","appId":2017042003528,"appKey":"60034539","appName":"Apanti_App-android","appPackageName":"com.cainiao.apanti.android.mirror","gmtCreate":1492661355000,"isAvailable":"y","motuAppId":"60034539@android","osType":2,"valid":true},{"appDetail":"优酷iPad日常","appId":2017041703524,"appKey":"60034473","appName":"优酷iPad日常-ios","gmtCreate":1492410065000,"isAvailable":"y","motuAppId":"60034473@ipad","osType":1,"valid":true},{"appDetail":"优酷iPhone日常","appId":2017041703523,"appKey":"60034474","appName":"优酷iPhone日常-ios","gmtCreate":1492409951000,"isAvailable":"y","motuAppId":"60034474@iphoneos","osType":1,"valid":true},{"appDetail":"pos系统","appId":2017041002009,"appKey":"60034407","appName":"hmpos-android","appPackageName":"com.wudaokou.hmpos","gmtCreate":1491813033000,"isAvailable":"y","motuAppId":"60034407@apad","osType":2,"valid":true},{"appDetail":"淘票票专业版Android","appId":2017031400504,"appKey":"60033513","appName":"淘票票专业版Android-android","appPackageName":"com.alipictures.moviepro.dev","gmtCreate":1486972813000,"isAvailable":"y","motuAppId":"60033513@android","osType":2,"valid":true},{"appDetail":"淘票票专业版iOS","appId":2017031400503,"appKey":"60033514","appName":"淘票票专业版iOS-ios","gmtCreate":1486972872000,"isAvailable":"y","motuAppId":"60033514@iphoneos","osType":1,"valid":true},{"appDetail":"盒马pos机专用app","appId":2017031400478,"appKey":"60033106","appName":"盒马pos-android","appPackageName":"com.wudaokou.pos","gmtCreate":1482908377000,"isAvailable":"y","motuAppId":"60033106@android","osType":2,"valid":true},{"appDetail":"众配宝iPhone版","appId":2017031400421,"appKey":"60031191","appName":"众配宝iPhone版-ios","gmtCreate":1471246729000,"isAvailable":"y","motuAppId":"60031191@iphoneos","osType":1,"valid":true},{"appDetail":"干线运输司机端iPhone版","appId":2017031400419,"appKey":"60031182","appName":"干线运输司机端iPhone版-ios","gmtCreate":1471246981000,"isAvailable":"y","motuAppId":"60031182@iphoneos","osType":1,"valid":true},{"appDetail":"智能音箱","appId":2017031400396,"appKey":"60030793","appName":"智能音箱-ios","gmtCreate":1469011445000,"isAvailable":"y","motuAppId":"60030793@iphoneos","osType":1,"valid":true},{"appDetail":"支持多个version测试","appId":2017052405374,"appKey":"100007","appName":"支持多个version测试-android","gmtCreate":1357352723000,"isAvailable":"y","motuAppId":"100007@android","osType":2,"valid":true},{"appDetail":"淘宝旅行-daily","appId":2017052405196,"appKey":"611268","appName":"淘宝旅行-daily-android","appPackageName":"com.taobao.trip","gmtCreate":1381918745000,"isAvailable":"y","motuAppId":"611268@android","osType":2,"valid":true},{"appDetail":"淘宝大学test-ios","appId":2017052405118,"appKey":"698484","appName":"淘宝大学test-ios-ios","appPackageName":"com.taobao.taoweike","gmtCreate":1402993131000,"isAvailable":"y","motuAppId":"698484@iphoneos","osType":1,"valid":true},{"appDetail":"Aliexpress_Test日常环境","appId":2017052404982,"appKey":"60028268","appName":"Aliexpress_Test-android","appPackageName":"com.alibaba.aliexpresshd","gmtCreate":1453789771000,"isAvailable":"y","motuAppId":"60028268@android","osType":2,"valid":true},{"appDetail":"ZPB","appId":2017041302463,"appKey":"60029672","appName":"ZPB-android","appPackageName":"com.cainiao.android.zpb","gmtCreate":1468928407000,"isAvailable":"y","motuAppId":"60029672@android","osType":2,"valid":true},{"appDetail":"cainiao_open_android","appId":2017041302434,"appKey":"60030421","appName":"cainiao_open_android-android","appPackageName":"com.cainiao.CNCTakingOrderExample","gmtCreate":1467013419000,"isAvailable":"y","motuAppId":"60030421@android","osType":2,"valid":true},{"appDetail":"淘宝大学android日常","appId":2017041302414,"appKey":"60029260","appName":"tdvideo_android-android","appPackageName":"com.taobao.tdvideo","gmtCreate":1463741716000,"isAvailable":"y","motuAppId":"60029260@android","osType":2,"valid":true},{"appDetail":"htao-android","appId":2017041302085,"appKey":"60026808","appName":"taobao_globle_android-android","appPackageName":"com.taobao.htao.android","gmtCreate":1446195766000,"isAvailable":"y","motuAppId":"60026808@android","osType":2,"valid":true}],"success":true}

}
