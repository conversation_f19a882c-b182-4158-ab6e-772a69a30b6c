//const tempPath = "//dev.g.alicdn.com/ali-wireless-midplatform-frontend/orangeview/0.9.8/";
const tempPath = "//g.alicdn.com/ali-wireless-midplatform-frontend/orangeview/0.9.9/";


export default {
  // 应用类型
  appType: 'console',
  // 部署模式
  deployMode: 'assets',
  favicon: 'http://g-assets.daily.taobao.net/ali-wireless-midplatform-frontend/fregataview/1.0.5/static/emas-logo.dda1df01.svg',
  publicPath: tempPath,
  runtimePublicPath: false,
  script: [`
    window._DATA_ = {
    userId: '39753',
    userName: '兰茵',
    emailPrefix: 'lanyin.smz',
    isAdmin: 'false',
    tips: '预案有命中规则,务必务必确保可生效。11.10 23:30-11.11 01:30 Orange熔断发布，详见 https://yuque.antfin-inc.com/wireless-orange/wiki/sgi7uy 【问题咨询请钉钉搜索答疑群号：11713696】'
    }
  `],
  history: 'hash',
  outputPath: './build',
  // outputPath: './overrides/g.alicdn.com/ali-wireless-midplatform-frontend/orangeview/0.5.7',
  // outputPath: './overrides/g-assets.daily.taobao.net/ali-wireless-midplatform-frontend/orangeview/0.2.4',
  build: {
    assetsRoot: tempPath,
  },
  routes: [{
    path: '/',
    component: '../layouts/App',
    indexRoute: {component: '../pages/home/<USER>'},
    routes: [
      // dashboard
      {path: '/', redirect: '/index'},
      {path: '/index', component: '../pages/home/<USER>'},
      {
        path: 'namespace',
        name: 'root',
        routes: [
          {
            path: 'namespace',
            name: 'namespace',
            routes: [
              {path: 'list', name: 'namespace_list', component: '../pages/namespace/list'},
              {path: 'me', name: 'namespace_me', component: '../pages/namespace/me'},
              {path: 'detail/:version', name: 'detail_version', component: '../pages/namespace/detail'},
              {path: 'revise/:namespaceId', name: 'reviseNamespace', component: '../pages/namespace/revise'},

            ]
          },
          {
            path: 'version',
            name: 'version',
            routes: [
              {path: 'list', name: 'namespace_list', component: '../pages/version/list'},
              {path: 'detail/:version', name: 'namespace_me', component: '../pages/version/detail'},
              {path: 'debug/:version', name: 'namespace_me', component: '../pages/version/debug'},
            ]
          }
        ],
      }, {
        path: 'setting',
        name: 'setting',
        routes: [
          {
            path: 'business',
            name: 'business',
            routes: [
              {path: 'list', name: 'businessList', component: '../pages/business/list'},
              {path: 'detail/:name', name: 'businessDetail', component: '../pages/business/detail'},
            ]
          },{
            path: 'app',
            name: 'app',
            routes: [
              {path: 'list', name: 'appList', component: '../pages/app/list'},
              {path: 'detail/:appKey', name: 'appDetail', component: '../pages/app/detail'},
            ]
          }
        ]
      }, {
        path: 'report',
        name: 'report',
        routes: [
          {path: 'dashboard/dashboard', name: 'dashboard', component: '../pages/report/dashboard'},
          {path: 'trace/publish', name: 'namespace_me', component: '../pages/report/trace/publish'},
          {path: 'namespace/namespace', name: 'namespace_me', component: '../pages/report/namespace'},
        ]
      }
    ]
  }],
  proxy: {
    "dev": {
      "api/": {
        "target": "http://orange-console-daily.alibaba.net:7001",
        "headers": {
          "Host": "orange-console-daily.alibaba.net", // 有的后端服务器可能有一些安全限制的可以设置下 host 的 header 来规避
        }
      }
    },
    "test": {
      "api/": {
        "target": "https://orange-console-daily.alibaba.net"
      }
    },
    "pre": {
      "api/": {
        "target": "https://orange-console-pre.alibaba-inc.com"
      }
    },
    "online": {
      "api/": {
        "target": "https://orange-console.alibaba-inc.com"
      }
    }
  },
  theme: {
    '@primary-color': '#3599ff',
    '@link-color': '#3599ff',
    '@border-radius-base': '2px',
    '@font-size-base': '13px',
    '@line-height-base': '1.4',
    '@btn-height-base': '28px',
  },
};
